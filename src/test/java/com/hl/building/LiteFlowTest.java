//package com.hl.building;
//
//import com.alibaba.fastjson2.JSONObject;
//import com.yomahub.liteflow.core.FlowExecutor;
//import com.yomahub.liteflow.flow.LiteflowResponse;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import javax.annotation.Resource;
//
//@SpringBootTest
//public class LiteFlowTest {
//
//
//    @Resource
//    private FlowExecutor flowExecutor;
//
//    @Test
//     void test() throws Exception {
//
//        LiteflowResponse liteflowResponse = flowExecutor.execute2Resp("chain1", "person",JSONObject.class);
//        System.out.println(liteflowResponse.isSuccess());
//        JSONObject contextBean = liteflowResponse.getContextBean(JSONObject.class);
//        System.out.println(contextBean);
//    }
//}
