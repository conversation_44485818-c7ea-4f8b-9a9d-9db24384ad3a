package com.hl.building;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson2.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class Excel {
    public static void main(String[] args) {
        ExcelReader reader = ExcelUtil.getReader("C:\\Users\\<USER>\\Desktop\\重点楼宇清单.xlsx");
        List<List<Object>> read = reader.read();

        List<JSONObject> list = new ArrayList<>();

        for (int i = 2; i < read.size(); i++) {
            List<Object> objects = read.get(i);
            String org = (String) objects.get(1);
            String buildName = (String) objects.get(2);
            String type = (String) objects.get(3);
            String address = (String) objects.get(4);
            JSONObject object = new JSONObject();

            object.put("org", org);
            object.put("buildName", buildName);
            object.put("type", type);
            object.put("address", address);
            list.add(object);
        }

        ExcelWriter writer = ExcelUtil.getWriter("C:\\Users\\<USER>\\Desktop\\楼宇.xlsx");
        writer.write(list);
        writer.close();

//        for (List<Object> objects : read) {
//            System.out.println(objects);
//        }
    }
}
