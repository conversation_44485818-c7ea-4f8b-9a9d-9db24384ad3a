package com.hl.building;

import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.analysis.util.SqlLiteUtil;
import com.hl.building.domain.WjCompanyInfo;
import com.hl.building.service.WjCompanyInfoService;
import com.hl.building.service.impl.WjCompanyInfoServiceImpl;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.List;

@SpringBootTest
public class CompanyInfoTest {


    @Resource
    private WjCompanyInfoService wjCompanyInfoService;


    @Test
    public void test() throws SQLException {
        DataSource dataSource = SqlLiteUtil.getDataSource("conf/sql-data/zww_data.db");
        List<Entity> query = DbUtil.use(dataSource).query("select * from wj_corporate_base_info");

        for (Entity entity : query) {
            String tyshxydm = entity.getStr("tyshxydm");
            WjCompanyInfo wjCompanyInfo = new WjCompanyInfo();
            wjCompanyInfo.setCompanyName(entity.getStr("jgmc"));
            wjCompanyInfo.setCompanyAddress(entity.getStr("zcdz"));
            wjCompanyInfo.setZczb(entity.getStr("reg_capi"));
            wjCompanyInfo.setZcrq(entity.getStr("start_date"));
            wjCompanyInfo.setYxqx(entity.getStr("fare_term_end"));
            wjCompanyInfo.setJyfw(entity.getStr("fare_scope"));
            wjCompanyInfo.setZt(entity.getStr("corp_status"));

            WjCompanyInfo one = wjCompanyInfoService.getOne(Wrappers.<WjCompanyInfo>lambdaQuery()
                    .eq(WjCompanyInfo::getCompanyCode, tyshxydm));
            if (one != null) {
                System.out.println("存在");
                wjCompanyInfo.setCompanyId(one.getCompanyId());
            }
            wjCompanyInfoService.saveOrUpdate(wjCompanyInfo);
        }
    }
}
