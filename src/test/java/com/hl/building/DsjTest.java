package com.hl.building;

import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson2.JSONObject;
import com.hl.building.domain.WjCompanyInfo;
import com.hl.building.domain.WjPersonInfo;
import com.hl.building.service.WjCompanyInfoService;
import com.hl.building.service.WjPersonInfoService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class DsjTest {

    @Resource
    private WjPersonInfoService wjPersonInfoService;

    @Resource
    private WjCompanyInfoService wjCompanyInfoService;

    @Test
    void test() {
        List<WjPersonInfo> list = wjPersonInfoService.list();

        for (WjPersonInfo personInfo : list) {
            String idCard = personInfo.getIdCard();
            String name = personInfo.getName();

            JSONObject bzxrxx = DsjApiUtil.getRKSXBZXRXX(idCard, name);
        }
    }

    @Test
    void test2() {
//        List<WjCompanyInfo> list = wjCompanyInfoService.list(Wrappers.<WjCompanyInfo>lambdaQuery()
//                .last(" limit 10"));

        List<WjCompanyInfo> list = wjCompanyInfoService.list();
        List<JSONObject> qsgqczdjxxList = new ArrayList<>();
        for (WjCompanyInfo companyInfo : list) {
            String companyCode = companyInfo.getCompanyCode();
            String companyName = companyInfo.getCompanyName();
            JSONObject qsgqczdjxx = DsjApiUtil.getCBDWXX(companyCode, companyName);
            List<JSONObject> objectList = qsgqczdjxx.getList("value", JSONObject.class);
            if (objectList != null && !objectList.isEmpty()){

                qsgqczdjxxList.addAll(objectList);
            }
        }

        ExcelUtil.getWriter("C:\\Users\\<USER>\\Desktop\\公司参保信息.xlsx")
                .write(qsgqczdjxxList)
                .close();

    }
}
