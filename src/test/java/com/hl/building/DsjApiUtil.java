package com.hl.building;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class DsjApiUtil {

    private static volatile String accessToken;

    static {
        System.out.println("类加载调用刷新token");
        accessToken = getToken();
    }


    public static String getToken() {
        System.out.println("刷新token");
//        HttpResponse response = HttpUtil.createGet("http://172.18.66.90:9090/auth/accesstoken/create")
        HttpResponse response = HttpUtil.createGet("http://127.0.0.1:9090/auth/accesstoken/create")
                .form("appId", "saca_OrBcEF3Zy0")
                .form("appSecret", "qqcaN2Ux295oXPSr138z9Tafgi4KL9bn")
                .timeout(1000 * 30)
                .execute();
        String body = response.body();
        JSONObject jsonObject = JSONObject.parseObject(body);
        return jsonObject.getString("access_token");

    }


    private static Map<String, String> getRequestHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("authoritytype", "2");
        headers.put("elementsVersion", "1.00");
        headers.put("heartbeat", "1");
        headers.put("testmarker", "0");
        headers.put("access_token", accessToken);
        return headers;
    }

    private static JSONObject parseResult(String body) {
        JSONObject jsonObject = JSONObject.parseObject(body);
        log.info("返回值:{}", jsonObject.toJSONString());
        if (jsonObject.containsKey("value")) {
            return jsonObject;
        } else {
            accessToken = getToken();
        }
        return jsonObject;
    }


    /**
     * 武进区_大数据管理局_人口_职工社保缴纳信息(回流)V1.00
     *
     * @param JFQB   缴费期别
     * @param GMSFHM 身份证
     * @param XM     姓名
     * @return 返回值
     * {"value":[{"DJH":"91320412MA22417444","TZNY":"202410","SJJE":"1170.96","D_OPERATION":"更新","GRSJ":"390.32","D_TIMESTAMP":"2025-05-25 03:01:36.0","DWJFBL":"0.16","DWSJ":"780.64","YJJE":"1170.96","JS":"4879","XZLX":"企业职工养老保险","LYJG":"人社局","LYB":"dest_rsj_grjfmx_zg","YWID":"330381199010043613202410","XM":"袁成业","GRYJ":"390.32","GMSFHM":"330381199010043613","GZDWMC":"常州奥视科技有限公司","JFQB":"202410","GRJFBL":"0.08","BSZHLX":"统一社会信用代码","AGENCY_KEY":"6443890d-f690-40f1-bdf6-a8e0d30735ae","DWYJ":"780.64","COUNTY":"武进区"},{"DJH":"91320412MA22417444","TZNY":"202410","SJJE":"48.8","D_OPERATION":"更新","GRSJ":"24.4","D_TIMESTAMP":"2025-05-25 03:01:36.0","DWJFBL":"0.005","DWSJ":"24.4","YJJE":"48.8","JS":"4879","XZLX":"失业保险","LYJG":"人社局","LYB":"dest_rsj_grjfmx_zg","YWID":"330381199010043613202410","XM":"袁成业","GRYJ":"24.4","GMSFHM":"330381199010043613","GZDWMC":"常州奥视科技有限公司","JFQB":"202410","GRJFBL":"0.005","BSZHLX":"统一社会信用代码","AGENCY_KEY":"7a1545a8-4d66-4298-be97-f7d3609942e7","DWYJ":"24.4","COUNTY":"武进区"},{"DJH":"91320412MA22417444","TZNY":"202410","SJJE":"9.76","D_OPERATION":"更新","GRSJ":"0","D_TIMESTAMP":"2025-05-25 03:01:36.0","DWJFBL":"0.002","DWSJ":"9.76","YJJE":"9.76","JS":"0","XZLX":"工伤保险","LYJG":"人社局","LYB":"dest_rsj_grjfmx_zg","YWID":"330381199010043613202410","XM":"袁成业","GRYJ":"0","GMSFHM":"330381199010043613","GZDWMC":"常州奥视科技有限公司","JFQB":"202410","GRJFBL":"0","BSZHLX":"统一社会信用代码","AGENCY_KEY":"fe26f684-ef78-4062-8579-20cef6f1afc2","DWYJ":"9.76","COUNTY":"武进区"}]}
     */
    public static JSONObject getZGSBJNXX(String JFQB,
                                         String GMSFHM,
                                         String XM) {
//        String url = "http://172.18.66.90:9090/wjqdsjgljhl/zgsbjnxx";
        String url = "http://127.0.0.1:9090/wjqdsjgljhl/zgsbjnxx";
        String filter = "JFQB eq '" + JFQB + "' and GMSFHM eq '" + GMSFHM + "' and XM eq '" + XM + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();

        return parseResult(body);
    }


    /**
     * 武进区_大数据管理局_人口_被执行人信息(回流)V1.00
     *
     * @param MC   被执行人姓名/名称
     * @param ZJHM 身份证号码/组织机构代码
     * @return 返回值
     */
    public static JSONObject getBZXRXX(String MC,
                                       String ZJHM) {

        String url = "http://127.0.0.1:9090/wjqdsjgljhl/bzxrxx";
        String filter = "MC eq '" + MC + "' and ZJHM eq '" + ZJHM + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);
    }

    /**
     * 武进区_大数据管理局_人口_失信被执行人信息(回流)V1.00
     *
     * @param ZJHM     证件号码
     * @param SXBZXRXM 失信被执行人姓名
     * @return 返回值
     */
    public static JSONObject getRKSXBZXRXX(String ZJHM,
                                           String SXBZXRXM) {

        String url = "http://127.0.0.1:9090/wjqdsjgljhl/rk_sxbzxrxx";
        String filter = "ZJHM eq '" + ZJHM + "' and SXBZXRXM eq '" + SXBZXRXM + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);
    }


    /**
     * 武进区_大数据管理局_人口_不动产信息(回流)V1.00
     *
     * @param GMSFHM 公民身份号码
     * @param XM     姓名
     * @return 返回值
     * <p>
     * {"value":[{"YWID":"670d621355b9f993dd9aff9953a50e11","BDCQH":"苏(2017)常州市不动产权第2001569-2号","QLRGMSFHM":"320322196212140029、320322198403250078、320682198812246443","QLRXM":"刘金红、许亚、王海琴","XM":"许亚","GMSFHM":"320322198403250078","FWYT":"住宅","TDYT":"城镇住宅用地","XZQ":"武进区","ZL":"凯尔锋度花园5幢乙单元702室","JZMJ":"83.48","LYB":"dwd_bdc_info","LYJG":"资规局","D_TIMESTAMP":"2024-10-19 23:32:53.0","AGENCY_KEY":"21416042-f6f5-4666-9736-6d2113aa161e","D_OPERATION":"更新","COUNTY":"武进区"},{"YWID":"c88fb01c03037ba19a7cb0a269893c74","BDCQH":"苏(2019)常州市不动产权第2019257号","QLRGMSFHM":"320322198403250078、320682198812246443","QLRXM":"许亚、王海琴","XM":"许亚","GMSFHM":"320322198403250078","FWYT":"住宅","TDYT":"城镇住宅用地","XZQ":"武进区","ZL":"湖塘镇吾悦广场10幢乙单元302室","JZMJ":"99.76","LYB":"dwd_bdc_info","LYJG":"资规局","D_TIMESTAMP":"2024-10-19 23:32:53.0","AGENCY_KEY":"21edaa08-9dcc-4ede-bd02-f6258f269fa7","D_OPERATION":"更新","COUNTY":"武进区"},{"YWID":"38979a03fddbf55c2745e52b22d84004","BDCQH":"苏(2017)常州市不动产权第2045826号","QLRGMSFHM":"320322198403250078","QLRXM":"许亚","XM":"许亚","GMSFHM":"320322198403250078","FWYT":"车库","TDYT":"城镇住宅用地","XZQ":"武进区","ZL":"武进区湖塘镇凯尔锋度花园1号车库538号","JZMJ":"26.8","LYB":"dwd_bdc_info","LYJG":"资规局","D_TIMESTAMP":"2024-10-19 23:32:53.0","AGENCY_KEY":"3e2449d9-78d9-4a15-a1aa-1d78a10850f8","D_OPERATION":"更新","COUNTY":"武进区"}]}
     */
    public static JSONObject getBDCXX(String GMSFHM,
                                      String XM) {

        String url = "http://127.0.0.1:9090/wjqdsjgljhl/bdcxx";
        String filter = "GMSFHM eq '" + GMSFHM + "' and XM eq '" + XM + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);
    }


    /**
     * 武进区_大数据管理局_企业_参保单位信息(回流)V1.00
     *
     * @param TYSHXYDM 统一社会信用代码
     * @param DWMC     单位名称
     * @return 返回值
     * <p>
     * {"value":[{"TCQ":"320412","SSJD":"320412000000","TYSHXYDM":"91320412MA1MQL9993","DWBH":15089922,"DWMC":"常州微通信息科技有限公司","CBRS":12,"LXDZ":"","FRXM":"徐小双","FRDH":"18915068678","ZGY":"","ZGYDH":"18915068678","CBZT":"1","COUNTY":"武进区"}]}
     */
    public static JSONObject getCBDWXX(String TYSHXYDM,
                                       String DWMC) {
        String url = "http://127.0.0.1:9090/wjqdsjgljhl/cbdwxx";
        String filter = "TYSHXYDM eq '" + TYSHXYDM + "' and DWMC eq '" + DWMC + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);
    }


    /**
     * 武进区_大数据管理局_企业_全市股权出质登记信息(回流)V1.00
     *
     * @param TYSHXYDM 统一社会信用代码
     * @param JGMC     机构名称
     * @return 返回值
     */
    public static JSONObject getQSGQCZDJXX(String TYSHXYDM,
                                           String JGMC) {
        String url = "http://127.0.0.1:9090/wjqdsjgljhl/qsgqczdjxx";
        String filter = "TYSHXYDM eq '" + TYSHXYDM + "' and JGMC eq '" + JGMC + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);
    }

    /**
     * 武进区_大数据管理局_企业_全市企业高管人员信息(回流)V1.00
     *
     * @param TYSHXYDM 统一社会信用代码
     * @param JGMC     机构名称
     * @return 返回值
     * <p>
     * {"value":[{"MD5":"55ed582af7a10fbbd4dfddb18fa577d2","TYSHXYDM":"91320412MA1MQL9993","ZCDZ":"常州西太湖科技产业园禾香路123号7号楼A区3008S-498室","ZJHM":"320483199311084220","FDDBRZJLX":"0","JGMC":"常州微通信息科技有限公司","PERSON_NAME":"臧滢洁","PERSON_TYPE":"监事","REG_CAPI":1000000000,"D_TIMESTAMP":"2024-10-18 22:31:13.0","COUNTY":"武进区","TOWN":"湖塘镇"},{"MD5":"e65532e2bf86db24594cd34b44158ca9","TYSHXYDM":"91320412MA1MQL9993","ZCDZ":"常州西太湖科技产业园禾香路123号7号楼A区3008S-498室","ZJHM":"32092319830528031X","FDDBRZJLX":"0","JGMC":"常州微通信息科技有限公司","PERSON_NAME":"徐小双","PERSON_TYPE":"执行董事","REG_CAPI":1000000000,"D_TIMESTAMP":"2024-10-18 22:31:13.0","COUNTY":"武进区","TOWN":"湖塘镇"}]}
     */
    public static JSONObject getQSQYGGRYXX(String TYSHXYDM,
                                           String JGMC) {

        String url = "http://127.0.0.1:9090/wjqdsjgljhl/qsqyggryxx";
        String filter = "TYSHXYDM eq '" + TYSHXYDM + "' and JGMC eq '" + JGMC + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);
    }


    /**
     * 武进区_大数据管理局_企业_全市企业分支机构信息(回流)V1.00
     *
     * @param TYSHXYDM 统一社会信用代码
     * @param JGMC     机构名称
     * @return 返回值
     */
    public static JSONObject getQSQYFZJGXX(String TYSHXYDM,
                                           String JGMC) {
        String url = "http://127.0.0.1:9090/wjqdsjgljhl/qsqyfzjgxx";
        String filter = "TYSHXYDM eq '" + TYSHXYDM + "' and JGMC eq '" + JGMC + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);
    }

    /**
     * 武进区_大数据管理局_企业_全市企业股东信息(回流)V1.00
     *
     * @param TYSHXYDM
     * @param JGMC
     * @return 返回值
     */
    public static JSONObject getQSQYGDXX(String TYSHXYDM,
                                         String JGMC) {

        String url = "http://127.0.0.1:9090/wjqdsjgljhl/qsqygdxx";
        String filter = "TYSHXYDM eq '" + TYSHXYDM + "' and JGMC eq '" + JGMC + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);
    }


    /**
     * 武进区_大数据管理局_企业_失信被执行人信息(回流)V1.00
     *
     * @param TYSHXYDM 统一社会信用代码
     * @param BZXRMC   被执行人名称
     * @return 返回值
     */
    public static JSONObject getQYSXBZXRXX(String TYSHXYDM,
                                           String BZXRMC) {

        String url = "http://127.0.0.1:9090/wjqdsjgljhl/sxbzxrxx";
        String filter = "TYSHXYDM eq '" + TYSHXYDM + "' and BZXRMC eq '" + BZXRMC + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);
    }


    /**
     * 武进区_大数据管理局_企业_全市企业注销信息(回流)V1.00
     *
     * @param TYSHXYDM
     * @param JGMC
     * @return 返回值
     * <p>
     * {"value":[{"MD5":"6d84fe821ef54f2a0267bd5767ef59c4","TYSHXYDM":"91320412MA1XG5A46L","JGMC":"常州坚愚商务信息咨询有限公司","JYZT":"吊销后注销","FDDBRZJHM":"320481198808014634","FDDBR":"罗伟","CLRQ":"2018-11-16","ADDR":"常州市武进区湖塘镇吾悦广场1幢1705号","BELONG_DIST_ORG_NAME":"常州市武进区市场监督管理局湖塘分局","BELONG_ORG_NAME":"常州市武进区行政审批局","BELONG_TRADE":"商务服务业","CHECK_DATE":"2023-08-07 10:32:04.0","ECON_KIND":"有限责任公司（自然人投资或控股）","FARE_SCOPE":"商务信息咨询；汽车租赁；企业管理咨询；房地产营销策划；房产信息咨询。（依法须经批准的项目，经相关部门批准后方可开展经营活动）","FARE_TERM_START":"2018-11-16 10:38:44.0","REG_CAPI":100,"REVOKE_DATE":"2022-06-20 00:00:00.0","REVOKE_REASON":"长期停业未经营","TEL":"13913583549","ZIP":"213000","D_TIMESTAMP":"2024-10-18 22:31:23.0","COUNTY":"武进区"}]}
     */
    public static JSONObject getQSQYZXXX(String TYSHXYDM,
                                         String JGMC) {

        String url = "http://127.0.0.1:9090/wjqdsjgljhl/qsqyzxxx";
        String filter = "TYSHXYDM eq '" + TYSHXYDM + "' and JGMC eq '" + JGMC + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);

    }

    /**
     * 武进区_大数据管理局_企业_全市企业基本信息(回流)V1.00
     *
     * @param TYSHXYDM
     * @param JGMC
     * @return 返回值
     * <p>
     * {"value":[{"MD5":"e0eb6edacbddeef8066d5362869e641d","TYSHXYDM":"91320412MA1MQL9993","ZCDZ":"常州西太湖科技产业园禾香路123号7号楼A区3008S-498室","JGMC":"常州微通信息科技有限公司","FDDBRZJHM":"32092319830528031X","FDDBR":"徐小双","BELONG_DIST_ORG_NAME":"常州市武进区市场监督管理局西太湖分局","BELONG_ORG_NAME":"常州市武进区行政审批局","BELONG_TRADE":"研究和试验发展","BELONG_TRADE_CODE":"M7300","CHECK_DATE":"2024-04-03","CORP_STATUS":"在业","ECON_KIND":"有限责任公司（自然人独资）","FARE_SCOPE":"许可项目：第二类增值电信业务；第一类增值电信业务（依法须经批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以审批结果为准）一般项目：技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；通讯设备销售；电子产品销售；信息技术咨询服务（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）","FARE_TERM_START":"2016-07-29","REG_CAPI":1000.00,"REG_CAPI_TYPE":"人民币","START_DATE":"2016-07-29","TEL":"13651571111","ZIP":"213100","D_TIMESTAMP":"2024-10-19 06:05:51","COUNTY":"武进区","TOWN":"湖塘镇"}]}
     */
    public static JSONObject getQSQYJBXX(String TYSHXYDM,
                                         String JGMC) {
        String url = "http://127.0.0.1:9090/wjqdsjgljhl/qsqyjbxx";
        String filter = "TYSHXYDM eq '" + TYSHXYDM + "' and JGMC eq '" + JGMC + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);


    }

    /**
     * 武进区_大数据管理局_企业_全市企业吊销信息(回流)V1.00
     *
     * @param TYSHXYDM
     * @param JGMC
     * @return 返回值
     * <p>
     * {"value":[{"MD5":"206ef6b54dabe4c3264e12aeee6ff2ae","TYSHXYDM":"91320412MA1NUP2042","JGMC":"常州微米商务信息咨询有限公司","JYZT":"吊销后注销","FDDBRZJHM":"320482199403156918","FDDBR":"沙志鹏","CLRQ":"2017-04-25","ADDR":"常州市武进区湖塘镇吾悦广场1幢1916号","BELONG_DIST_ORG_NAME":"常州市武进区市场监督管理局湖塘分局","BELONG_ORG_NAME":"常州市武进区行政审批局","BELONG_TRADE":"商务服务业","CHECK_DATE":"2022-04-28 16:46:51.0","ECON_KIND":"有限责任公司（自然人独资）","FARE_SCOPE":"商务信息咨询；企业管理咨询；二手车经纪；市场营销策划；会务服务；展览展示服务；计算机信息咨询。（依法须经批准的项目，经相关部门批准后方可开展经营活动）","FARE_TERM_END":"2047-04-24 00:00:00.0","FARE_TERM_START":"2017-04-25 00:00:00.0","REG_CAPI":50,"REG_CAPI_TYPE":"人民币","REVOKE_DATE":"2021-06-28 00:00:00.0","REVOKE_REASON":"长期停业未经营","TEL":"13915074190","ZIP":"213161","D_TIMESTAMP":"2024-10-18 22:31:23.0","COUNTY":"武进区"}]}
     */
    public static JSONObject getQSQYDXXX(String TYSHXYDM,
                                         String JGMC) {

        String url = "http://127.0.0.1:9090/wjqdsjgljhl/qsqydxxx";
        String filter = "TYSHXYDM eq '" + TYSHXYDM + "' and JGMC eq '" + JGMC + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);
    }

    /**
     * 武进区_大数据管理局_企业_全市企业变更信息(回流)V1.00
     *
     * @param TYSHXYDM
     * @param JGMC
     * @return 返回值
     * <p>
     * {"value":[{"MD5":"215403a55dc98dd8b688fde464dd125a","TYSHXYDM":"91320412MA1YG5BJ6B","JGMC":"常州顺驰生态环境科技有限公司","FDDBR":"蒋骋","ADDR":"武进区湖塘镇吾悦广场1幢1210号","BELONG_TRADE":"专业技术服务业","CHANGE_ITEM_NO":"类型变更","CREATE_DATE":"2020-01-06 11:09:08.0","FARE_SCOPE":"环境治理技术的研发；园林绿化工程、古典园林建筑工程、市政公用工程、灯光照明工程设计、施工与养护；市政道路工程、桥梁工程、公路工程、房屋建筑工程、道路交通安全管制设施工程、城市排水管道工程、园林景观工程、室内外装饰工程、土石方工程、河湖整治工程、城市道路照明工程、环境治理工程、防水工程、防腐保温工程设计、施工；室内水电安装、钢结构安装、机电设备安装；花卉、苗木、盆景种植、销售；喷泉、雕塑、公用健身设施、木结构制品的设计、销售。（依法须经批准的项目，经相关部门批准后方可开展经营活动）","D_TIMESTAMP":"2024-10-18 22:31:35.0","COUNTY":"武进区","TOWN":"湖塘镇"},{"MD5":"4e1f5c33752ff2cf943d22c4adaa3af7","TYSHXYDM":"91320412MA1YG5BJ6B","JGMC":"常州顺驰生态环境科技有限公司","FDDBR":"蒋骋","ADDR":"武进区湖塘镇吾悦广场1幢1210号","BELONG_TRADE":"专业技术服务业","CHANGE_ITEM_NO":"股东变更","CREATE_DATE":"2020-01-06 11:09:08.0","FARE_SCOPE":"环境治理技术的研发；园林绿化工程、古典园林建筑工程、市政公用工程、灯光照明工程设计、施工与养护；市政道路工程、桥梁工程、公路工程、房屋建筑工程、道路交通安全管制设施工程、城市排水管道工程、园林景观工程、室内外装饰工程、土石方工程、河湖整治工程、城市道路照明工程、环境治理工程、防水工程、防腐保温工程设计、施工；室内水电安装、钢结构安装、机电设备安装；花卉、苗木、盆景种植、销售；喷泉、雕塑、公用健身设施、木结构制品的设计、销售。（依法须经批准的项目，经相关部门批准后方可开展经营活动）","D_TIMESTAMP":"2024-10-18 22:31:35.0","COUNTY":"武进区","TOWN":"湖塘镇"}]}
     */
    public static JSONObject getQSQYBGXX(String TYSHXYDM,
                                         String JGMC) {

        String url = "http://127.0.0.1:9090/wjqdsjgljhl/qsqybgxx";
        String filter = "TYSHXYDM eq '" + TYSHXYDM + "' and JGMC eq '" + JGMC + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);

    }


    /**
     * 武进区_大数据管理局_企业_全市个体工商户基本信息(回流)V1.00
     *
     * @param TYSHXYDM
     * @param JGMC
     * @return 返回值
     * <p>
     * {"value":[{"MD5":"f326537bc6e217f7af727528181537c0","V_HYDM":"O8200","JJHKLB":"个体","XZQH":"武进区","ZCDZ":"武进区湖塘镇吾悦广场1幢12B16号","BELONG_TRADE":"其他服务业","BELONG_DIST_ORG_NAME":"常州市武进区市场监督管理局湖塘分局","BELONG_ORG_NAME":"常州市武进区行政审批局","JGMC":"武进区湖塘素颜婚介服务部（个体工商户）","JYZT":"注销","FARE_SCOPE":"一般项目：婚姻介绍服务；健康咨询服务（不含诊疗服务）；信息咨询服务（不含许可类信息咨询服务）（除依法须经批准的项目外，凭营业执照依法自主开展经营活动）","FDDBRZJHM":"321183198911174828","FDDBR":"秦纪丽","PRAC_PERSON_NUM":2,"REG_CAPI":10,"CLRQ":"2024-04-19","TEL":"18961442117","TYSHXYDM":"92320412MADJ4MWF6P","D_TIMESTAMP":"2024-10-18 22:32:23.0","COUNTY":"武进区"}]}
     */
    public static JSONObject getQSGTGSHJBXX(String TYSHXYDM,
                                            String JGMC) {

        String url = "http://127.0.0.1:9090/wjqdsjgljhl/qsgtgshjbxx";
        String filter = "TYSHXYDM eq '" + TYSHXYDM + "' and JGMC eq '" + JGMC + "'";
        HttpResponse response = HttpUtil.createGet(url)
                .addHeaders(getRequestHeaders())
                .form("$filter", filter)
                .execute();
        String body = response.body();
        return parseResult(body);
    }
}
