package com.hl.building;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.building.domain.*;
import com.hl.building.domain.tables.CompanyInfo;
import com.hl.building.domain.tables.HouseInfo;
import com.hl.building.domain.tables.PersonInfo;
import com.hl.building.mapper.CompanyMapper;
import com.hl.building.mapper.HouseMapper;
import com.hl.building.mapper.PersonMapper;
import com.hl.building.service.*;
import com.hl.building.services.CompanyService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class HouseDataTest {

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private WjHouseInfoService wjHouseInfoService;

    @Test
    void houseTest() {
        List<Map<String, Object>> mapList = jdbcTemplate.queryForList("SELECT * FROM `house_info` WHERE build_id in (SELECT build_id from building_info  )");
        for (Map<String, Object> map : mapList) {
            JSONObject house = JSONObject.from(map);
            System.out.println(house);
            String houseName = house.getString("house_id");
            WjHouseInfo wjHouseInfo = new WjHouseInfo();
            wjHouseInfo.setHouseName(houseName);

            Integer buildId = house.getInteger("build_id");
            if (buildId == 6) {
                wjHouseInfo.setBuildId("ed9456d66912f65a3d45089cf40cd59e");
            }
            if (buildId == 7) {
                wjHouseInfo.setBuildId("5e1ff1919533fb4808de5b1f0322b3a9");
            }
            if (buildId == 8) {
                wjHouseInfo.setBuildId("e5329bd46805555ea1aa7e89bf36ec82");
            }
            if (buildId == 9) {
                wjHouseInfo.setBuildId("a14a82f5b16e277ae349a1a803ddf448");
            }
            if (buildId == 10) {
                wjHouseInfo.setBuildId("57b12c7c39a188e672ba8ea8c64523de");
            }
            wjHouseInfo.setFloor(house.getInteger("floor"));
            wjHouseInfo.setXLayer(house.getInteger("x_layer"));
            wjHouseInfo.setYLayer(house.getInteger("y_layer"));
            wjHouseInfo.setRowNum(house.getInteger("row_num"));
            wjHouseInfo.setColumnNum(house.getInteger("column_num"));
            wjHouseInfo.setAddress(house.getString("address"));
            wjHouseInfo.setCreateUser("hualong");
            wjHouseInfoService.save(wjHouseInfo);
        }

    }

    @Resource
    private WjCompanyInfoService wjCompanyInfoService;

    @Test
    void companyTest() {
        List<Map<String, Object>> list = jdbcTemplate.queryForList("SELECT * from company_info WHERE company_code is not NULL");
        for (Map<String, Object> map : list) {
            JSONObject company = JSONObject.from(map);

            WjCompanyInfo wjCompanyInfo = new WjCompanyInfo();
            wjCompanyInfo.setCompanyName(company.getString("company_name"));
            wjCompanyInfo.setCompanyCode(company.getString("company_code"));
            wjCompanyInfo.setCompanyAddress(company.getString("company_address"));
            wjCompanyInfo.setIsFocus(company.getInteger("is_focus"));
            wjCompanyInfo.setIsLogout(company.getInteger("is_logout"));
            wjCompanyInfo.setCreateUser("hualong");

            Integer buildId = company.getInteger("build_id");
            if (buildId != null) {
                if (buildId == 6) {
                    wjCompanyInfo.setBuildId("ed9456d66912f65a3d45089cf40cd59e");
                }
                if (buildId == 7) {
                    wjCompanyInfo.setBuildId("5e1ff1919533fb4808de5b1f0322b3a9");
                }
                if (buildId == 8) {
                    wjCompanyInfo.setBuildId("e5329bd46805555ea1aa7e89bf36ec82");
                }
                if (buildId == 9) {
                    wjCompanyInfo.setBuildId("a14a82f5b16e277ae349a1a803ddf448");
                }
                if (buildId == 10) {
                    wjCompanyInfo.setBuildId("57b12c7c39a188e672ba8ea8c64523de");
                }
            }
            wjCompanyInfoService.save(wjCompanyInfo);
        }
    }

    @Resource
    private CompanyMapper companyMapper;

    @Resource
    private HouseMapper houseMapper;

    @Resource
    private WjCompanyHouseService wjCompanyHouseService;

    @Test
    void companyHouse(){

        List<WjCompanyInfo> list = wjCompanyInfoService.list();
        for (WjCompanyInfo wjCompanyInfo : list) {

            String companyCode = wjCompanyInfo.getCompanyCode();
            CompanyInfo companyInfo = companyMapper.selectOne(Wrappers.<CompanyInfo>lambdaQuery()
                    .eq(CompanyInfo::getCompanyCode, companyCode)
                    .last(" limit 1"));
            if (companyInfo!= null){

                List<Integer> houseId = companyInfo.getHouseId();
                if (houseId != null && !houseId.isEmpty()) {

                    List<HouseInfo> houseInfos = houseMapper.selectBatchIds(houseId);

                    for (HouseInfo houseInfo : houseInfos) {
                        String infoHouseId = houseInfo.getHouseId();
                        WjHouseInfo one = wjHouseInfoService.getOne(Wrappers.<WjHouseInfo>lambdaQuery()
                                .eq(WjHouseInfo::getHouseName, infoHouseId));
                        if (one != null){
                            String id = one.getHouseId();

                            WjCompanyHouse companyHouse = new WjCompanyHouse();
                            companyHouse.setCompanyId(wjCompanyInfo.getCompanyId());
                            companyHouse.setHouseId(id);
                            wjCompanyHouseService.save(companyHouse);

                        }


                    }


                }

            }

        }

    }


    @Resource
    private PersonMapper personMapper;


    @Resource
    private WjPersonInfoService wjPersonInfoService;


    @Test
    void companyPersonTest(){
        List<PersonInfo> personInfos = personMapper.selectList(null);

        for (PersonInfo personInfo : personInfos) {
            WjPersonInfo wjPersonInfo = BeanUtil.copyProperties(personInfo, WjPersonInfo.class);
            wjPersonInfo.setPersonId(null);
            wjPersonInfoService.save(wjPersonInfo);
        }
    }

    @Resource
    private WjCompanyPersonService wjCompanyPersonService;

    @Test
    void companyPersonTableTest(){
        List<CompanyInfo> companyInfos = companyMapper.selectList(null);
        for (CompanyInfo companyInfo : companyInfos) {

            String companyCode = companyInfo.getCompanyCode();
            WjCompanyInfo one = wjCompanyInfoService.getOne(Wrappers.<WjCompanyInfo>lambdaQuery().eq(WjCompanyInfo::getCompanyCode, companyCode).last(" limit 1"));

            List<Integer> corporate = companyInfo.getCorporate();
            if (corporate != null && !corporate.isEmpty()){
                // 企业法人
                for (Integer i : corporate) {
                    PersonInfo personInfo = personMapper.selectById(i);
                    String idCard = personInfo.getIdCard();
                    WjPersonInfo wjPersonInfo = wjPersonInfoService.getOne(Wrappers.<WjPersonInfo>lambdaQuery().eq(WjPersonInfo::getIdCard, idCard));

                    WjCompanyPerson wjCompanyPerson = new WjCompanyPerson();
                    wjCompanyPerson.setCompanyId(one.getCompanyId());
                    wjCompanyPerson.setPersonId(wjPersonInfo.getPersonId());
                    wjCompanyPerson.setPosition(1);
                    wjCompanyPersonService.save(wjCompanyPerson);
                }
            }

            List<Integer> executives = companyInfo.getExecutives();
            if (executives != null && !executives.isEmpty()){
                // 企业高管
                for (Integer i : executives) {
                    PersonInfo personInfo = personMapper.selectById(i);
                    String idCard = personInfo.getIdCard();
                    WjPersonInfo wjPersonInfo = wjPersonInfoService.getOne(Wrappers.<WjPersonInfo>lambdaQuery().eq(WjPersonInfo::getIdCard, idCard));
                    WjCompanyPerson wjCompanyPerson = new WjCompanyPerson();
                    wjCompanyPerson.setCompanyId(one.getCompanyId());
                    wjCompanyPerson.setPersonId(wjPersonInfo.getPersonId());
                    wjCompanyPerson.setPosition(2);
                    wjCompanyPersonService.save(wjCompanyPerson);
                }
            }
        }

    }

}
