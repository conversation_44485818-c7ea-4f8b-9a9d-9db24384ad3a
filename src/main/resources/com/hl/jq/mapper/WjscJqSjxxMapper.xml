<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.jq.mapper.WjscJqSjxxMapper">
  <resultMap id="BaseResultMap" type="com.hl.jq.domain.WjscJqSjxx">
    <!--@mbg.generated-->
    <!--@Table wjsc_jq_sjxx-->
    <id column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="jjbh" jdbcType="VARCHAR" property="jjbh" />
    <result column="gmsfhm" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="xm" jdbcType="VARCHAR" property="xm" />
    <result column="sjlb" jdbcType="VARCHAR" property="sjlb" />
    <result column="djsj_time" jdbcType="VARCHAR" property="djsjTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="djsj" jdbcType="TIMESTAMP" property="djsj" />
    <result column="personM" jdbcType="LONGVARCHAR" property="personm" />
    <result column="personMs" jdbcType="LONGVARCHAR" property="personms" />
    <result column="jgbh" jdbcType="VARCHAR" property="jgbh" />
    <result column="dwmc" jdbcType="VARCHAR" property="dwmc" />
    <result column="zxbs" jdbcType="INTEGER" property="zxbs" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    uuid, jjbh, gmsfhm, xm, sjlb, djsj_time, create_time, djsj, personM, personMs, jgbh, 
    dwmc, zxbs
  </sql>
</mapper>