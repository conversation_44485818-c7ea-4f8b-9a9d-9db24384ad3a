<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.ViewCameraFaceRecordMapper">
  <resultMap id="BaseResultMap" type="com.hl.building.domain.ViewCameraFaceRecord">
    <!--@mbg.generated-->
    <!--@Table wjhl.view_camera_face_record-->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="face_image_id" jdbcType="VARCHAR" property="faceImageId" />
    <result column="pic_id" jdbcType="VARCHAR" property="picId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="appear_time" jdbcType="TIMESTAMP" property="appearTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="similarity" jdbcType="VARCHAR" property="similarity" />
    <result column="global_face_image_uri" jdbcType="VARCHAR" property="globalFaceImageUri" />
    <result column="camera_name" jdbcType="VARCHAR" property="cameraName" />
    <result column="camera_id" jdbcType="VARCHAR" property="cameraId" />
    <result column="inter_code" jdbcType="VARCHAR" property="interCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, face_image_id, pic_id, "name", id_card, appear_time, create_time, similarity, 
    global_face_image_uri, camera_name, camera_id, inter_code
  </sql>
</mapper>