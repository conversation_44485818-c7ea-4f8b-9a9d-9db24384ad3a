<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.WjscSyrkMapper">
  <resultMap id="BaseResultMap" type="com.hl.building.domain.WjscSyrk">
    <!--@mbg.generated-->
    <!--@Table wjsc_syrk-->
    <id column="syrkid" jdbcType="VARCHAR" property="syrkid" />
    <result column="gmsfhm" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="xm" jdbcType="VARCHAR" property="xm" />
    <result column="xb" jdbcType="VARCHAR" property="xb" />
    <result column="csrq" jdbcType="TIMESTAMP" property="csrq" />
    <result column="rylb" jdbcType="VARCHAR" property="rylb" />
    <result column="ryzt" jdbcType="VARCHAR" property="ryzt" />
    <result column="hjddzmc" jdbcType="VARCHAR" property="hjddzmc" />
    <result column="hjdzrqdm" jdbcType="VARCHAR" property="hjdzrqdm" />
    <result column="xzddzmc" jdbcType="VARCHAR" property="xzddzmc" />
    <result column="xzddzdm" jdbcType="VARCHAR" property="xzddzdm" />
    <result column="xzdzrqdm" jdbcType="VARCHAR" property="xzdzrqdm" />
    <result column="rksj" jdbcType="TIMESTAMP" property="rksj" />
    <result column="lxfs" jdbcType="VARCHAR" property="lxfs" />
    <result column="scrksj" jdbcType="TIMESTAMP" property="scrksj" />
    <result column="zxgxsj" jdbcType="TIMESTAMP" property="zxgxsj" />
    <result column="xmpy" jdbcType="VARCHAR" property="xmpy" />
    <result column="cym" jdbcType="VARCHAR" property="cym" />
    <result column="mz" jdbcType="VARCHAR" property="mz" />
    <result column="sg" jdbcType="INTEGER" property="sg" />
    <result column="gj" jdbcType="VARCHAR" property="gj" />
    <result column="hjqx" jdbcType="VARCHAR" property="hjqx" />
    <result column="hyzk" jdbcType="VARCHAR" property="hyzk" />
    <result column="zzmm" jdbcType="VARCHAR" property="zzmm" />
    <result column="zjxy" jdbcType="VARCHAR" property="zjxy" />
    <result column="hjddzdm" jdbcType="VARCHAR" property="hjddzdm" />
    <result column="hjddsqdm" jdbcType="VARCHAR" property="hjddsqdm" />
    <result column="xzdpcsdm" jdbcType="VARCHAR" property="xzdpcsdm" />
    <result column="xzdfjdm" jdbcType="VARCHAR" property="xzdfjdm" />
    <result column="xzdsjdm" jdbcType="VARCHAR" property="xzdsjdm" />
    <result column="dpt_rybh" jdbcType="VARCHAR" property="dptRybh" />
    <result column="dpt_rysx" jdbcType="VARCHAR" property="dptRysx" />
    <result column="ywid" jdbcType="VARCHAR" property="ywid" />
    <result column="x" jdbcType="NUMERIC" property="x" />
    <result column="y" jdbcType="NUMERIC" property="y" />
    <result column="sjc" jdbcType="TIMESTAMP" property="sjc" />
    <result column="hh" jdbcType="VARCHAR" property="hh" />
    <result column="hklx" jdbcType="VARCHAR" property="hklx" />
    <result column="yhzgx" jdbcType="VARCHAR" property="yhzgx" />
    <result column="whcd" jdbcType="VARCHAR" property="whcd" />
    <result column="byzk" jdbcType="VARCHAR" property="byzk" />
    <result column="fwcs" jdbcType="VARCHAR" property="fwcs" />
    <result column="zylb" jdbcType="VARCHAR" property="zylb" />
    <result column="zw" jdbcType="VARCHAR" property="zw" />
    <result column="fqid" jdbcType="VARCHAR" property="fqid" />
    <result column="jzrkbs" jdbcType="VARCHAR" property="jzrkbs" />
    <result column="jzrksjc" jdbcType="VARCHAR" property="jzrksjc" />
    <result column="jyqkid" jdbcType="VARCHAR" property="jyqkid" />
    <result column="xpid" jdbcType="VARCHAR" property="xpid" />
    <result column="jzqkid" jdbcType="VARCHAR" property="jzqkid" />
    <result column="rysx" jdbcType="VARCHAR" property="rysx" />
    <result column="sf" jdbcType="VARCHAR" property="sf" />
    <result column="jg" jdbcType="VARCHAR" property="jg" />
    <result column="operation" jdbcType="VARCHAR" property="operation" />
    <result column="last_modify_time" jdbcType="TIMESTAMP" property="lastModifyTime" />
    <result column="zplj" jdbcType="VARCHAR" property="zplj" />
    <result column="syrkhsjg" jdbcType="VARCHAR" property="syrkhsjg" />
    <result column="czsj" jdbcType="TIMESTAMP" property="czsj" />
    <result column="ldrkbs" jdbcType="VARCHAR" property="ldrkbs" />
    <result column="xzdzrqmc" jdbcType="VARCHAR" property="xzdzrqmc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    syrkid, gmsfhm, xm, xb, csrq, rylb, ryzt, hjddzmc, hjdzrqdm, xzddzmc, xzddzdm, xzdzrqdm, 
    rksj, lxfs, scrksj, zxgxsj, xmpy, cym, mz, sg, gj, hjqx, hyzk, zzmm, zjxy, hjddzdm, 
    hjddsqdm, xzdpcsdm, xzdfjdm, xzdsjdm, dpt_rybh, dpt_rysx, ywid, x, y, sjc, hh, hklx, 
    yhzgx, whcd, byzk, fwcs, zylb, zw, fqid, jzrkbs, jzrksjc, jyqkid, xpid, jzqkid, rysx, 
    sf, jg, "operation", last_modify_time, zplj, syrkhsjg, czsj, ldrkbs, xzdzrqmc
  </sql>
</mapper>