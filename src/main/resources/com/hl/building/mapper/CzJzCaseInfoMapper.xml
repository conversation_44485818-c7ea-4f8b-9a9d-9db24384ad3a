<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.CzJzCaseInfoMapper">

  <select id="selectCzJzCaseInfoList" resultType="com.hl.building.domain.CzJzCaseInfo">
    select *
    from harzon.cz_jz_case_info where case_no in (select case_no
                                                  from harzon.cz_jz_case_person where id_number = #{idCard})
  </select>
</mapper>