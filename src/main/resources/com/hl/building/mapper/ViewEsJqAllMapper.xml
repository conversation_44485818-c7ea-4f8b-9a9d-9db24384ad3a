<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.ViewEsJqAllMapper">

  <select id="selectJqList" resultType="com.hl.building.domain.ViewEsJqAll">
    select * from view_es_jq_all where jjbh in (select  jjbh from wjsc_jq_sjxx where gmsfhm = #{idCard} )
  </select>
</mapper>