<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.WjLyJqxxMapper">

    <resultMap id="baseResultMap" type="com.hl.building.domain.vo.WjLyJqxxPageRespVO">
        <result property="company" column="company" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result property="building" column="building" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result property="person" column="person" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result property="companyInfo" column="company_info" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result property="buildingInfo" column="building_info" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result property="personInfo" column="person_info" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
    </resultMap>

    <select id="pageList" resultMap="baseResultMap">
        select *
        from wjhl.wj_ly_jqxx
        <where>
            <if test="req.jjbh != null and req.jjbh != ''">
                and jjbh like '%' || #{req.jjbh} || '%'
            </if>
            <if test="req.bjlx != null and req.bjlx != ''">
                and bjlx = #{req.bjlx}
            </if>
            <if test="req.jjdw != null and req.jjdw != ''">
                and jjdw like '%' || #{req.jjdw} || '%'
            </if>
            <if test="req.cjlb != null and req.cjlb != ''">
                and cjlb = #{req.cjlb}
            </if>
            <if test="req.cjdw != null and req.cjdw != ''">
                and cjdw like '%' || #{req.cjdw} || '%'
            </if>
            <if test="req.companyName != null and req.companyName != ''">
                and company_info::text like '%' || #{req.companyName} || '%'
            </if>
            <if test="req.buildingName != null and req.buildingName != ''">
                and building_info::text like '%' || #{req.buildingName} || '%'
            </if>
            <if test="req.jjsjStart != null and req.jjsjStart != ''">
                and bjdhsj_time &gt;= #{req.jjsjStart}
            </if>
            <if test="req.jjsjEnd != null  and  req.jjsjEnd != ''">
                and bjdhsj_time &lt;= #{req.jjsjEnd}
            </if>
        </where>
    </select>

    <select id="countJqCount" resultType="com.alibaba.fastjson2.JSONObject">
        select jgbh,count(1) as count
        from  wj_ly_jqxx where jgbh in
        <foreach collection="jgbhList" item="jgbh" separator="," open="(" close=")">
            #{jgbh}
        </foreach>
    </select>
</mapper>