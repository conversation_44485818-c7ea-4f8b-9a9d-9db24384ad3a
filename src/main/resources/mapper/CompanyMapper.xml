<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.CompanyMapper">
    <resultMap id="SqrResultMap" type="com.hl.building.domain.vo.CompanyDistributionVo">
        <result property="otherPerson" column="other_person" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="corporate" column="corporate" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="executives" column="executives" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="finance" column="finance" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="houseId" column="house_id" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <resultMap id="SqrResultMap2" type="com.hl.building.domain.vo.CompanyInfoVo">
        <result property="otherPerson" column="other_person" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="corporate" column="corporate" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="executives" column="executives" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="finance" column="finance" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="houseId" column="house_id" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <resultMap id="SqrResultMap3" type="com.hl.building.domain.vo.CompanyNatureVo">
        <result property="houseId" column="house_id" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <resultMap id="SqrResultMap4" type="com.hl.building.domain.tables.CompanyInfo">
        <result property="houseId" column="house_id" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="otherPerson" column="other_person" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="corporate" column="corporate" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="executives" column="executives" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="finance" column="finance" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="company_distribution" resultMap="SqrResultMap">
        select * from company_info c
        where c.is_delete = 0
        <if test="req.isLogout != null and req.isLogout != ''">
            and c.is_logout = #{req.isLogout}
        </if>
        and c.build_id = #{req.buildId}
    </select>

    <select id="selectVoList" resultMap="SqrResultMap2">
        select h.* ,b.build_name from company_info h
        left join building_info b
        on h.build_id = b.build_id
        <where>
            <if test="req.isDelete == 0">
                and h.is_delete = 0
            </if>
            <if test="req.buildId != 0">
                and h.build_id = #{req.buildId}
            </if>
            <if test="req.isFocus == 1">
                and h.is_focus = 1
            </if>
            <if test="req.isLogout == 1">
                and h.is_logout = 1
            </if>
            <if test="req.isLogout == 0">
                and h.is_logout = 0
            </if>
            <if test="req.companyId != null and req.companyId != ''">
                and h.company_id = #{req.companyId}
            </if>
        </where>
        order by b.build_name
    </select>


    <select id="selectCompanyList" resultMap="SqrResultMap3">
        select company_id, company_name, house_id, house_nature
        from company_info
        where is_delete = 0
    </select>

    <select id="selectCountNature" resultMap="SqrResultMap4">
        SELECT house_nature, house_id
        from company_info
        WHERE is_delete = 0
          and build_id = #{req.buildId}
    </select>

    <select id="detail" resultMap="SqrResultMap">
        select *
        from company_info
        where company_id = #{req.companyId}
    </select>

    <select id="selectDelList" resultMap="SqrResultMap4">
        select *
        from company_info
        where is_logout = 1
    </select>
</mapper>
