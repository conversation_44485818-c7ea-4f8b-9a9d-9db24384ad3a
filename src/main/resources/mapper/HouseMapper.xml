<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.HouseMapper">
    <resultMap id="SqrResultMap" type="com.hl.building.domain.vo.HouseInfoVo">
<!--        <result property="personInfo" column="person_info" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>-->
    </resultMap>

    <delete id="deleteUpdHouse">
        delete from house_info where build_id = #{buildId} and create_time not in
        (select create_time from house_info where build_id = #{buildId} order by create_time limit #{houseNum})
    </delete>

<!--    <select id="selectVoList" resultMap="SqrResultMap">-->
<!--        select * from (select h.* ,b.build_name from house_info h-->
<!--        inner join building_info b-->
<!--        on h.build_id = b.build_id) t-->
<!--        where t.is_delete = #{req.isDelete}-->
<!--        <if test="req.query != null and req.query !=''">-->
<!--            and (t.other_person like concat ('%',#{req.query},'%') or t.corporate like concat ('%',#{req.query},'%') or t.executives like concat ('%',#{req.query},'%') or t.finance like concat ('%',#{req.query},'%')-->
<!--            or t.build_name like concat ('%',#{req.query},'%') or t.house_name like concat ('%',#{req.query},'%') or t.house_id like concat ('%',#{req.query},'%'))-->
<!--        </if>-->
<!--        order by t.build_name,t.house_id-->
<!--    </select>-->

    <select id="selectVoList" resultType="com.hl.building.domain.vo.ListHouseVo">
        select id,house_id,address
        from house_info
        where is_delete = 0
        <if test="req.buildId != null and req.buildId != ''">
            and build_id = #{req.buildId}
        </if>
        <if test="req.query != null and req.query != ''">
            and (house_id like concat ('%',#{req.query},'%') or address like concat ('%',#{req.query},'%') )
        </if>
        <if test="ids.size() != 0">
            and
            <foreach collection="ids" item="i" open="(" separator="or" close=")">
                id = #{i}
            </foreach>
        </if>
        order by build_id,house_id
    </select>

    <select id="house_distribution" resultMap="SqrResultMap">
        select * from (select h.* ,b.build_name from house_info h
        inner join building_info b
        on h.build_id = b.build_id
        where h.is_delete = 0) t
        where t.build_id = #{req.buildId}
        order by t.house_id
    </select>


    <select id="detail" resultMap="SqrResultMap">
        select * from (select h.* ,b.build_name from house_info h
        inner join building_info b
        on h.build_id = b.build_id) t
        where t.id = #{req.id}
    </select>




</mapper>
