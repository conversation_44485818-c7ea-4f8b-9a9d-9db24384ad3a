<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.warn.mapper.KgmArchivesMemberMapper">
  <resultMap id="BaseResultMap" type="com.hl.warn.domain.KgmArchivesMember">
    <!--@mbg.generated-->
    <!--@Table kgm_archives_member-->
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="group_uid" jdbcType="VARCHAR" property="groupUid" />
    <result column="create_dept_name" jdbcType="VARCHAR" property="createDeptName" />
    <result column="create_dept_uid" jdbcType="VARCHAR" property="createDeptUid" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_user_uid" jdbcType="VARCHAR" property="createUserUid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="personel_uid" jdbcType="VARCHAR" property="personelUid" />
    <result column="member_status" jdbcType="VARCHAR" property="memberStatus" />
    <result column="ew_deteail_uid" jdbcType="VARCHAR" property="ewDeteailUid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    uuid, group_uid, create_dept_name, create_dept_uid, create_user_name, create_user_uid, 
    create_time, personel_uid, member_status, ew_deteail_uid
  </sql>
</mapper>