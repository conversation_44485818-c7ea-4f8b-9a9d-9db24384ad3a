<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.warn.mapper.WjLyTaskResultCompanyMapper">
  <resultMap id="BaseResultMap" type="com.hl.warn.domain.WjLyTaskResultCompany">
    <!--@mbg.generated-->
    <!--@Table wj_ly_task_result_company-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="details" jdbcType="VARCHAR" property="details" />
    <result column="risk_score" jdbcType="VARCHAR" property="riskScore" />
    <result column="risk_level" jdbcType="VARCHAR" property="riskLevel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, company_name, company_code, task_name, task_id, details, risk_score, risk_level
  </sql>
</mapper>