<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.WjCompanyHouseMapper">


  <select id="selectCompanyHouseList" resultType="com.hl.building.domain.vo.WjCompanyHouseVO">
      SELECT wch.company_id, wch.house_id, wci.company_name, wci.company_code, wci.is_logout, wci.build_id
      FROM wjhl.wj_company_house wch
               left join wjhl.wj_company_info wci on wch.company_id = wci.company_id
      <where>
          <if test="collection != null and collection.size() != 0">
               and wch.house_id in
              <foreach item="item" collection="collection" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
      </where>
      order by company_name
  </select>

  <select id="selectCompanyHouseInfoList" resultType="com.hl.building.domain.vo.WjCompanyHouseRespVO">
      SELECT wch.company_id,
             wch.is_active,
             wch.house_id,
             whi.house_name,
             whi.address,
             whi.floor,
             whi.x_layer,
             whi.y_layer,
             whi.row_num,
             whi.house_nature,
             whi.column_num
      FROM wjhl.wj_company_house wch
               LEFT JOIN wjhl.wj_house_info whi ON wch.house_id = whi.house_id
      <where>
          <if test="collection != null and collection.size() != 0">
              and wch.company_id in
              <foreach item="item" collection="collection" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
      </where>
  </select>

  <select id="countHouseNature" resultType="com.alibaba.fastjson2.JSONObject">
      select count(1) as count, house_nature
      from wjhl.wj_house_info
      where build_id = #{buildId}
      group by house_nature
  </select>
</mapper>