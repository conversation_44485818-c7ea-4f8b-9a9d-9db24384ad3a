<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.WjLyCaseInfoMapper">

    <resultMap id="baseResultMap" type="com.hl.building.domain.WjLyCaseInfo">
        <result property="company" column="company" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result property="building" column="building" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result property="person" column="person" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result property="companyInfo" column="company_info" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result property="buildingInfo" column="building_info" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result property="personInfo" column="person_info" typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
    </resultMap>

    <select id="queryCaseInfoByIdCard" resultType="com.hl.building.domain.WjLyCaseInfo">
        select *
        from harzon.cz_jz_case_info ca
        where ca.case_no in
              (select case_no from harzon.cz_jz_case_person where id_number = #{idCard})
    </select>

    <select id="pageList" resultMap="baseResultMap">
        select *
        from wjhl.wj_ly_case_info
        <where>
            <if test="req.caseNo != null and req.caseNo != ''">
                and case_no like '%' || #{req.caseNo} || '%'
            </if>
            <if test="req.caseMainClass and req.caseMainClass != ''">
                and case_main_class = #{req.caseMainClass}
            </if>
            <if test="req.caseName != null and req.caseName != ''">
                and case_name like '%' || #{req.caseName} || '%'
            </if>
        </where>

    </select>
</mapper>