<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.WjCompanyInfoMapper">

    <!--     and (wci.company_name like concat('%',#{req.query},'%')-->
    <!--     or wbi.build_name like concat('%',#{req.query},'%')-->
    <!--     or wci.company_address like concat('%',#{req.query},'%')-->
    <!--     or wci.company_code like concat('%',#{req.query},'%'))-->
  <select id="pageCompanyList" resultType="com.hl.building.domain.vo.WjCompanyRespVO">
      SELECT wci.*,
             wbi.build_name
      FROM wjhl.wj_company_info wci
               LEFT JOIN wjhl.wj_building_info wbi ON wci.build_id = wbi.build_id
    <where>
      <if test="req.query != null and req.query != ''">
          and (wci.company_name like '%' || #{req.query} || '%'
          or wbi.build_name like '%' || #{req.query} || '%'
          or wci.company_address like '%' || #{req.query} || '%'
          or wci.company_code like '%' || #{req.query} || '%')
      </if>
      <if test="req.buildId != null and req.buildId != ''">
        and wci.build_id = #{req.buildId}
      </if>
      <if test="req.isLogout != null ">
        and wci.is_logout = #{req.isLogout}
      </if>
      <if test="req.isDelete != null ">
        and wci.is_delete = #{req.isDelete}
      </if>
      <if test="req.isFocus != null">
        and wci.is_focus = #{req.isFocus}
      </if>
    </where>
      order by wci.company_id desc
  </select>

  <select id="countCompanyInfo" resultType="com.alibaba.fastjson2.JSONObject">
    SELECT
      COUNT(1) AS company_num,
      SUM(CASE WHEN is_focus = 1 THEN 1 ELSE 0 END) AS focus_num,
      SUM(CASE WHEN is_logout = 1 THEN 1 ELSE 0 END) AS logout_num
    FROM
      wjhl.wj_company_info
    WHERE
      build_id = #{buildId};
    </select>

  <select id="selectCompanyListByBuildId" resultType="com.hl.building.domain.vo.WjCompanyRespVO">
      select *
      from wjhl.wj_company_info wci
      <where>
          <if test="req.buildId != null and req.buildId != ''">
              and wci.build_id = #{req.buildId}
          </if>
          <if test="req.isLogout != null">
              and wci.is_logout = #{req.isLogout}
          </if>
      </where>
  </select>
</mapper>