<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.WjJqDataSourceMapper">
    
    <select id="getPoliceData" resultType="com.alibaba.fastjson2.JSONObject">
        select *
        from wjsc_jq_jjxx jj left join wjsc_jq_cjxx cj on jj.jjbh = cj.jjbh where
            cj.cjdw like '320412%'
    </select>
    
    <select id="queryPersonPoliceData" resultType="com.hl.building.domain.vo.PoliceIncidentInfo">
        select jj.jjbh,
               cjbs,
               bjr,
               bjlx,
               jjdwmc,
               bjdhsj_time,
               jjdw,
               lxdh,
               bjnr,
               djdwmc,
               sfdd,
               bjxs,
               jjdbh,
               bjdhsj,
               jjrqsj,
               jqdj,
               gis_x,
               gis_y,
               cj.cjsj_time,
               cjlb,
               cjdw,
               sfcs,
               ssxxqk,
               bccljg,
               sfsjxx_time,
               sfxq,
               cljgnr,
               sfsjsx_time,
               djr,
               jqsx,
               tqqk,
               cjbh,
               cjxz,
               cjdwmc,
               cjjg,
               cjsj,
               sfsjxx,
               sfsjsx,
               ssxq,
               cjr,
               cjjqbq,
               bzdzmc,
               qylb,
               cjxxdd,
               xzb,
               yzb,
               jqbz,
               fjbq,
               bzzt,
               addressM,
               personM,
               resultM,
               timeM,
               toolM,
               reasonM,
               unit,
               spjg,
               spr,
               sp_time,
               spnr,
               bzr,
               bz_time,
               mark,
               bzrxm,
               sprxm,
               fsyy,
               baidu,
               dz_type,
               dsdz,
               jgbh,
               dzid,
               dwmc,
               dwdz,
               xq,
               build_no,
               zrq
        from wjsc_jq_jjxx jj
                 left join wjsc_jq_cjxx cj on jj.jjbh = cj.jjbh
        where jj.jjbh in (select jjbh from wjsc_jq_sjxx where gmsfhm = #{id_card});
    </select>



    <select id="queryJjbhByJqLabel" resultType="java.lang.String">
           select jjbh
           from wj_jq_label where label in
           <foreach collection="jqLabel" item="label" separator="," open="(" close=")">
               #{label}
           </foreach>
       </select>

    <select id="queryJqInfoByJqLabel" resultType="com.hl.building.domain.WjLyJqxx">
           select jj.jjbh,
                  cjbs,
                  bjr,
                  bjlx,
                  jjdwmc,
                  bjdhsj_time,
                  jjdw,
                  lxdh,
                  bjnr,
                  djdwmc,
                  sfdd,
                  bjxs,
                  jjdbh,
                  bjdhsj,
                  jjrqsj,
                  jqdj,
                  gis_x,
                  gis_y,
                  cj.cjsj_time,
                  cjlb,
                  cjdw,
                  sfcs,
                  ssxxqk,
                  bccljg,
                  sfsjxx_time,
                  sfxq,
                  cljgnr,
                  sfsjsx_time,
                  djr,
                  jqsx,
                  tqqk,
                  cjbh,
                  cjxz,
                  cjdwmc,
                  cjjg,
                  cjsj,
                  sfsjxx,
                  sfsjsx,
                  ssxq,
                  cjr,
                  cjjqbq,
                  bzdzmc,
                  qylb,
                  cjxxdd,
                  xzb,
                  yzb,
                  unit,
                  spjg,
                  spr,
                  sp_time,
                  spnr,
                  bzr,
                  bz_time,
                  mark,
                  bzrxm,
                  sprxm,
                  fsyy,
                  baidu,
                  dz_type,
                  dsdz,
                  jgbh,
                  dzid,
                  dwmc,
                  dwdz,
                  xq,
                  build_no,
                  zrq
           from wjsc_jq_jjxx jj
                       left join wjsc_jq_cjxx cj on jj.jjbh = cj.jjbh
           where jj.jjbh in (
               select jl.jjbh
               from wj_jq_label jl
               where label in
               <foreach collection="param.jqLabel" item="label" separator="," open="(" close=")">
                   #{label}
               </foreach>
           )
           order by bjdhsj_time desc
    </select>

    <select id="queryJqInfoByIdCard" resultType="com.hl.building.domain.WjLyJqxx">
           select jj.jjbh,
           cjbs,
           bjr,
           bjlx,
           jjdwmc,
           bjdhsj_time,
           jjdw,
           lxdh,
           bjnr,
           djdwmc,
           sfdd,
           bjxs,
           jjdbh,
           bjdhsj,
           jjrqsj,
           jqdj,
           gis_x,
           gis_y,
           cj.cjsj_time,
           cjlb,
           cjdw,
           sfcs,
           ssxxqk,
           bccljg,
           sfsjxx_time,
           sfxq,
           cljgnr,
           sfsjsx_time,
           djr,
           jqsx,
           tqqk,
           cjbh,
           cjxz,
           cjdwmc,
           cjjg,
           cjsj,
           sfsjxx,
           sfsjsx,
           ssxq,
           cjr,
           cjjqbq,
           bzdzmc,
           qylb,
           cjxxdd,
           xzb,
           yzb,
           unit,
           spjg,
           spr,
           sp_time,
           spnr,
           bzr,
           bz_time,
           mark,
           bzrxm,
           sprxm,
           fsyy,
           baidu,
           dz_type,
           dsdz,
           jgbh,
           dzid,
           dwmc,
           dwdz,
           xq,
           build_no,
           zrq
           from wjsc_jq_jjxx jj
           left join wjsc_jq_cjxx cj on jj.jjbh = cj.jjbh
           where jj.jjbh in (select jjbh from  wjsc_jq_sjxx where gmsfhm = #{idCard})
           order by bjdhsj_time desc
    </select>

    <select id="queryJqInfoByJgbhList" resultType="com.hl.building.domain.WjLyJqxx">
           select jj.jjbh,
                  cjbs,
                  bjr,
                  bjlx,
                  jjdwmc,
                  bjdhsj_time,
                  jjdw,
                  lxdh,
                  bjnr,
                  djdwmc,
                  sfdd,
                  bjxs,
                  jjdbh,
                  bjdhsj,
                  jjrqsj,
                  jqdj,
                  gis_x,
                  gis_y,
                  cj.cjsj_time,
                  cjlb,
                  cjdw,
                  sfcs,
                  ssxxqk,
                  bccljg,
                  sfsjxx_time,
                  sfxq,
                  cljgnr,
                  sfsjsx_time,
                  djr,
                  jqsx,
                  tqqk,
                  cjbh,
                  cjxz,
                  cjdwmc,
                  cjjg,
                  cjsj,
                  sfsjxx,
                  sfsjsx,
                  ssxq,
                  cjr,
                  cjjqbq,
                  bzdzmc,
                  qylb,
                  cjxxdd,
                  xzb,
                  yzb,
                  unit,
                  spjg,
                  spr,
                  sp_time,
                  spnr,
                  bzr,
                  bz_time,
                  mark,
                  bzrxm,
                  sprxm,
                  fsyy,
                  baidu,
                  dz_type,
                  dsdz,
                  jgbh,
                  dzid,
                  dwmc,
                  dwdz,
                  xq,
                  build_no,
                  zrq
           from wjsc_jq_jjxx jj
           left join wjsc_jq_cjxx cj on jj.jjbh = cj.jjbh
           where cj.jgbh in
           <foreach collection="jgbhList" item="jgbh" separator="," open="(" close=")">
               #{jgbh}
           </foreach>
           order by bjdhsj_time desc
    </select>
</mapper>
