<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.WjBuildingInfoMapper">

    <select id="selectBuildingList" resultType="com.hl.building.domain.vo.WjBuildingInfoRespVO">
        SELECT wbi.*,
               (SELECT count(1)
                FROM wjhl.wj_company_info wci
                WHERE wci.build_id = wbi.build_id) AS company_num,
               (SELECT count(1)
                FROM wjhl.wj_house_info whi
                WHERE whi.build_id = wbi.build_id) AS house_num
        FROM wjhl.wj_building_info wbi
        <where>
            <if test="req.query != null and req.query != ''">
                and (wbi.build_name like '%'|| #{req.query} || '%'
                or wbi.address like '%' || #{req.query} || '%')
            </if>
        </where>
        order by wbi.build_id desc
    </select>
</mapper>