<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.ViewPersonalTubeInfoMapper">

  <select id="pageList" resultType="com.hl.building.domain.ViewPersonalTubeInfo">
    select *
    from harzon.view_personal_tube_info
    <where>
        <if test="req.gmsfhm != null and req.gmsfhm != ''">
            and gmsfhm like '%' || #{req.gmsfhm} || '%'
        </if>
        <if test="req.xm != null and req.xm != ''">
            and xm like '%' || #{req.xm} || '%'
        </if>
        <if test="req.gljbdm != null and req.gljbdm.size() >0 ">
            and gljbdm in
            <foreach collection="req.gljbdm" item="gljbdm" open="(" separator="," close=")">
                #{gljbdm}
            </foreach>
        </if>
        <if test="req.zdryglztdm != null and req.zdryglztdm.size() >0">
            and zdryglztdm in
            <foreach collection="req.zdryglztdm" item="zdryglztdm" open="(" separator="," close=")">
                #{zdryglztdm}
            </foreach>
        </if>
        <if test="req.xgldwGajgmc != null  and  req.xgldwGajgmc != ''">
            and xgldw_gajgmc like '%' || #{req.xgldwGajgmc} || '%'
        </if>
        <if test="req.zdryjzlbdmList != null  and req.zdryjzlbdmList.size() > 0">
            and zdryjzlbdm in
            <foreach collection="req.zdryjzlbdmList" item="zdryjzlbdm" open="(" separator="," close=")">
                #{zdryjzlbdm}
            </foreach>
        </if>
        <if test="req.zdrysflbdmList != null  and req.zdrysflbdmList.size() > 0">
            and zdrylbdm in
            <foreach collection="req.zdrysflbdmList" item="zdrysflbdm" open="(" separator="," close=")">
                #{zdrysflbdm}
            </foreach>
        </if>
        <if test="req.controlTimeStart != null and req.controlTimeStart != ''">
            and lgsj_rqsj &gt;= #{req.controlTimeStart}
        </if>
        <if test="req.controlTimeEnd != null  and  req.controlTimeEnd != ''">
            and lgsj_rqsj &lt;= #{req.controlTimeEnd}
        </if>
        <if test="req.cancelTimeStart != null and req.cancelTimeStart != ''">
            and cgsj_rqsj &gt;= #{req.cancelTimeStart}
        </if>
        <if test="req.cancelTimeEnd != null  and req.cancelTimeEnd != ''">
            and cgsj_rqsj &lt;= #{req.cancelTimeEnd}
        </if>
    </where>
  </select>
</mapper>