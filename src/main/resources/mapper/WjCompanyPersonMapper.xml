<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.WjCompanyPersonMapper">
  <resultMap id="BaseResultMap" type="com.hl.building.domain.WjCompanyPerson">
    <!--@mbg.generated-->
    <!--@Table wj_company_person-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="person_id" jdbcType="VARCHAR" property="personId" />
    <result column="position" jdbcType="INTEGER" property="position" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, company_id, person_id, `position`, is_delete
  </sql>

  <select id="selectCompanyPersonList" resultType="com.hl.building.domain.vo.WjCompanyPersonRespVO">
      SELECT wcp.company_id,
             wcp.person_id,
             wcp.position,
             wpi.name,
             wpi.id_card,
             wpi.phone,
             wpi.domicile,
             wpi.current
      FROM wjhl.wj_company_person wcp
               LEFT JOIN wjhl.wj_person_info wpi ON wcp.person_id = wpi.person_id
      <where>
          <if test="collection != null and collection.size() != 0">
              and wcp.company_id in
              <foreach collection="collection" item="item" open="(" separator="," close=")">
                  #{item}
              </foreach>
          </if>
      </where>
  </select>
</mapper>