<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.WjPersonInfoMapper">

    <select id="countPersonCountByCompanyId" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT company_id ,
        COUNT(1) AS count
        FROM wjhl.wj_person_info
        WHERE company_id in
        <foreach collection="companyIdList" item="companyId" separator="," open="(" close=")">
            #{companyId}
        </foreach>
        GROUP BY company_id
    </select>

    <select id="selectPersonList" resultType="com.hl.building.domain.vo.WjPersonInfoRespVO">
        SELECT wpi.person_id , wpi.name, wpi.id_card, wpi.phone, wpi.hjxz, wpi.xzz, wpi.is_focus,
        wpi.create_user, wpi.create_time, wpi.update_user, wpi.update_time,
        wpi.is_delete, wpi.zw_mc, wpi.jgbh, wpi.company_id,
        wci.company_name,
        wbi.build_name
        FROM wjhl.wj_person_info wpi
        LEFT JOIN wjhl.wj_company_info wci ON wpi.company_id = wci.company_id
        LEFT JOIN wjhl.wj_building_info wbi ON wci.build_id = wbi.build_id
        <where>
            <if test="req.name != null and req.name != ''">
                and wpi.name like '%' || #{req.name} || '%'
            </if>
            <if test="req.idCard != null and req.idCard != ''">
                and wpi.id_card like '%' || #{req.idCard} || '%'
            </if>
            <if test="req.companyName != null and req.companyName != ''">
                and wci.company_name like '%' || #{req.companyName} || '%'
            </if>
            <if test="req.buildName != null and req.buildName != ''">
                and wbi.build_name like '%' || #{req.buildName} || '%'
            </if>
        </where>
        order by person_id desc
    </select>
</mapper>