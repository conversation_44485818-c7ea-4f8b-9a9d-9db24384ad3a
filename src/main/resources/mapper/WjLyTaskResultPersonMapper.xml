<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.warn.mapper.WjLyTaskResultPersonMapper">
  <resultMap id="BaseResultMap" type="com.hl.warn.domain.WjLyTaskResultPerson">
    <!--@mbg.generated-->
    <!--@Table wj_ly_task_result_person-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="gmsfhm" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="xm" jdbcType="VARCHAR" property="xm" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="risk_score" jdbcType="VARCHAR" property="riskScore" />
    <result column="details" jdbcType="VARCHAR" property="details" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, gmsfhm, xm, task_name, task_id, risk_score, details
  </sql>
</mapper>