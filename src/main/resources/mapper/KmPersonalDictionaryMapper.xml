<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.KmPersonalDictionaryMapper">
  <resultMap id="BaseResultMap" type="com.hl.building.domain.KmPersonalDictionary">
    <!--@mbg.generated-->
    <!--@Table km_personal_dictionary-->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="glid" jdbcType="VARCHAR" property="glid" />
    <result column="nbbsf" jdbcType="VARCHAR" property="nbbsf" />
    <result column="zdlbdm" jdbcType="VARCHAR" property="zdlbdm" />
    <result column="zdlbmc" jdbcType="VARCHAR" property="zdlbmc" />
    <result column="zddm" jdbcType="VARCHAR" property="zddm" />
    <result column="zdmc" jdbcType="VARCHAR" property="zdmc" />
    <result column="fzddm" jdbcType="VARCHAR" property="fzddm" />
    <result column="fzdmc" jdbcType="VARCHAR" property="fzdmc" />
    <result column="gflylb" jdbcType="VARCHAR" property="gflylb" />
    <result column="sm" jdbcType="VARCHAR" property="sm" />
    <result column="zt" jdbcType="VARCHAR" property="zt" />
    <result column="xh" jdbcType="VARCHAR" property="xh" />
    <result column="qyrq" jdbcType="TIMESTAMP" property="qyrq" />
    <result column="zxrq" jdbcType="TIMESTAMP" property="zxrq" />
    <result column="djdw_gajgjgdm" jdbcType="VARCHAR" property="djdwGajgjgdm" />
    <result column="djdw_gajgmc" jdbcType="VARCHAR" property="djdwGajgmc" />
    <result column="djr_yhm" jdbcType="VARCHAR" property="djrYhm" />
    <result column="djsj" jdbcType="TIMESTAMP" property="djsj" />
    <result column="sjgsdwmc" jdbcType="VARCHAR" property="sjgsdwmc" />
    <result column="djbgsj" jdbcType="TIMESTAMP" property="djbgsj" />
    <result column="djbgbs" jdbcType="VARCHAR" property="djbgbs" />
    <result column="shcjbs" jdbcType="VARCHAR" property="shcjbs" />
    <result column="sjjzbs" jdbcType="VARCHAR" property="sjjzbs" />
    <result column="jlmj" jdbcType="VARCHAR" property="jlmj" />
    <result column="sjgsry" jdbcType="VARCHAR" property="sjgsry" />
    <result column="sjgsdwdm" jdbcType="VARCHAR" property="sjgsdwdm" />
    <result column="jc" jdbcType="VARCHAR" property="jc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, glid, nbbsf, zdlbdm, zdlbmc, zddm, zdmc, fzddm, fzdmc, gflylb, sm, zt, xh, qyrq, 
    zxrq, djdw_gajgjgdm, djdw_gajgmc, djr_yhm, djsj, sjgsdwmc, djbgsj, djbgbs, shcjbs, 
    sjjzbs, jlmj, sjgsry, sjgsdwdm, jc
  </sql>
</mapper>