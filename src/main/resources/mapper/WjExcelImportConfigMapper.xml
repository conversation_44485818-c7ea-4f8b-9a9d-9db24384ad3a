<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.WjExcelImportConfigMapper">
  <resultMap id="BaseResultMap" type="com.hl.building.domain.WjExcelImportConfig">
    <!--@mbg.generated-->
    <!--@Table wj_excel_import_config-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, "name", "table_name", create_time, remark
  </sql>
</mapper>