<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.PersonMapper">


    <select id="selectVoList" resultType="com.hl.building.domain.tables.PersonInfo">
        select * from person_info
        <where>
            <if test="ids.size() != 0">
                and
                <foreach collection="ids" item="i" open="(" separator="or" close=")">
                    id = #{i}
                </foreach>
            </if>
            <if test="req.query !=null and req.query != ''">
                and ( name like concat('%',#{req.query},"%") or id_card like concat('%',#{req.query},"%"))
            </if>
            <if test="req.isCorporate == 1">
                and is_corporate = 1
            </if>
            <if test="req.isFocus == 1">
                and is_focus = 1
            </if>
        </where>
        group by name
    </select>
</mapper>
