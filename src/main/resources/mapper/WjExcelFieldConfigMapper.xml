<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.WjExcelFieldConfigMapper">
  <resultMap id="BaseResultMap" type="com.hl.building.domain.WjExcelFieldConfig">
    <!--@mbg.generated-->
    <!--@Table wj_excel_field_config-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="import_config_id" jdbcType="INTEGER" property="importConfigId" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="column_name" jdbcType="VARCHAR" property="columnName" />
    <result column="data_type" jdbcType="VARCHAR" property="dataType" />
    <result column="field_order" jdbcType="INTEGER" property="fieldOrder" />
    <result column="is_required" jdbcType="BOOLEAN" property="isRequired" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, import_config_id, field_name, "column_name", data_type, field_order, is_required
  </sql>
</mapper>