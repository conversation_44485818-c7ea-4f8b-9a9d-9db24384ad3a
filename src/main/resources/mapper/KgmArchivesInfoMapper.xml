<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.KgmArchivesInfoMapper">
  <resultMap id="BaseResultMap" type="com.hl.building.domain.KgmArchivesInfo">
    <!--@mbg.generated-->
    <!--@Table kgm_archives_info-->
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="create_dept_name" jdbcType="VARCHAR" property="createDeptName" />
    <result column="create_dept_uid" jdbcType="VARCHAR" property="createDeptUid" />
    <result column="group_number" jdbcType="VARCHAR" property="groupNumber" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_user_uid" jdbcType="VARCHAR" property="createUserUid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="group_public_kind" jdbcType="VARCHAR" property="groupPublicKind" />
    <result column="group_level" jdbcType="VARCHAR" property="groupLevel" />
    <result column="group_state" jdbcType="VARCHAR" property="groupState" />
    <result column="group_source" jdbcType="VARCHAR" property="groupSource" />
    <result column="ew_group_uid" jdbcType="VARCHAR" property="ewGroupUid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    uuid, create_dept_name, create_dept_uid, group_number, create_user_name, create_user_uid, 
    create_time, group_name, group_public_kind, group_level, group_state, group_source, 
    ew_group_uid
  </sql>
</mapper>