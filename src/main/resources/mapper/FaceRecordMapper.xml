<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.building.mapper.FaceRecordMapper">

    <select id="getFaceRecord" resultType="com.alibaba.fastjson2.JSONObject">
        select * from wjhl.view_camera_face_record
        where similarity >= '93'
        <if test="param.deviceInfo != null and param.deviceInfo.size > 0">
            and camera_id in
            <foreach collection="param.deviceInfo" item="camera_id" open="(" separator="," close=")">
                #{camera_id}
            </foreach>
        </if>
    </select>

    <select id="queryPoliceData" resultType="com.alibaba.fastjson2.JSONObject">
        select gmsfhm,count(1) as count from harzon.wjsc_cz_sjxx where gmsfhm in
        <foreach collection="idCardList" item="idCard" separator="," open="(" close=")">
            #{idCard}
        </foreach>
        group by gmsfhm
    </select>

    <select id="queryCaseData" resultType="com.alibaba.fastjson2.JSONObject">
        select id_number,count(1) as count from harzon.cz_jz_case_person where id_number in
        <foreach collection="idCardList" item="idCard" separator="," open="(" close=")">
            #{idCard}
        </foreach>
        group by id_number
    </select>

    <select id="queryPersonPoliceRecord" resultType="com.alibaba.fastjson2.JSONObject">
    </select>

    <select id="queryPersonCaseRecord" resultType="com.alibaba.fastjson2.JSONObject">
        select *
        from harzon.cz_jz_case_info
        where case_no in (select case_no
                          from harzon.cz_jz_case_person
                          where id_number = #{id_card})
    </select>
</mapper>
