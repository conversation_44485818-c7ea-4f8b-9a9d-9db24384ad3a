<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.warn.mapper.KmPersonalBaseInfoMapper">

  <select id="getPersonalBaseInfoByGroupId" resultType="com.hl.warn.domain.KmPersonalBaseInfo">
      select *
      from harzon.km_personal_base_info
      where rkbm in (select personel_uid
                     from harzon.kgm_archives_member where group_uid in
      <foreach collection="list" item="item" separator="," open="(" close=")">
          #{item}
      </foreach>
      )
  </select>
</mapper>