<?xml version="1.0" encoding="UTF-8"?>
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，
你会看到log4j2内部各种详细输出-->
<!-- monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数 -->
<!-- vm加上：-Dlog4j.skipJansi=false -->
<configuration status="info" monitorInterval="30">
    <Properties>
        <Property name="LOG_HOME"><![CDATA[logs]]></Property>
        <property name="file_pattern">%d{MM-dd HH:mm:ss}[%c:%L|%p] %m%n</property>
        <Property name="console_pattern">%highlight{%d{MM-dd HH:mm:ss}} %highlight{%5p} %highlight{%l}: %m%n</Property>
    </Properties>

    <!--先定义所有的appender-->
    <appenders>
        <!--这个输出控制台的配置-->
        <console name="CONSOLE" target="SYSTEM_OUT">
            <!--输出日志的格式-->
            <PatternLayout pattern="${console_pattern}"/>
            <ThresholdFilter level="info" onMatch="NEUTRAL" onMismatch="DENY"/>
        </console>

        <RollingFile name="FILEWARN" fileName="./logs/warn.log" immediateFlush="true"
                     filePattern="./logs/$${date:yyyy-MM}/warn-%d{yyyy-MM-dd}-%i.log"
                     bufferSize="262144">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <Filters>
                <ThresholdFilter level="warn" onMatch="NEUTRAL" onMismatch="DENY"/>
                <NoMarkerFilter onMatch="NEUTRAL" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${file_pattern}"/>
            <Policies>
<!--                <TimeBasedTriggeringPolicy/>-->
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件，这里设置了24 -->
            <DefaultRolloverStrategy max="24"/>
        </RollingFile>
        <RollingFile name="FILEERROR" fileName="./logs/error.log" immediateFlush="true"
                     filePattern="./logs/$${date:yyyy-MM}/error-%d{yyyy-MM-dd}-%i.log"
                     bufferSize="262144">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <Filters>
                <ThresholdFilter level="error" onMatch="NEUTRAL" onMismatch="DENY"/>
                <NoMarkerFilter onMatch="NEUTRAL" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${file_pattern}"/>
            <Policies>
<!--                <TimeBasedTriggeringPolicy/>-->
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件，这里设置了24 -->
            <DefaultRolloverStrategy max="24"/>
        </RollingFile>

        <!-- 配置日志输出文件名字     追加读写     host地址  端口    -->
        <Socket name="SOCKET" host="**************" port="9992" protocol="UDP">
            <ThresholdFilter level="warn" onMatch="NEUTRAL" onMismatch="DENY"/>
            <JsonLayout compact="true" eventEol="false" locationInfo="true">
                <KeyValuePair key="serverTitle" value="sso_hw"/>
            </JsonLayout>
        </Socket>
    </appenders>

    <!--然后定义logger，只有定义了logger并引入appender，appender才会生效-->
    <loggers>
        <!-- 日志输出级别，共有8个级别，按照从低到高为：All < Trace < Debug < Info < Warn < Error < Fatal < OFF. -->
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <!-- <appender-ref ref="SOCKET"/>-->
            <appender-ref ref="FILEWARN"/>
            <appender-ref ref="FILEERROR"/>
        </root>

        <logger name="org.springframework.boot.actuate.jdbc.DataSourceHealthIndicator" level="error"/>
    </loggers>
</configuration>