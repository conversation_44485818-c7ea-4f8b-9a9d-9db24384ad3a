package com.hl.rule.service;

import com.hl.rule.domain.WjJqAnalysisResult;
import com.hl.rule.domain.WjJqAnalysisRules;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AnalysisService {

    private final RuleEngine ruleEngine;

    private final Map<String, ContextBuilder> contextBuilders = new HashMap<>();

    @PostConstruct
    public void init() {
        contextBuilders.put("person", this::buildPersonContext);
    }

    @Transactional
    public boolean analyze(String identifier, WjJqAnalysisRules rules) {
        AnalysisContext context = buildContext(identifier, rules);
        boolean result = ruleEngine.evaluate(context);

        if (result) {
            saveAnalysisResult(identifier, rules, context);
        }
        return result;
    }

    private void saveAnalysisResult(String identifier, WjJqAnalysisRules rules, AnalysisContext context) {
        try {

        } catch (Exception e) {
            log.error("保存分析结果失败", e);
            throw new RuntimeException("保存分析结果失败", e);
        }
    }

    private void saveAnalysisDetails(WjJqAnalysisResult analysisResult, AnalysisContext context, WjJqAnalysisRules rules) {

    }

    private AnalysisContext buildContext(String identifier, WjJqAnalysisRules rules) {
        ContextBuilder builder = contextBuilders.get(rules.getRuleType());
        if (builder == null) {
            throw new IllegalArgumentException("Unsupported rule type: " + rules.getRuleType());
        }
        return builder.build(identifier, rules);
    }

    private AnalysisContext buildPersonContext(String idCard, WjJqAnalysisRules rules) {

        AnalysisContext context = new AnalysisContext();

        return context;
    }


}
