package com.hl.rule.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.hl.rule.domain.WjJqAnalysisRules;
import com.hl.rule.mapper.WjJqAnalysisRulesMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class WjJqAnalysisRulesService extends ServiceImpl<WjJqAnalysisRulesMapper, WjJqAnalysisRules> {


//    private final WjJqAnalysisRulesMapper wjJqAnalysisRulesMapper;
//
//    public Page<WjJqAnalysisRules> pageRules(WjJqAnalysisRulesReqVO analysisRulesReqVO) {
//
//        Page<WjJqAnalysisRules> page = wjJqAnalysisRulesMapper.page(new Page<>(analysisRulesReqVO.getPage(), analysisRulesReqVO.getLimit()), analysisRulesReqVO);
//
//        return page;
//    }
}
