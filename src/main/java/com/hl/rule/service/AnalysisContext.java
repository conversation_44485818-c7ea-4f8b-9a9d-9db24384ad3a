package com.hl.rule.service;


import com.hl.rule.domain.WjJqAnalysisRules;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnalysisContext {
    /**
     * 分析目标标识（身份证号/警情编号/电话号码/地址）
     */
    private String identifier;

    /**
     * 规则配置
     */
    private WjJqAnalysisRules rules;


}