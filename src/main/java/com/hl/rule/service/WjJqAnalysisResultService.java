package com.hl.rule.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.hl.rule.domain.WjJqAnalysisResult;
import com.hl.rule.mapper.WjJqAnalysisResultMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WjJqAnalysisResultService extends ServiceImpl<WjJqAnalysisResultMapper, WjJqAnalysisResult> {



//    private final WjJqAnalysisResultMapper wjJqAnalysisResultMapper;
//
//
//    private final WjJqAnalysisRulesService wjJqAnalysisRulesService;
//
//    private final WjJqAnalysisDetailMapper wjJqAnalysisDetailMapper;
//
//    private final WjscJqSjxxService wjscJqSjxxService;
//
//    public Page<WjJqAnalysisResultResVO> pageResult(WjJqAnalysisResultReqVO analysisResultReqVO) {
//
//        if (StringUtils.isNotBlank(analysisResultReqVO.getJqStartTime())) {
//            analysisResultReqVO.setJqStartTime(DateUtil.format(DateUtil.parse(analysisResultReqVO.getJqStartTime()), "yyyyMMddHHmmss"));
//        }
//        if (StringUtils.isNotBlank(analysisResultReqVO.getJqEndTime())) {
//            analysisResultReqVO.setJqEndTime(DateUtil.format(DateUtil.parse(analysisResultReqVO.getJqStartTime()), "yyyyMMddHHmmss"));
//        }
//        if (StringUtils.isNotBlank(analysisResultReqVO.getWarningUnit())){
//            analysisResultReqVO.setWarningUnit(analysisResultReqVO.getWarningUnit().substring(0,8));
//        }
//
//        // 1. 获取分页数据
//        Page<WjJqAnalysisResultResVO> page = wjJqAnalysisResultMapper.pageResultByCountOrder(
//                new Page<>(analysisResultReqVO.getPage(), analysisResultReqVO.getLimit()),
//                analysisResultReqVO
//        );
//
//        if (!page.getRecords().isEmpty()) {
//            page.getRecords().forEach(item -> {
//                String latestWarnTime = item.getLatestWarnTime();
//                if (StringUtils.isNotBlank(latestWarnTime)) {
//                    try {
//                        DateTime parse = DateUtil.parse(latestWarnTime);
//                        item.setLatestWarnTime(DateUtil.format(parse, "yyyy-MM-dd HH:mm:ss"));
//                    } catch (Exception e) {
//                        log.error(e.getMessage());
//                    }
//                }
//            });
//        }
//
//        // 2. 填充统计数据
////        fillStatisticsData(page.getRecords());
//
//        return page;
//    }
//
//    public List<WjPersonWarningDetailVO> getDetail(JSONObject param) {
//        String gmsfhm = param.getString("gmsfhm");
//
//        // 1. 使用一次查询获取所有预警记录
//        List<Map<String, Object>> results = this.listMaps(
//                Wrappers.<WjJqAnalysisResult>lambdaQuery()
//                        .select(
//                                WjJqAnalysisResult::getId,
//                                WjJqAnalysisResult::getRuleId,
//                                WjJqAnalysisResult::getWarningUnit,
//                                WjJqAnalysisResult::getCreateTime
//                        )
//                        .like(WjJqAnalysisResult::getLinkData, gmsfhm)
//                        .orderByDesc(WjJqAnalysisResult::getCreateTime)
//        );
//
//        if (results.isEmpty()) {
//            return new ArrayList<>();
//        }
//
//        // 2. 收集所有结果ID和规则ID
//        Set<String> resultIds = new HashSet<>();
//        Set<String> ruleIds = new HashSet<>();
//        for (Map<String, Object> result : results) {
//            resultIds.add((String) result.get("id"));
//            ruleIds.add((String) result.get("rule_id"));
//        }
//
//        // 3. 批量查询规则信息
//        Map<String, String> ruleNames = wjJqAnalysisRulesService.listByIds(ruleIds)
//                .stream()
//                .collect(Collectors.toMap(
//                        WjJqAnalysisRules::getId,
//                        WjJqAnalysisRules::getRuleName
//                ));
//
//        // 4. 批量查询详情信息
//        Map<String, List<WjJqAnalysisDetail>> detailsMap = wjJqAnalysisDetailMapper.selectDetailsByResultIds(resultIds)
//                .stream()
//                .collect(Collectors.groupingBy(WjJqAnalysisDetail::getResultId));
//
//        // 5. 组装返回数据
//        List<WjPersonWarningDetailVO> warningVOs = new ArrayList<>(results.size());
//        for (Map<String, Object> result : results) {
//            String resultId = (String) result.get("id");
//            String ruleId = (String) result.get("rule_id");
//            WjPersonWarningDetailVO vo = new WjPersonWarningDetailVO();
//            vo.setId(resultId);
//            vo.setRuleId(ruleId);
//            vo.setRuleName(ruleNames.get(ruleId));
//            vo.setWarningUnit((String) result.get("warning_unit"));
//            vo.setCreateTime((LocalDateTime) result.get("create_time"));
//            List<WjJqAnalysisDetail> details = detailsMap.getOrDefault(resultId, Collections.emptyList());
//            vo.setMatchCount((long) details.size());
//            vo.setDetails(details);
//            warningVOs.add(vo);
//        }
//        return warningVOs;
//    }
//
//
//    public void exportRuleResult(WjJqAnalysisResultReqVO analysisResultReqVO, HttpServletResponse response) {
//        try {
//            // 1. 获取所有数据（不分页）
//            Page<WjJqAnalysisResultResVO> page = wjJqAnalysisResultMapper.pageResult(
//                    new Page<>(1, Integer.MAX_VALUE),
//                    analysisResultReqVO
//            );
//            List<WjJqAnalysisResultResVO> records = page.getRecords();
//
//            if (records.isEmpty()) {
//                return;
//            }
//            // 2. 填充统计数据
//            fillStatisticsData(records);
//
//            // 3. 导出Excel
//            exportToExcel(records, response);
//
//        } catch (Exception e) {
//            log.error("导出预警结果失败", e);
//            throw new RuntimeException("导出预警结果失败", e);
//        }
//    }
//
//    /**
//     * 填充统计数据
//     */
//    private void fillStatisticsData(List<WjJqAnalysisResultResVO> records) {
//        if (records.isEmpty()) {
//            return;
//        }
//
//        // 1. 获取所有结果ID
//        List<String> resultIds = records.stream()
//                .map(WjJqAnalysisResultResVO::getId)
//                .collect(Collectors.toList());
//
//        // 2. 批量查询详情记录获取匹配规则数
//        Map<String, Long> matchCountMap = wjJqAnalysisDetailMapper.selectList(
//                Wrappers.<WjJqAnalysisDetail>lambdaQuery()
//                        .in(WjJqAnalysisDetail::getResultId, resultIds)
//        ).stream().collect(
//                Collectors.groupingBy(
//                        WjJqAnalysisDetail::getResultId,
//                        Collectors.counting()
//                )
//        );
//
//        // 3. 获取人员涉警次数
//        Map<String, Integer> personTotalCountMap = getPersonTotalCountMap(records);
//
//        // 4. 设置统计数据
//        for (WjJqAnalysisResultResVO vo : records) {
//            vo.setMatchCount(matchCountMap.getOrDefault(vo.getId(), 0L).intValue());
//            if ("person".equals(vo.getRuleType())) {
//                vo.setTotalCount(personTotalCountMap.getOrDefault(vo.getLinkData(), 0));
//            } else {
//                vo.setTotalCount(vo.getMatchCount());
//            }
//            // 转换一下时间
//            String latestWarnTime = vo.getLatestWarnTime();
//            if (StringUtils.isNotBlank(latestWarnTime)) {
//                try {
//                    DateTime parse = DateUtil.parse(latestWarnTime);
//                    vo.setLatestWarnTime(DateUtil.format(parse, "yyyy-MM-dd HH:mm:ss"));
//                } catch (Exception e) {
//                    log.error(e.getMessage());
//                }
//            }
//        }
//    }
//
//    /**
//     * 获取人员涉警次数统计
//     */
//    private Map<String, Integer> getPersonTotalCountMap(List<WjJqAnalysisResultResVO> records) {
//        // 收集所有人员身份证号
//        Set<String> personIds = records.stream()
//                .filter(vo -> "person".equals(vo.getRuleType()))
//                .map(WjJqAnalysisResultResVO::getLinkData)
//                .collect(Collectors.toSet());
//
//        if (personIds.isEmpty()) {
//            return Collections.emptyMap();
//        }
//
//        // 批量查询人员涉警次数
//        List<Map<String, Object>> countResults = wjscJqSjxxService.listMaps(
//                Wrappers.query(WjscJqSjxx.class)
//                        .select("gmsfhm, COUNT(1) as count")
//                        .in("gmsfhm", personIds)
//                        .groupBy("gmsfhm")
//        );
//
//        Map<String, Integer> personTotalCountMap = new HashMap<>();
//        for (Map<String, Object> result : countResults) {
//            String gmsfhm = (String) result.get("gmsfhm");
//            Long count = (Long) result.get("count");
//            personTotalCountMap.put(gmsfhm, count.intValue());
//        }
//        return personTotalCountMap;
//    }
//
//    /**
//     * 导出到Excel
//     */
//    private void exportToExcel(List<WjJqAnalysisResultResVO> records, HttpServletResponse response) throws Exception {
//        // 1. 准备Excel数据
//        List<List<String>> rows = new ArrayList<>();
//
//        // 添加数据行
//        for (WjJqAnalysisResultResVO vo : records) {
//            rows.add(Arrays.asList(
//                    vo.getRuleName(),
//                    vo.getLinkData(),
//                    vo.getResult(),
//                    vo.getWarningUnit(),
//                    String.valueOf(vo.getMatchCount()),
//                    String.valueOf(vo.getTotalCount()),
//                    DateUtil.format(vo.getCreateTime(), "yyyy-MM-dd HH:mm:ss"),
//                    getNotifyStatusText(vo.getNotifyStatus())
//            ));
//        }
//
//        // 2. 设置响应头
//        response.setContentType("application/vnd.ms-excel");
//        response.setCharacterEncoding("utf-8");
//        String fileName = URLEncoder.encode("预警结果", "UTF-8");
//        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
//        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
//
//        // 3. 写入Excel
//        FastExcel.write(response.getOutputStream())
//                .head(getExcelHead())
//                .sheet("预警结果")
//                .doWrite(rows);
//    }
//
//    private List<List<String>> getExcelHead() {
//        List<List<String>> head = new ArrayList<>();
//        head.add(Collections.singletonList("规则名称"));
//        head.add(Collections.singletonList("关联数据"));
//        head.add(Collections.singletonList("预警结果"));
//        head.add(Collections.singletonList("预警单位"));
//        head.add(Collections.singletonList("匹配规则数"));
//        head.add(Collections.singletonList("涉警总数"));
//        head.add(Collections.singletonList("创建时间"));
//        head.add(Collections.singletonList("通知状态"));
//        return head;
//    }
//
//    private String getNotifyStatusText(Integer status) {
//        if (status == null) return "";
//        switch (status) {
//            case 0:
//                return "待通知";
//            case 1:
//                return "无需通知";
//            case 2:
//                return "已通知";
//            case 3:
//                return "通知失败";
//            default:
//                return "";
//        }
//    }
//
//
//    public void saveCountDetail(List<String> ids) {
//        List<WjJqAnalysisResultResVO> wjJqAnalysisResultResVOS = wjJqAnalysisResultMapper.queryCountDetailByIds(ids);
//        for (WjJqAnalysisResultResVO wjJqAnalysisResultResVO : wjJqAnalysisResultResVOS) {
//            LambdaUpdateWrapper<WjJqAnalysisResult> updateWrapper = Wrappers.<WjJqAnalysisResult>lambdaUpdate()
//                    .eq(WjJqAnalysisResult::getId, wjJqAnalysisResultResVO.getId())
//                    .set(WjJqAnalysisResult::getMatchCount, wjJqAnalysisResultResVO.getMatchCount())
//                    .set(WjJqAnalysisResult::getTotalCount, wjJqAnalysisResultResVO.getTotalCount());
//            wjJqAnalysisResultMapper.update(updateWrapper);
//        }
//    }
//
//
////    @Scheduled(cron = "0 0 * * * *")
//    public void refreshCountDetail(){
//        List<WjJqAnalysisResult> list = this.list(Wrappers.<WjJqAnalysisResult>lambdaQuery()
//                .select(WjJqAnalysisResult::getId));
//        List<String> collect = list.stream().map(WjJqAnalysisResult::getId).collect(Collectors.toList());
//        saveCountDetail(collect);
//    }
}
