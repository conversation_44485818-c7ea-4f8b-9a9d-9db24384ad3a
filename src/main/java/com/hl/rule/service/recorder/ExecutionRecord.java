package com.hl.rule.service.recorder;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 执行记录
 * 
 * 记录规则执行的完整过程，包括开始时间、结束时间、执行步骤、结果等
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Data
public class ExecutionRecord {
    
    /**
     * 执行标识符
     */
    private String identifier;
    
    /**
     * 执行描述
     */
    private String description;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 执行状态
     */
    private ExecutionStatus status;
    
    /**
     * 最终结果
     */
    private Boolean finalResult;
    
    /**
     * 执行摘要
     */
    private String summary;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 总执行时长（毫秒）
     */
    private Long totalDuration;
    
    /**
     * 执行步骤列表
     */
    private List<ExecutionStep> steps;
    
    /**
     * 详细信息
     */
    private Map<String, Object> details;
    
    /**
     * 获取格式化的开始时间
     * 
     * @return 格式化的开始时间
     */
    public String getFormattedStartTime() {
        return startTime != null ? startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")) : null;
    }
    
    /**
     * 获取格式化的结束时间
     * 
     * @return 格式化的结束时间
     */
    public String getFormattedEndTime() {
        return endTime != null ? endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")) : null;
    }
    
    /**
     * 获取格式化的执行时长
     * 
     * @return 格式化的执行时长
     */
    public String getFormattedDuration() {
        if (totalDuration == null) {
            return "N/A";
        }
        
        if (totalDuration < 1000) {
            return totalDuration + "ms";
        } else if (totalDuration < 60000) {
            return String.format("%.2fs", totalDuration / 1000.0);
        } else {
            return String.format("%.2fm", totalDuration / 60000.0);
        }
    }
    
    /**
     * 获取成功步骤数量
     * 
     * @return 成功步骤数量
     */
    public int getSuccessStepCount() {
        if (steps == null) {
            return 0;
        }
        return (int) steps.stream().mapToLong(step -> step.isResult() ? 1 : 0).sum();
    }
    
    /**
     * 获取失败步骤数量
     * 
     * @return 失败步骤数量
     */
    public int getFailedStepCount() {
        if (steps == null) {
            return 0;
        }
        return (int) steps.stream().mapToLong(step -> step.isResult() ? 0 : 1).sum();
    }
    
    /**
     * 获取总步骤数量
     * 
     * @return 总步骤数量
     */
    public int getTotalStepCount() {
        return steps != null ? steps.size() : 0;
    }
    
    /**
     * 检查是否执行成功
     * 
     * @return 是否执行成功
     */
    public boolean isSuccess() {
        return status == ExecutionStatus.COMPLETED && Boolean.TRUE.equals(finalResult);
    }
    
    /**
     * 检查是否执行失败
     * 
     * @return 是否执行失败
     */
    public boolean isFailed() {
        return status == ExecutionStatus.FAILED || (status == ExecutionStatus.COMPLETED && Boolean.FALSE.equals(finalResult));
    }
    
    /**
     * 检查是否正在执行
     * 
     * @return 是否正在执行
     */
    public boolean isRunning() {
        return status == ExecutionStatus.RUNNING;
    }
    
    /**
     * 获取执行摘要报告
     * 
     * @return 摘要报告
     */
    public String getSummaryReport() {
        StringBuilder report = new StringBuilder();
        report.append("执行记录摘要:\n");
        report.append("标识符: ").append(identifier).append("\n");
        report.append("描述: ").append(description).append("\n");
        report.append("状态: ").append(status).append("\n");
        report.append("开始时间: ").append(getFormattedStartTime()).append("\n");
        report.append("结束时间: ").append(getFormattedEndTime()).append("\n");
        report.append("执行时长: ").append(getFormattedDuration()).append("\n");
        report.append("最终结果: ").append(finalResult).append("\n");
        report.append("总步骤数: ").append(getTotalStepCount()).append("\n");
        report.append("成功步骤: ").append(getSuccessStepCount()).append("\n");
        report.append("失败步骤: ").append(getFailedStepCount()).append("\n");
        
        if (errorMessage != null) {
            report.append("错误信息: ").append(errorMessage).append("\n");
        }
        
        if (summary != null) {
            report.append("摘要: ").append(summary).append("\n");
        }
        
        return report.toString();
    }
    
    /**
     * 获取详细的执行报告
     * 
     * @return 详细报告
     */
    public String getDetailedReport() {
        StringBuilder report = new StringBuilder();
        report.append(getSummaryReport());
        
        if (steps != null && !steps.isEmpty()) {
            report.append("\n执行步骤详情:\n");
            for (int i = 0; i < steps.size(); i++) {
                ExecutionStep step = steps.get(i);
                report.append(String.format("%d. [%s] %s - %s - %s\n", 
                        i + 1, 
                        step.getFormattedTimestamp(),
                        step.getStepName(),
                        step.getDescription(),
                        step.isResult() ? "成功" : "失败"));
                
                if (step.getDetails() != null && !step.getDetails().isEmpty()) {
                    for (Map.Entry<String, Object> entry : step.getDetails().entrySet()) {
                        report.append(String.format("   %s: %s\n", entry.getKey(), entry.getValue()));
                    }
                }
            }
        }
        
        if (details != null && !details.isEmpty()) {
            report.append("\n详细信息:\n");
            for (Map.Entry<String, Object> entry : details.entrySet()) {
                report.append(String.format("%s: %s\n", entry.getKey(), entry.getValue()));
            }
        }
        
        return report.toString();
    }
    
    @Override
    public String toString() {
        return String.format("ExecutionRecord{identifier='%s', status=%s, duration=%s, steps=%d, result=%s}", 
                identifier, status, getFormattedDuration(), getTotalStepCount(), finalResult);
    }
}
