package com.hl.rule.service.recorder;

import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 默认规则执行监听器
 * 
 * 提供基础的日志记录功能
 * 可以作为其他监听器的基类或参考实现
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@Component
public class DefaultRuleExecutionListener implements RuleExecutionListener {
    
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss.SSS");
    
    @Override
    public void onExecutionStarted(String identifier, String description, LocalDateTime startTime) {
        if (log.isInfoEnabled()) {
            log.info("[{}] 执行开始 - {} - {}", 
                    startTime.format(TIME_FORMATTER), identifier, description);
        }
    }
    
    @Override
    public void onStepExecuted(String identifier, String stepName, String stepDescription, 
                              boolean result, LocalDateTime timestamp) {
        if (log.isDebugEnabled()) {
            log.debug("[{}] 步骤执行 - {} - {} - {} - {}", 
                    timestamp.format(TIME_FORMATTER), identifier, stepName, stepDescription, 
                    result ? "成功" : "失败");
        }
    }
    
    @Override
    public void onEvaluationRecorded(String identifier, EvaluationResult evaluationResult) {
        if (log.isDebugEnabled()) {
            log.debug("[{}] 评估记录 - {} - {} - {} - {}ms", 
                    evaluationResult.getEndTime() != null ? evaluationResult.getEndTime().format(TIME_FORMATTER) : "N/A",
                    identifier, 
                    evaluationResult.getEvaluatorType(),
                    evaluationResult.isResult() ? "通过" : "失败",
                    evaluationResult.getDuration());
        }
    }
    
    @Override
    public void onExecutionCompleted(String identifier, boolean finalResult, String summary, LocalDateTime endTime) {
        if (log.isInfoEnabled()) {
            log.info("[{}] 执行完成 - {} - {} - {}", 
                    endTime.format(TIME_FORMATTER), identifier, 
                    finalResult ? "成功" : "失败", summary);
        }
    }
    
    @Override
    public void onExecutionFailed(String identifier, String error, Throwable exception, LocalDateTime errorTime) {
        log.error("[{}] 执行失败 - {} - {}", 
                errorTime.format(TIME_FORMATTER), identifier, error, exception);
    }
}
