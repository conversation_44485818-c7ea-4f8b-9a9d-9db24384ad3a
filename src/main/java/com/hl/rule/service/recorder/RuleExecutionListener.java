package com.hl.rule.service.recorder;

import com.hl.rule.service.evaluator.result.EvaluationResult;

import java.time.LocalDateTime;

/**
 * 规则执行监听器接口
 * 
 * 使用观察者模式，监听规则执行过程中的各种事件
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
public interface RuleExecutionListener {
    
    /**
     * 执行开始时触发
     * 
     * @param identifier 执行标识符
     * @param description 执行描述
     * @param startTime 开始时间
     */
    void onExecutionStarted(String identifier, String description, LocalDateTime startTime);
    
    /**
     * 执行步骤完成时触发
     * 
     * @param identifier 执行标识符
     * @param stepName 步骤名称
     * @param stepDescription 步骤描述
     * @param result 步骤结果
     * @param timestamp 时间戳
     */
    void onStepExecuted(String identifier, String stepName, String stepDescription, 
                       boolean result, LocalDateTime timestamp);
    
    /**
     * 评估记录时触发
     * 
     * @param identifier 执行标识符
     * @param evaluationResult 评估结果
     */
    void onEvaluationRecorded(String identifier, EvaluationResult evaluationResult);
    
    /**
     * 执行完成时触发
     * 
     * @param identifier 执行标识符
     * @param finalResult 最终结果
     * @param summary 执行摘要
     * @param endTime 结束时间
     */
    void onExecutionCompleted(String identifier, boolean finalResult, String summary, LocalDateTime endTime);
    
    /**
     * 执行失败时触发
     * 
     * @param identifier 执行标识符
     * @param error 错误信息
     * @param exception 异常对象
     * @param errorTime 错误时间
     */
    void onExecutionFailed(String identifier, String error, Throwable exception, LocalDateTime errorTime);
}
