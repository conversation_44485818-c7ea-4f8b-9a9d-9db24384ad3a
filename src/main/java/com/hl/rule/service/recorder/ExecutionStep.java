package com.hl.rule.service.recorder;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 执行步骤
 * 
 * 记录规则执行过程中的单个步骤信息
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Data
public class ExecutionStep {
    
    /**
     * 步骤名称
     */
    private String stepName;
    
    /**
     * 步骤描述
     */
    private String description;
    
    /**
     * 步骤结果
     */
    private boolean result;
    
    /**
     * 执行时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 步骤详细信息
     */
    private Map<String, Object> details;
    
    /**
     * 获取格式化的时间戳
     * 
     * @return 格式化的时间戳
     */
    public String getFormattedTimestamp() {
        return timestamp != null ? timestamp.format(DateTimeFormatter.ofPattern("HH:mm:ss.SSS")) : null;
    }
    
    /**
     * 获取步骤状态文本
     * 
     * @return 状态文本
     */
    public String getStatusText() {
        return result ? "成功" : "失败";
    }
    
    /**
     * 获取步骤摘要
     * 
     * @return 步骤摘要
     */
    public String getSummary() {
        return String.format("[%s] %s - %s - %s", 
                getFormattedTimestamp(), stepName, description, getStatusText());
    }
    
    @Override
    public String toString() {
        return String.format("ExecutionStep{stepName='%s', description='%s', result=%s, timestamp=%s}", 
                stepName, description, result, getFormattedTimestamp());
    }
}
