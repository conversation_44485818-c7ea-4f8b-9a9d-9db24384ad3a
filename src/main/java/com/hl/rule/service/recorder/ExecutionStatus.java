package com.hl.rule.service.recorder;

/**
 * 执行状态枚举
 * 
 * 定义规则执行的各种状态
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
public enum ExecutionStatus {
    
    /**
     * 正在执行
     */
    RUNNING("正在执行"),
    
    /**
     * 执行完成
     */
    COMPLETED("执行完成"),
    
    /**
     * 执行失败
     */
    FAILED("执行失败"),
    
    /**
     * 已取消
     */
    CANCELLED("已取消");
    
    private final String description;
    
    ExecutionStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return description;
    }
}
