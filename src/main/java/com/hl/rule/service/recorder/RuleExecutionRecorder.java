package com.hl.rule.service.recorder;

import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 规则执行记录器
 * 
 * 使用观察者模式记录规则执行的详细过程
 * 支持多种记录策略和监听器
 * 线程安全的执行记录管理
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@Component
public class RuleExecutionRecorder {
    
    /**
     * 执行记录存储（线程安全）
     */
    private final Map<String, ExecutionRecord> executionRecords = new ConcurrentHashMap<>();
    
    /**
     * 执行监听器列表（线程安全）
     */
    private final List<RuleExecutionListener> listeners = new CopyOnWriteArrayList<>();
    
    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    /**
     * 开始执行记录
     * 
     * @param identifier 执行标识符
     * @param description 执行描述
     */
    public void startExecution(String identifier, String description) {
        LocalDateTime startTime = LocalDateTime.now();
        
        ExecutionRecord record = new ExecutionRecord();
        record.setIdentifier(identifier);
        record.setDescription(description);
        record.setStartTime(startTime);
        record.setStatus(ExecutionStatus.RUNNING);
        record.setSteps(new ArrayList<>());
        record.setDetails(new HashMap<>());
        
        executionRecords.put(identifier, record);
        
        // 通知监听器
        notifyListeners(listener -> listener.onExecutionStarted(identifier, description, startTime));
        
        log.info("Started execution recording: {} - {}", identifier, description);
    }
    
    /**
     * 记录执行步骤
     * 
     * @param identifier 执行标识符
     * @param stepName 步骤名称
     * @param stepDescription 步骤描述
     * @param result 步骤结果
     */
    public void recordStep(String identifier, String stepName, String stepDescription, boolean result) {
        ExecutionRecord record = executionRecords.get(identifier);
        if (record == null) {
            log.warn("No execution record found for identifier: {}", identifier);
            return;
        }
        
        LocalDateTime stepTime = LocalDateTime.now();
        
        ExecutionStep step = new ExecutionStep();
        step.setStepName(stepName);
        step.setDescription(stepDescription);
        step.setResult(result);
        step.setTimestamp(stepTime);
        step.setDetails(new HashMap<>());
        
        record.getSteps().add(step);
        
        // 通知监听器
        notifyListeners(listener -> listener.onStepExecuted(identifier, stepName, stepDescription, result, stepTime));
        
        log.debug("Recorded execution step: {} - {} - {} - {}", identifier, stepName, stepDescription, result);
    }
    
    /**
     * 记录评估结果
     * 
     * @param identifier 执行标识符
     * @param evaluationResult 评估结果
     */
    public void recordEvaluation(String identifier, EvaluationResult evaluationResult) {
        ExecutionRecord record = executionRecords.get(identifier);
        if (record == null) {
            log.warn("No execution record found for identifier: {}", identifier);
            return;
        }
        
        // 转换评估结果为执行步骤
        ExecutionStep step = new ExecutionStep();
        step.setStepName("EVALUATION");
        step.setDescription(evaluationResult.getDescription());
        step.setResult(evaluationResult.isResult());
        step.setTimestamp(evaluationResult.getEndTime() != null ? evaluationResult.getEndTime() : LocalDateTime.now());
        step.setDetails(new HashMap<>());
        
        // 添加评估详情
        step.getDetails().put("evaluatorType", evaluationResult.getEvaluatorType());
        step.getDetails().put("duration", evaluationResult.getDuration());
        step.getDetails().put("success", evaluationResult.isSuccess());
        if (evaluationResult.getDetails() != null) {
            step.getDetails().putAll(evaluationResult.getDetails());
        }
        
        record.getSteps().add(step);
        
        // 通知监听器
        notifyListeners(listener -> listener.onEvaluationRecorded(identifier, evaluationResult));
        
        log.debug("Recorded evaluation: {} - {} - {}", identifier, evaluationResult.getEvaluatorType(), evaluationResult.isResult());
    }
    
    /**
     * 完成执行记录
     * 
     * @param identifier 执行标识符
     * @param finalResult 最终结果
     * @param summary 执行摘要
     */
    public void completeExecution(String identifier, boolean finalResult, String summary) {
        ExecutionRecord record = executionRecords.get(identifier);
        if (record == null) {
            log.warn("No execution record found for identifier: {}", identifier);
            return;
        }
        
        LocalDateTime endTime = LocalDateTime.now();
        
        record.setEndTime(endTime);
        record.setFinalResult(finalResult);
        record.setSummary(summary);
        record.setStatus(ExecutionStatus.COMPLETED);
        
        // 计算总执行时间
        if (record.getStartTime() != null) {
            long duration = java.time.temporal.ChronoUnit.MILLIS.between(record.getStartTime(), endTime);
            record.setTotalDuration(duration);
        }
        
        // 通知监听器
        notifyListeners(listener -> listener.onExecutionCompleted(identifier, finalResult, summary, endTime));
        
        log.info("Completed execution recording: {} - {} - {} - {}ms", 
                identifier, finalResult, summary, record.getTotalDuration());
    }
    
    /**
     * 记录执行错误
     * 
     * @param identifier 执行标识符
     * @param error 错误信息
     * @param exception 异常对象
     */
    public void recordError(String identifier, String error, Throwable exception) {
        ExecutionRecord record = executionRecords.get(identifier);
        if (record == null) {
            log.warn("No execution record found for identifier: {}", identifier);
            return;
        }
        
        LocalDateTime errorTime = LocalDateTime.now();
        
        record.setEndTime(errorTime);
        record.setStatus(ExecutionStatus.FAILED);
        record.setErrorMessage(error);
        
        if (record.getStartTime() != null) {
            long duration = java.time.temporal.ChronoUnit.MILLIS.between(record.getStartTime(), errorTime);
            record.setTotalDuration(duration);
        }
        
        // 记录错误步骤
        ExecutionStep errorStep = new ExecutionStep();
        errorStep.setStepName("ERROR");
        errorStep.setDescription(error);
        errorStep.setResult(false);
        errorStep.setTimestamp(errorTime);
        errorStep.setDetails(new HashMap<>());
        
        if (exception != null) {
            errorStep.getDetails().put("exceptionType", exception.getClass().getSimpleName());
            errorStep.getDetails().put("exceptionMessage", exception.getMessage());
        }
        
        record.getSteps().add(errorStep);
        
        // 通知监听器
        notifyListeners(listener -> listener.onExecutionFailed(identifier, error, exception, errorTime));
        
        log.error("Recorded execution error: {} - {}", identifier, error, exception);
    }
    
    /**
     * 获取执行记录
     * 
     * @param identifier 执行标识符
     * @return 执行记录
     */
    public ExecutionRecord getExecutionRecord(String identifier) {
        return executionRecords.get(identifier);
    }
    
    /**
     * 获取所有执行记录
     * 
     * @return 所有执行记录
     */
    public Map<String, ExecutionRecord> getAllExecutionRecords() {
        return new HashMap<>(executionRecords);
    }
    
    /**
     * 清理执行记录
     * 
     * @param identifier 执行标识符
     */
    public void clearExecutionRecord(String identifier) {
        ExecutionRecord removed = executionRecords.remove(identifier);
        if (removed != null) {
            log.debug("Cleared execution record: {}", identifier);
        }
    }
    
    /**
     * 清理所有执行记录
     */
    public void clearAllExecutionRecords() {
        int count = executionRecords.size();
        executionRecords.clear();
        log.info("Cleared {} execution records", count);
    }
    
    /**
     * 添加执行监听器
     * 
     * @param listener 监听器
     */
    public void addListener(RuleExecutionListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
            log.debug("Added execution listener: {}", listener.getClass().getSimpleName());
        }
    }
    
    /**
     * 移除执行监听器
     * 
     * @param listener 监听器
     */
    public void removeListener(RuleExecutionListener listener) {
        if (listeners.remove(listener)) {
            log.debug("Removed execution listener: {}", listener.getClass().getSimpleName());
        }
    }
    
    /**
     * 获取执行统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getExecutionStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        int totalRecords = executionRecords.size();
        long completedCount = executionRecords.values().stream()
                .mapToLong(record -> record.getStatus() == ExecutionStatus.COMPLETED ? 1 : 0)
                .sum();
        long failedCount = executionRecords.values().stream()
                .mapToLong(record -> record.getStatus() == ExecutionStatus.FAILED ? 1 : 0)
                .sum();
        long runningCount = executionRecords.values().stream()
                .mapToLong(record -> record.getStatus() == ExecutionStatus.RUNNING ? 1 : 0)
                .sum();
        
        stats.put("totalRecords", totalRecords);
        stats.put("completedCount", completedCount);
        stats.put("failedCount", failedCount);
        stats.put("runningCount", runningCount);
        stats.put("listenerCount", listeners.size());
        
        return stats;
    }
    
    /**
     * 通知所有监听器
     * 
     * @param action 通知动作
     */
    private void notifyListeners(ListenerAction action) {
        for (RuleExecutionListener listener : listeners) {
            try {
                action.execute(listener);
            } catch (Exception e) {
                log.error("Error notifying listener: {}", listener.getClass().getSimpleName(), e);
            }
        }
    }
    
    /**
     * 监听器动作接口
     */
    @FunctionalInterface
    private interface ListenerAction {
        void execute(RuleExecutionListener listener);
    }
}
