package com.hl.rule.service.evaluator.impl;

import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.evaluator.AbstractConditionEvaluator;
import com.hl.rule.service.evaluator.context.EvaluationContext;
import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 字符串条件评估器
 * 
 * 处理字符串类型的条件评估，支持多种字符串操作
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@Component
public class StringConditionEvaluator extends AbstractConditionEvaluator {
    
    @Override
    protected boolean doEvaluate(JSONObject field, EvaluationContext context) {
        String column = field.getString("column");
        String operator = field.getString("opt");
        String expectedValue = field.getString("value");
        
        // 从上下文获取实际值
        String actualValue = getActualValue(column, context);
        
        // 执行字符串条件评估
        boolean result = evaluateStringCondition(actualValue, operator, expectedValue);
        
        // 记录详细信息
        context.setTempResult("lastStringComparison", 
                String.format("%s %s %s = %s", actualValue, operator, expectedValue, result));
        
        logDebug(context, "String evaluation: {} {} {} = {}", actualValue, operator, expectedValue, result);
        
        return result;
    }
    
    @Override
    protected String getConditionType() {
        return "STRING_CONDITION";
    }
    
    @Override
    protected void addEvaluationDetails(EvaluationResult result, JSONObject field, 
                                      EvaluationContext context, boolean evaluationResult) {
        super.addEvaluationDetails(result, field, context, evaluationResult);
        
        String column = field.getString("column");
        String operator = field.getString("opt");
        String expectedValue = field.getString("value");
        String actualValue = getActualValue(column, context);
        
        result.addDetail("actualValue", actualValue)
              .addDetail("expectedValue", expectedValue)
              .addDetail("operator", operator)
              .addDetail("comparisonResult", evaluationResult)
              .addDetail("valueType", "string");
        
        // 添加字符串特有的详情
        if (actualValue != null) {
            result.addDetail("actualLength", actualValue.length());
        }
        if (expectedValue != null) {
            result.addDetail("expectedLength", expectedValue.length());
        }
    }
    
    /**
     * 获取实际值
     */
    private String getActualValue(String column, EvaluationContext context) {
        Object value = context.getData(column);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 执行字符串条件评估
     */
    private boolean evaluateStringCondition(String actualValue, String operator, String expectedValue) {
        if (actualValue == null && expectedValue == null) {
            return "equals".equals(operator) || "=".equals(operator);
        }
        
        if (actualValue == null || expectedValue == null) {
            return "not_equals".equals(operator) || "!=".equals(operator);
        }
        
        switch (operator) {
            case "equals":
            case "=":
                return actualValue.equals(expectedValue);
                
            case "not_equals":
            case "!=":
                return !actualValue.equals(expectedValue);
                
            case "contains":
                return actualValue.contains(expectedValue);
                
            case "not_contains":
                return !actualValue.contains(expectedValue);
                
            case "starts_with":
                return actualValue.startsWith(expectedValue);
                
            case "ends_with":
                return actualValue.endsWith(expectedValue);
                
            case "regex":
                try {
                    return actualValue.matches(expectedValue);
                } catch (Exception e) {
                    log.warn("Invalid regex pattern: {}", expectedValue, e);
                    return false;
                }
                
            case "length_equals":
                try {
                    int expectedLength = Integer.parseInt(expectedValue);
                    return actualValue.length() == expectedLength;
                } catch (NumberFormatException e) {
                    log.warn("Invalid length value: {}", expectedValue);
                    return false;
                }
                
            case "length_greater":
                try {
                    int expectedLength = Integer.parseInt(expectedValue);
                    return actualValue.length() > expectedLength;
                } catch (NumberFormatException e) {
                    log.warn("Invalid length value: {}", expectedValue);
                    return false;
                }
                
            case "length_less":
                try {
                    int expectedLength = Integer.parseInt(expectedValue);
                    return actualValue.length() < expectedLength;
                } catch (NumberFormatException e) {
                    log.warn("Invalid length value: {}", expectedValue);
                    return false;
                }
                
            case "is_empty":
                return actualValue.isEmpty();
                
            case "is_not_empty":
                return !actualValue.isEmpty();
                
            case "ignore_case_equals":
                return actualValue.equalsIgnoreCase(expectedValue);
                
            case "ignore_case_contains":
                return actualValue.toLowerCase().contains(expectedValue.toLowerCase());
                
            default:
                log.warn("Unsupported string operator: {}", operator);
                return false;
        }
    }
}
