package com.hl.rule.service.evaluator.impl;

import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.evaluator.AbstractConditionEvaluator;
import com.hl.rule.service.evaluator.context.EvaluationContext;
import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 数值条件评估器
 * 
 * 处理数值类型的条件评估，支持多种数值比较操作
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@Component
public class NumberConditionEvaluator extends AbstractConditionEvaluator {
    
    @Override
    protected boolean doEvaluate(JSONObject field, EvaluationContext context) {
        String column = field.getString("column");
        String operator = field.getString("opt");
        
        // 获取期望数值
        Number expectedValue = getExpectedNumber(field);
        if (expectedValue == null && !isNullCheckOperator(operator)) {
            logWarn(context, "Expected value is null for column: {}", column);
            return false;
        }
        
        // 获取实际数值
        Number actualValue = getActualNumber(column, context);
        
        // 执行数值条件评估
        boolean result = evaluateNumberCondition(actualValue, operator, expectedValue);
        
        // 记录详细信息
        context.setTempResult("lastNumberComparison", 
                String.format("%.2f %s %.2f = %s", 
                        actualValue != null ? actualValue.doubleValue() : null, 
                        operator, 
                        expectedValue != null ? expectedValue.doubleValue() : null, 
                        result));
        
        logDebug(context, "Number evaluation: {} {} {} = {}", actualValue, operator, expectedValue, result);
        
        return result;
    }
    
    @Override
    protected String getConditionType() {
        return "NUMBER_CONDITION";
    }
    
    @Override
    protected void addEvaluationDetails(EvaluationResult result, JSONObject field, 
                                      EvaluationContext context, boolean evaluationResult) {
        super.addEvaluationDetails(result, field, context, evaluationResult);
        
        String column = field.getString("column");
        String operator = field.getString("opt");
        Number expectedValue = getExpectedNumber(field);
        Number actualValue = getActualNumber(column, context);
        
        result.addDetail("actualValue", actualValue)
              .addDetail("expectedValue", expectedValue)
              .addDetail("operator", operator)
              .addDetail("comparisonResult", evaluationResult)
              .addDetail("valueType", "number");
        
        // 添加数值特有的详情
        if (actualValue != null && expectedValue != null) {
            double diff = actualValue.doubleValue() - expectedValue.doubleValue();
            result.addDetail("difference", diff)
                  .addDetail("absoluteDifference", Math.abs(diff));
        }
    }
    
    /**
     * 获取期望数值
     */
    private Number getExpectedNumber(JSONObject field) {
        Object value = field.get("value");
        if (value == null) {
            return null;
        }
        
        if (value instanceof Number) {
            return (Number) value;
        }
        
        try {
            String strValue = value.toString();
            if (strValue.contains(".")) {
                return Double.parseDouble(strValue);
            } else {
                return Long.parseLong(strValue);
            }
        } catch (NumberFormatException e) {
            log.warn("Cannot parse number value: {}", value);
            return null;
        }
    }
    
    /**
     * 获取实际数值
     */
    private Number getActualNumber(String column, EvaluationContext context) {
        Object value = context.getData(column);
        if (value == null) {
            return null;
        }
        
        if (value instanceof Number) {
            return (Number) value;
        }
        
        try {
            String strValue = value.toString();
            if (strValue.contains(".")) {
                return Double.parseDouble(strValue);
            } else {
                return Long.parseLong(strValue);
            }
        } catch (NumberFormatException e) {
            log.warn("Cannot parse number value from context: {}", value);
            return null;
        }
    }
    
    /**
     * 检查是否为空值检查操作符
     */
    private boolean isNullCheckOperator(String operator) {
        return "is_null".equals(operator) || "is_not_null".equals(operator);
    }
    
    /**
     * 执行数值条件评估
     */
    private boolean evaluateNumberCondition(Number actualValue, String operator, Number expectedValue) {
        // 处理空值情况
        if (actualValue == null) {
            switch (operator) {
                case "is_null":
                    return true;
                case "is_not_null":
                    return false;
                default:
                    return false;
            }
        }
        
        if ("is_null".equals(operator)) {
            return false;
        }
        if ("is_not_null".equals(operator)) {
            return true;
        }
        
        if (expectedValue == null) {
            return false;
        }
        
        // 使用BigDecimal进行精确比较
        BigDecimal actual = new BigDecimal(actualValue.toString());
        BigDecimal expected = new BigDecimal(expectedValue.toString());
        
        switch (operator) {
            case "equals":
            case "=":
                return actual.compareTo(expected) == 0;
                
            case "not_equals":
            case "!=":
                return actual.compareTo(expected) != 0;
                
            case "greater_than":
            case ">":
                return actual.compareTo(expected) > 0;
                
            case "less_than":
            case "<":
                return actual.compareTo(expected) < 0;
                
            case "greater_equal":
            case ">=":
                return actual.compareTo(expected) >= 0;
                
            case "less_equal":
            case "<=":
                return actual.compareTo(expected) <= 0;
                
            case "between":
                // 期望值应该是一个范围，这里简化处理
                return actual.compareTo(expected) >= 0;
                
            default:
                log.warn("Unsupported number operator: {}", operator);
                return false;
        }
    }
}
