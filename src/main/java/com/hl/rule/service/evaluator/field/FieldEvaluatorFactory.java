package com.hl.rule.service.evaluator.field;

import com.hl.rule.service.evaluator.FieldEvaluator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 字段评估器工厂
 * 
 * 使用工厂模式管理不同类型的字段评估器
 * 支持自动注册和动态扩展
 * 线程安全的字段评估器管理
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@Component
public class FieldEvaluatorFactory {
    
    /**
     * 字段评估器注册表（线程安全）
     */
    private final Map<String, FieldEvaluator> fieldEvaluators = new ConcurrentHashMap<>();
    
    /**
     * 字段名到评估器的映射
     */
    private final Map<String, FieldEvaluator> fieldNameMapping = new ConcurrentHashMap<>();
    
    /**
     * 自动注入所有字段评估器
     */
    @Autowired(required = false)
    private List<FieldEvaluator> allFieldEvaluators;
    
    @PostConstruct
    public void init() {
        // 自动注册所有Spring管理的字段评估器
        if (allFieldEvaluators != null) {
            for (FieldEvaluator evaluator : allFieldEvaluators) {
                register(evaluator);
            }
        }
        
        log.info("FieldEvaluatorFactory initialized with {} field evaluators", fieldEvaluators.size());
        log.info("Field name mappings: {}", fieldNameMapping.size());
    }
    
    /**
     * 注册字段评估器
     * 
     * @param evaluator 字段评估器实例
     */
    public void register(FieldEvaluator evaluator) {
        if (evaluator == null) {
            log.warn("Cannot register null field evaluator");
            return;
        }
        
        String fieldType = evaluator.getFieldType();
        if (fieldType == null || fieldType.trim().isEmpty()) {
            log.warn("Cannot register field evaluator with null or empty type: {}", 
                    evaluator.getClass().getSimpleName());
            return;
        }
        
        String normalizedType = fieldType.toLowerCase();
        fieldEvaluators.put(normalizedType, evaluator);
        
        // 注册字段名映射
        String[] supportedFields = evaluator.getSupportedFields();
        if (supportedFields != null) {
            for (String fieldName : supportedFields) {
                if (fieldName != null && !fieldName.trim().isEmpty()) {
                    fieldNameMapping.put(fieldName.toLowerCase(), evaluator);
                }
            }
        }
        
        log.debug("Registered field evaluator: {} -> {} (supports {} fields)", 
                normalizedType, evaluator.getClass().getSimpleName(), 
                supportedFields != null ? supportedFields.length : 0);
    }
    
    /**
     * 根据字段类型获取评估器
     * 
     * @param fieldType 字段类型
     * @return 字段评估器实例，如果不存在则返回null
     */
    public FieldEvaluator getEvaluatorByType(String fieldType) {
        if (fieldType == null || fieldType.trim().isEmpty()) {
            log.warn("Cannot get field evaluator for null or empty type");
            return null;
        }
        
        String normalizedType = fieldType.toLowerCase();
        FieldEvaluator evaluator = fieldEvaluators.get(normalizedType);
        
        if (evaluator == null) {
            log.warn("No field evaluator found for type: {}", fieldType);
        }
        
        return evaluator;
    }
    
    /**
     * 根据字段名获取评估器
     * 
     * @param fieldName 字段名
     * @return 字段评估器实例，如果不存在则返回null
     */
    public FieldEvaluator getEvaluatorByFieldName(String fieldName) {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            log.warn("Cannot get field evaluator for null or empty field name");
            return null;
        }
        
        String normalizedFieldName = fieldName.toLowerCase();
        FieldEvaluator evaluator = fieldNameMapping.get(normalizedFieldName);
        
        if (evaluator == null) {
            log.debug("No field evaluator found for field: {}", fieldName);
        }
        
        return evaluator;
    }
    
    /**
     * 检查是否支持指定的字段类型
     * 
     * @param fieldType 字段类型
     * @return 是否支持
     */
    public boolean supportsFieldType(String fieldType) {
        return fieldType != null && fieldEvaluators.containsKey(fieldType.toLowerCase());
    }
    
    /**
     * 检查是否支持指定的字段名
     * 
     * @param fieldName 字段名
     * @return 是否支持
     */
    public boolean supportsFieldName(String fieldName) {
        return fieldName != null && fieldNameMapping.containsKey(fieldName.toLowerCase());
    }
    
    /**
     * 获取所有支持的字段类型
     * 
     * @return 支持的字段类型数组
     */
    public String[] getSupportedFieldTypes() {
        return fieldEvaluators.keySet().toArray(new String[0]);
    }
    
    /**
     * 获取所有支持的字段名
     * 
     * @return 支持的字段名数组
     */
    public String[] getSupportedFieldNames() {
        return fieldNameMapping.keySet().toArray(new String[0]);
    }
    
    /**
     * 获取字段评估器数量
     * 
     * @return 评估器数量
     */
    public int getEvaluatorCount() {
        return fieldEvaluators.size();
    }
    
    /**
     * 获取字段映射数量
     * 
     * @return 字段映射数量
     */
    public int getFieldMappingCount() {
        return fieldNameMapping.size();
    }
    
    /**
     * 获取所有字段评估器的摘要信息
     * 
     * @return 摘要信息
     */
    public Map<String, Object> getSummary() {
        Map<String, Object> summary = new HashMap<>();
        summary.put("evaluatorCount", fieldEvaluators.size());
        summary.put("fieldMappingCount", fieldNameMapping.size());
        summary.put("supportedFieldTypes", getSupportedFieldTypes());
        summary.put("supportedFieldNames", getSupportedFieldNames());
        
        Map<String, String> evaluatorClasses = new HashMap<>();
        for (Map.Entry<String, FieldEvaluator> entry : fieldEvaluators.entrySet()) {
            evaluatorClasses.put(entry.getKey(), entry.getValue().getClass().getSimpleName());
        }
        summary.put("evaluatorClasses", evaluatorClasses);
        
        return summary;
    }
    
    /**
     * 清理所有字段评估器
     */
    public void clear() {
        fieldEvaluators.clear();
        fieldNameMapping.clear();
        log.info("All field evaluators cleared");
    }
    
    /**
     * 移除指定类型的字段评估器
     * 
     * @param fieldType 字段类型
     * @return 被移除的评估器，如果不存在则返回null
     */
    public FieldEvaluator remove(String fieldType) {
        if (fieldType == null || fieldType.trim().isEmpty()) {
            return null;
        }
        
        String normalizedType = fieldType.toLowerCase();
        FieldEvaluator removed = fieldEvaluators.remove(normalizedType);
        
        if (removed != null) {
            // 同时移除字段名映射
            fieldNameMapping.entrySet().removeIf(entry -> entry.getValue() == removed);
            log.debug("Removed field evaluator: {}", normalizedType);
        }
        
        return removed;
    }
    
    /**
     * 获取字段评估器的详细信息
     * 
     * @param fieldType 字段类型
     * @return 详细信息
     */
    public Map<String, Object> getEvaluatorDetails(String fieldType) {
        FieldEvaluator evaluator = getEvaluatorByType(fieldType);
        if (evaluator == null) {
            return null;
        }
        
        Map<String, Object> details = new HashMap<>();
        details.put("fieldType", evaluator.getFieldType());
        details.put("className", evaluator.getClass().getSimpleName());
        details.put("supportedFields", evaluator.getSupportedFields());
        
        return details;
    }
}
