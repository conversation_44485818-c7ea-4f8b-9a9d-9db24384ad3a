package com.hl.rule.service.evaluator.field;

import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.AnalysisContext;
import com.hl.rule.service.evaluator.FieldEvaluator;
import com.hl.rule.service.evaluator.context.EvaluationContext;
import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 抽象字段评估器基类
 * 
 * 使用模板方法模式，定义统一的字段评估流程
 * 提供通用的评估逻辑和详情记录功能
 * 子类只需实现具体的字段评估逻辑
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
public abstract class AbstractFieldEvaluator implements FieldEvaluator {
    
    @Override
    public boolean evaluate(JSONObject field, AnalysisContext context) {
        // 兼容旧接口，转换为新的评估方式
        EvaluationContext evaluationContext = convertToEvaluationContext(context);
        EvaluationResult result = evaluateWithDetails(field, evaluationContext);
        return result.isResult();
    }
    
    @Override
    public EvaluationResult evaluateWithDetails(JSONObject field, EvaluationContext context) {
        LocalDateTime startTime = LocalDateTime.now();
        String fieldType = getFieldType();
        String description = buildDescription(field);
        
        try {
            // 前置验证
            if (!preValidate(field, context)) {
                return EvaluationResult.failure(fieldType, description, "Pre-validation failed");
            }
            
            // 设置当前字段到上下文
            context.setCurrentField(field);
            
            // 执行具体的字段评估逻辑
            boolean result = doEvaluate(field, context);
            
            // 创建成功的评估结果
            EvaluationResult evaluationResult = EvaluationResult.success(result, fieldType, description)
                    .setExecutionTime(startTime, LocalDateTime.now())
                    .addDetail("fieldConfig", field)
                    .addDetail("fieldType", fieldType)
                    .addDetail("identifier", context.getIdentifier());
            
            // 添加具体字段评估器的详情
            addFieldEvaluationDetails(evaluationResult, field, context, result);
            
            // 后置处理
            postProcess(evaluationResult, field, context);
            
            // 记录到上下文
            context.addEvaluationResult(evaluationResult);
            
            if (context.isDebugMode()) {
                log.debug("Field evaluation completed: {} - {}", description, evaluationResult.getExecutionSummary());
            }
            
            return evaluationResult;
            
        } catch (Exception e) {
            log.error("Error evaluating field: {}", description, e);
            
            EvaluationResult errorResult = EvaluationResult.failure(fieldType, description, e.getMessage())
                    .setExecutionTime(startTime, LocalDateTime.now())
                    .addDetail("fieldConfig", field)
                    .addDetail("fieldType", fieldType)
                    .addDetail("identifier", context.getIdentifier())
                    .addDetail("exception", e.getClass().getSimpleName());
            
            context.addEvaluationResult(errorResult);
            return errorResult;
        }
    }
    
    /**
     * 具体的字段评估逻辑，由子类实现
     * 
     * @param field 字段配置
     * @param context 评估上下文
     * @return 评估结果
     */
    protected abstract boolean doEvaluate(JSONObject field, EvaluationContext context);
    
    /**
     * 获取支持的字段列表，由子类实现
     * 
     * @return 支持的字段名数组
     */
    protected abstract String[] getFieldNames();
    
    @Override
    public String[] getSupportedFields() {
        return getFieldNames();
    }
    
    @Override
    public boolean supportsField(String fieldName) {
        if (fieldName == null) {
            return false;
        }
        return Arrays.asList(getFieldNames()).contains(fieldName);
    }
    
    /**
     * 前置验证，子类可以重写
     * 
     * @param field 字段配置
     * @param context 评估上下文
     * @return 验证结果
     */
    protected boolean preValidate(JSONObject field, EvaluationContext context) {
        if (field == null || context == null) {
            return false;
        }
        
        String column = field.getString("column");
        if (column == null || column.trim().isEmpty()) {
            log.warn("Field column is null or empty");
            return false;
        }
        
        if (!supportsField(column)) {
            log.warn("Field {} is not supported by {}", column, getFieldType());
            return false;
        }
        
        return true;
    }
    
    /**
     * 添加字段评估详情，子类可以重写
     * 
     * @param result 评估结果
     * @param field 字段配置
     * @param context 评估上下文
     * @param evaluationResult 评估结果值
     */
    protected void addFieldEvaluationDetails(EvaluationResult result, JSONObject field, 
                                           EvaluationContext context, boolean evaluationResult) {
        // 添加基础详情
        result.addDetail("fieldEvaluator", getFieldType())
              .addDetail("evaluationResult", evaluationResult)
              .addDetail("debugMode", context.isDebugMode());
        
        // 添加字段信息
        if (field != null) {
            result.addDetail("column", field.getString("column"))
                  .addDetail("operator", field.getString("opt"))
                  .addDetail("value", field.get("value"))
                  .addDetail("type", field.getString("type"));
        }
        
        // 添加支持的字段列表
        result.addDetail("supportedFields", Arrays.asList(getSupportedFields()));
    }
    
    /**
     * 后置处理，子类可以重写
     * 
     * @param result 评估结果
     * @param field 字段配置
     * @param context 评估上下文
     */
    protected void postProcess(EvaluationResult result, JSONObject field, EvaluationContext context) {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 构建描述信息
     * 
     * @param field 字段配置
     * @return 描述信息
     */
    protected String buildDescription(JSONObject field) {
        if (field == null) {
            return getFieldType() + " field evaluation";
        }
        
        String column = field.getString("column");
        String operator = field.getString("opt");
        Object value = field.get("value");
        
        return String.format("%s field evaluation: %s %s %s", getFieldType(), column, operator, value);
    }
    
    /**
     * 转换旧版本上下文为新版本上下文
     * 
     * @param oldContext 旧版本上下文
     * @return 新版本上下文
     */
    protected EvaluationContext convertToEvaluationContext(AnalysisContext oldContext) {
        if (oldContext == null) {
            return EvaluationContext.create("unknown");
        }
        
        EvaluationContext newContext = EvaluationContext.create(oldContext.getIdentifier());
        
        // 转换数据
        Map<String, Object> data = new HashMap<>();
        data.put("analysisContext", oldContext);
        data.put("rules", oldContext.getRules());
        data.put("identifier", oldContext.getIdentifier());
        
        newContext.setData(data);
        
        return newContext;
    }
    
    /**
     * 记录调试信息
     * 
     * @param context 评估上下文
     * @param message 消息
     * @param args 参数
     */
    protected void logDebug(EvaluationContext context, String message, Object... args) {
        if (context.isDebugMode()) {
            log.debug("[{}] {}", getFieldType(), String.format(message, args));
        }
    }
    
    /**
     * 记录警告信息
     * 
     * @param context 评估上下文
     * @param message 消息
     * @param args 参数
     */
    protected void logWarn(EvaluationContext context, String message, Object... args) {
        log.warn("[{}] {}", getFieldType(), String.format(message, args));
    }
    
    /**
     * 记录错误信息
     * 
     * @param context 评估上下文
     * @param message 消息
     * @param throwable 异常
     */
    protected void logError(EvaluationContext context, String message, Throwable throwable) {
        log.error("[{}] {}", getFieldType(), message, throwable);
    }
    
    /**
     * 提取分析上下文（兼容旧版本）
     */
    protected AnalysisContext extractAnalysisContext(EvaluationContext context) {
        return context.getData("analysisContext", AnalysisContext.class);
    }
}
