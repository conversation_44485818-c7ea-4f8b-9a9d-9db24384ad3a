package com.hl.rule.service.evaluator;

import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.AnalysisContext;
import com.hl.rule.service.evaluator.context.EvaluationContext;
import com.hl.rule.service.evaluator.result.EvaluationResult;

/**
 * 字段评估器接口
 *
 * 定义字段级别的评估逻辑，每个具体的字段类型都有对应的评估器
 * 支持详细的评估结果记录
 *
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
public interface FieldEvaluator {

    /**
     * 评估字段条件（兼容旧版本接口）
     *
     * @param field 字段配置
     * @param context 分析上下文
     * @return 评估结果
     */
    boolean evaluate(JSONObject field, AnalysisContext context);

    /**
     * 评估字段条件并返回详细结果（新版本接口）
     *
     * @param field 字段配置
     * @param context 评估上下文
     * @return 详细的评估结果
     */
    EvaluationResult evaluateWithDetails(JSONObject field, EvaluationContext context);

    /**
     * 获取字段评估器类型
     *
     * @return 评估器类型标识
     */
    String getFieldType();

    /**
     * 检查是否支持指定的字段
     *
     * @param fieldName 字段名
     * @return 是否支持
     */
    boolean supportsField(String fieldName);

    /**
     * 获取支持的字段列表
     *
     * @return 支持的字段名数组
     */
    String[] getSupportedFields();
}