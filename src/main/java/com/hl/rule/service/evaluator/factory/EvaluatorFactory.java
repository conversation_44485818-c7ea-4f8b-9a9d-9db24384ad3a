package com.hl.rule.service.evaluator.factory;

import com.hl.rule.service.evaluator.ConditionEvaluator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 评估器工厂
 * 
 * 使用工厂模式管理不同类型的条件评估器
 * 支持自动注册和动态扩展
 * 线程安全的评估器管理
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@Component
public class EvaluatorFactory {
    
    /**
     * 评估器注册表（线程安全）
     */
    private final Map<String, ConditionEvaluator> evaluators = new ConcurrentHashMap<>();
    
    /**
     * 字段类型到评估器类型的映射
     */
    private final Map<String, String> fieldTypeMapping = new ConcurrentHashMap<>();
    
    /**
     * 自动注入所有评估器
     */
    @Autowired(required = false)
    private List<ConditionEvaluator> allEvaluators;
    
    @PostConstruct
    public void init() {
        // 注册默认评估器
        registerDefaultEvaluators();
        
        // 自动注册所有Spring管理的评估器
        if (allEvaluators != null) {
            for (ConditionEvaluator evaluator : allEvaluators) {
                register(evaluator);
            }
        }
        
        // 初始化字段类型映射
        initFieldTypeMapping();
        
        log.info("EvaluatorFactory initialized with {} evaluators: {}", 
                evaluators.size(), evaluators.keySet());
    }
    
    /**
     * 注册评估器
     * 
     * @param evaluator 评估器实例
     */
    public void register(ConditionEvaluator evaluator) {
        if (evaluator == null) {
            log.warn("Cannot register null evaluator");
            return;
        }
        
        String type = evaluator.getEvaluatorType();
        if (type == null || type.trim().isEmpty()) {
            log.warn("Cannot register evaluator with null or empty type: {}", 
                    evaluator.getClass().getSimpleName());
            return;
        }
        
        String normalizedType = type.toLowerCase();
        evaluators.put(normalizedType, evaluator);
        
        log.debug("Registered evaluator: {} -> {}", normalizedType, evaluator.getClass().getSimpleName());
    }
    
    /**
     * 注册评估器（指定类型）
     * 
     * @param type 评估器类型
     * @param evaluator 评估器实例
     */
    public void register(String type, ConditionEvaluator evaluator) {
        if (type == null || type.trim().isEmpty()) {
            log.warn("Cannot register evaluator with null or empty type");
            return;
        }
        
        if (evaluator == null) {
            log.warn("Cannot register null evaluator for type: {}", type);
            return;
        }
        
        String normalizedType = type.toLowerCase();
        evaluators.put(normalizedType, evaluator);
        
        log.debug("Registered evaluator: {} -> {}", normalizedType, evaluator.getClass().getSimpleName());
    }
    
    /**
     * 获取评估器
     * 
     * @param type 评估器类型
     * @return 评估器实例，如果不存在则返回null
     */
    public ConditionEvaluator getEvaluator(String type) {
        if (type == null || type.trim().isEmpty()) {
            log.warn("Cannot get evaluator for null or empty type");
            return null;
        }
        
        String normalizedType = type.toLowerCase();
        ConditionEvaluator evaluator = evaluators.get(normalizedType);
        
        if (evaluator == null) {
            log.warn("No evaluator found for type: {}", type);
        }
        
        return evaluator;
    }
    
    /**
     * 根据字段名获取评估器
     * 
     * @param fieldName 字段名
     * @return 评估器实例，如果不存在则返回null
     */
    public ConditionEvaluator getEvaluatorByField(String fieldName) {
        String evaluatorType = fieldTypeMapping.get(fieldName);
        if (evaluatorType != null) {
            return getEvaluator(evaluatorType);
        }
        
        // 如果没有明确映射，尝试根据字段名推断
        return inferEvaluatorByFieldName(fieldName);
    }
    
    /**
     * 根据规则类型获取评估器
     * 
     * @param ruleType 规则类型
     * @return 评估器实例，如果不存在则返回null
     */
    public ConditionEvaluator getEvaluatorByRuleType(String ruleType) {
        return getEvaluator(ruleType);
    }
    
    /**
     * 检查是否支持某种类型
     * 
     * @param type 类型
     * @return 是否支持
     */
    public boolean supports(String type) {
        return type != null && evaluators.containsKey(type.toLowerCase());
    }
    
    /**
     * 获取所有支持的类型
     * 
     * @return 支持的类型数组
     */
    public String[] getSupportedTypes() {
        return evaluators.keySet().toArray(new String[0]);
    }
    
    /**
     * 获取评估器数量
     * 
     * @return 评估器数量
     */
    public int getEvaluatorCount() {
        return evaluators.size();
    }
    
    /**
     * 获取所有评估器的摘要信息
     * 
     * @return 摘要信息
     */
    public Map<String, Object> getSummary() {
        Map<String, Object> summary = new HashMap<>();
        summary.put("evaluatorCount", evaluators.size());
        summary.put("supportedTypes", getSupportedTypes());
        summary.put("fieldMappingCount", fieldTypeMapping.size());
        
        Map<String, String> evaluatorClasses = new HashMap<>();
        for (Map.Entry<String, ConditionEvaluator> entry : evaluators.entrySet()) {
            evaluatorClasses.put(entry.getKey(), entry.getValue().getClass().getSimpleName());
        }
        summary.put("evaluatorClasses", evaluatorClasses);
        
        return summary;
    }
    
    /**
     * 注册默认评估器
     */
    private void registerDefaultEvaluators() {
        // 这里可以注册一些默认的评估器
        // 实际的评估器将通过Spring自动注入
        log.debug("Default evaluators registration completed");
    }
    
    /**
     * 初始化字段类型映射
     */
    private void initFieldTypeMapping() {
        // 人员相关字段
        fieldTypeMapping.put("bjlx", "person");
        fieldTypeMapping.put("jjdw", "person");
        fieldTypeMapping.put("zxjjdw", "person");
        fieldTypeMapping.put("djsj", "person");
        fieldTypeMapping.put("bjnr", "person");
        fieldTypeMapping.put("cjlx", "person");
        fieldTypeMapping.put("cjdw", "person");
        fieldTypeMapping.put("zxcjdw", "person");
        fieldTypeMapping.put("cjsj", "person");
        fieldTypeMapping.put("cjnr", "person");
        fieldTypeMapping.put("stbq", "person");
        fieldTypeMapping.put("zxcjnr", "person");
        fieldTypeMapping.put("zxstbq", "person");
        fieldTypeMapping.put("zxcjlx", "person");
        fieldTypeMapping.put("fjbq", "person");
        fieldTypeMapping.put("zxfjbq", "person");
        
        // 字符串字段
        fieldTypeMapping.put("name", "string");
        fieldTypeMapping.put("address", "string");
        fieldTypeMapping.put("description", "string");
        
        // 数值字段
        fieldTypeMapping.put("age", "number");
        fieldTypeMapping.put("count", "number");
        fieldTypeMapping.put("amount", "number");
        
        // 日期字段
        fieldTypeMapping.put("createTime", "date");
        fieldTypeMapping.put("updateTime", "date");
        fieldTypeMapping.put("birthDate", "date");
        
        log.debug("Field type mapping initialized with {} mappings", fieldTypeMapping.size());
    }
    
    /**
     * 根据字段名推断评估器类型
     * 
     * @param fieldName 字段名
     * @return 评估器实例，如果无法推断则返回null
     */
    private ConditionEvaluator inferEvaluatorByFieldName(String fieldName) {
        if (fieldName == null) {
            return null;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        
        // 根据字段名后缀推断
        if (lowerFieldName.endsWith("time") || lowerFieldName.endsWith("date") || lowerFieldName.contains("sj")) {
            return getEvaluator("date");
        }
        
        if (lowerFieldName.endsWith("count") || lowerFieldName.endsWith("num") || lowerFieldName.endsWith("age")) {
            return getEvaluator("number");
        }
        
        if (lowerFieldName.contains("name") || lowerFieldName.contains("address") || lowerFieldName.contains("nr")) {
            return getEvaluator("string");
        }
        
        // 默认返回字符串评估器
        return getEvaluator("string");
    }
    
    /**
     * 清理所有评估器
     */
    public void clear() {
        evaluators.clear();
        fieldTypeMapping.clear();
        log.info("All evaluators cleared");
    }
    
    /**
     * 移除指定类型的评估器
     * 
     * @param type 评估器类型
     * @return 被移除的评估器，如果不存在则返回null
     */
    public ConditionEvaluator remove(String type) {
        if (type == null || type.trim().isEmpty()) {
            return null;
        }
        
        String normalizedType = type.toLowerCase();
        ConditionEvaluator removed = evaluators.remove(normalizedType);
        
        if (removed != null) {
            log.debug("Removed evaluator: {}", normalizedType);
        }
        
        return removed;
    }
}
