package com.hl.rule.service.evaluator.field.impl;

import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.AnalysisContext;
import com.hl.rule.service.evaluator.context.EvaluationContext;
import com.hl.rule.service.evaluator.field.AbstractFieldEvaluator;
import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 报警类型字段评估器
 * 
 * 处理报警类型(bjlx)相关的条件评估
 * 支持数量比较和类型匹配
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@Component
public class BjlxFieldEvaluator extends AbstractFieldEvaluator {
    
    private static final String[] SUPPORTED_FIELDS = {"bjlx"};
    
    @Override
    protected boolean doEvaluate(JSONObject field, EvaluationContext context) {
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            // 检查字段类型
            if (!"count".equals(field.getString("type"))) {
                logDebug(context, "Field type is not 'count', returning false");
                return false;
            }

            // 提取有效值列表
            List<String> validValues = extractValidValues(field, "dict_value");
            if (validValues.isEmpty()) {
                logWarn(context, "No valid values found for bjlx evaluation");
                return false;
            }

            // 模拟报警类型匹配逻辑
            Set<String> matchedJjbhs = simulateMatchedJjbhs(context, validValues, "bjlx");
            
            // 更新匹配的警情编号
            updateMatchedJjbhs(context, matchedJjbhs);
            
            // 评估数量条件
            boolean result = evaluateNumberCondition(
                    field.getString("opt"),
                    matchedJjbhs.size(),
                    field.getLongValue("value")
            );
            
            // 记录详细信息
            recordFieldEvaluation("bjlx", validValues, matchedJjbhs, result, startTime, context);
            
            return result;
            
        } catch (Exception e) {
            logError(context, "Error evaluating bjlx field", e);
            return false;
        }
    }
    
    @Override
    protected String[] getFieldNames() {
        return SUPPORTED_FIELDS;
    }
    
    @Override
    public String getFieldType() {
        return "BJLX_FIELD";
    }
    
    @Override
    protected void addFieldEvaluationDetails(EvaluationResult result, JSONObject field, 
                                           EvaluationContext context, boolean evaluationResult) {
        super.addFieldEvaluationDetails(result, field, context, evaluationResult);
        
        // 添加报警类型特有的详情
        result.addDetail("fieldCategory", "报警类型")
              .addDetail("evaluationType", "数量比较")
              .addDetail("businessType", "警情分析");
        
        // 添加字典值信息
        if (field.getJSONArray("dict_value") != null) {
            result.addDetail("dictValueCount", field.getJSONArray("dict_value").size());
            result.addDetail("dictValues", field.getJSONArray("dict_value").toJavaList(String.class));
        }
        
        // 添加匹配信息
        if (context.getMatchedJjbhs() != null && !context.getMatchedJjbhs().isEmpty()) {
            result.addDetail("matchedJjbhs", new ArrayList<>(context.getMatchedJjbhs()))
                  .addDetail("matchedCount", context.getMatchedJjbhs().size());
        }
    }
    
    /**
     * 提取有效值列表
     */
    private List<String> extractValidValues(JSONObject field, String arrayKey) {
        try {
            if (field.getJSONArray(arrayKey) != null) {
                return field.getJSONArray(arrayKey)
                        .toJavaList(String.class)
                        .stream()
                        .map(str -> str.length() > 3 ? str.substring(3) : str)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("Error extracting valid values from field: {}", arrayKey, e);
        }
        return new ArrayList<>();
    }
    
    /**
     * 模拟匹配的警情编号（用于演示）
     */
    private Set<String> simulateMatchedJjbhs(EvaluationContext context, List<String> validValues, String fieldType) {
        Set<String> matchedJjbhs = new HashSet<>();
        
        // 模拟逻辑：根据字段类型和有效值生成一些匹配的警情编号
        for (int i = 0; i < validValues.size() && i < 3; i++) {
            matchedJjbhs.add(fieldType.toUpperCase() + "_" + System.currentTimeMillis() + "_" + i);
        }
        
        logDebug(context, "Simulated {} matched jjbhs for field type: {}", matchedJjbhs.size(), fieldType);
        return matchedJjbhs;
    }
    
    /**
     * 更新匹配的警情编号
     */
    private void updateMatchedJjbhs(EvaluationContext context, Set<String> newJjbhs) {
        // 将匹配的警情编号添加到上下文
        for (String jjbh : newJjbhs) {
            context.addMatchedJjbh(jjbh);
        }
        logDebug(context, "Updated matched jjbhs: {}", newJjbhs);
    }
    
    /**
     * 评估数量条件
     */
    private boolean evaluateNumberCondition(String operator, int actualValue, Long expectedValue) {
        if (expectedValue == null) {
            return false;
        }
        
        switch (operator) {
            case "equals":
            case "=":
                return actualValue == expectedValue.longValue();
            case "greater_than":
            case ">":
                return actualValue > expectedValue.longValue();
            case "less_than":
            case "<":
                return actualValue < expectedValue.longValue();
            case "greater_equal":
            case ">=":
                return actualValue >= expectedValue.longValue();
            case "less_equal":
            case "<=":
                return actualValue <= expectedValue.longValue();
            case "not_equals":
            case "!=":
                return actualValue != expectedValue.longValue();
            default:
                log.warn("Unsupported number operator: {}", operator);
                return false;
        }
    }
    
    /**
     * 记录字段评估信息
     */
    private void recordFieldEvaluation(String fieldName, List<String> validValues, 
                                     Set<String> matchedJjbhs, boolean result, 
                                     LocalDateTime startTime, EvaluationContext context) {
        long duration = java.time.temporal.ChronoUnit.MILLIS.between(startTime, LocalDateTime.now());
        
        // 记录到临时结果
        context.setTempResult("lastFieldEvaluation", fieldName);
        context.setTempResult("lastValidValues", validValues);
        context.setTempResult("lastMatchedJjbhs", matchedJjbhs);
        context.setTempResult("lastFieldResult", result);
        context.setTempResult("lastFieldDuration", duration);
        
        log.info("Field evaluation completed - Field: {}, ValidValues: {}, MatchedCount: {}, Result: {}, Duration: {}ms",
                fieldName, validValues.size(), matchedJjbhs.size(), result, duration);
        
        if (log.isDebugEnabled()) {
            log.debug("Field evaluation details - Field: {}, ValidValues: {}, MatchedJjbhs: {}", 
                    fieldName, validValues, matchedJjbhs);
        }
    }
}
