package com.hl.rule.service.evaluator;

import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.AnalysisContext;
import com.hl.rule.service.evaluator.context.EvaluationContext;
import com.hl.rule.service.evaluator.result.EvaluationResult;

/**
 * 条件评估器接口
 *
 * 使用策略模式，支持多种类型的条件评估
 * 每个评估器负责特定类型的条件评估逻辑
 *
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
public interface ConditionEvaluator {

    /**
     * 评估条件（兼容旧版本接口）
     *
     * @param field 字段配置
     * @param context 分析上下文
     * @return 评估结果
     */
    boolean evaluate(JSONObject field, AnalysisContext context);

    /**
     * 评估条件并返回详细结果（新版本接口）
     *
     * @param field 字段配置
     * @param context 评估上下文
     * @return 详细的评估结果
     */
    EvaluationResult evaluateWithDetails(JSONObject field, EvaluationContext context);

    /**
     * 获取评估器类型
     *
     * @return 评估器类型标识
     */
    String getEvaluatorType();

    /**
     * 检查是否支持指定的字段类型
     *
     * @param fieldType 字段类型
     * @return 是否支持
     */
    boolean supports(String fieldType);
}