package com.hl.rule.service.evaluator;

import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.AnalysisContext;
import com.hl.rule.service.evaluator.context.EvaluationContext;
import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 抽象条件评估器基类
 * 
 * 使用模板方法模式，定义统一的评估流程
 * 提供通用的评估逻辑和详情记录功能
 * 子类只需实现具体的评估逻辑
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
public abstract class AbstractConditionEvaluator implements ConditionEvaluator {
    
    @Override
    public boolean evaluate(JSONObject field, AnalysisContext context) {
        // 兼容旧接口，转换为新的评估方式
        EvaluationContext evaluationContext = convertToEvaluationContext(context);
        EvaluationResult result = evaluateWithDetails(field, evaluationContext);
        return result.isResult();
    }
    
    @Override
    public EvaluationResult evaluateWithDetails(JSONObject field, EvaluationContext context) {
        LocalDateTime startTime = LocalDateTime.now();
        String conditionType = getConditionType();
        String description = buildDescription(field);
        
        try {
            // 前置验证
            if (!preValidate(field, context)) {
                return EvaluationResult.failure(conditionType, description, "Pre-validation failed");
            }
            
            // 设置当前字段到上下文
            context.setCurrentField(field);
            
            // 执行具体的评估逻辑
            boolean result = doEvaluate(field, context);
            
            // 创建成功的评估结果
            EvaluationResult evaluationResult = EvaluationResult.success(result, conditionType, description)
                    .setExecutionTime(startTime, LocalDateTime.now())
                    .addDetail("fieldConfig", field)
                    .addDetail("identifier", context.getIdentifier());
            
            // 添加具体评估器的详情
            addEvaluationDetails(evaluationResult, field, context, result);
            
            // 后置处理
            postProcess(evaluationResult, field, context);
            
            // 记录到上下文
            context.addEvaluationResult(evaluationResult);
            
            if (context.isDebugMode()) {
                log.debug("Evaluation completed: {} - {}", description, evaluationResult.getExecutionSummary());
            }
            
            return evaluationResult;
            
        } catch (Exception e) {
            log.error("Error evaluating condition: {}", description, e);
            
            EvaluationResult errorResult = EvaluationResult.failure(conditionType, description, e.getMessage())
                    .setExecutionTime(startTime, LocalDateTime.now())
                    .addDetail("fieldConfig", field)
                    .addDetail("identifier", context.getIdentifier())
                    .addDetail("exception", e.getClass().getSimpleName());
            
            context.addEvaluationResult(errorResult);
            return errorResult;
        }
    }
    
    /**
     * 具体的评估逻辑，由子类实现
     * 
     * @param field 字段配置
     * @param context 评估上下文
     * @return 评估结果
     */
    protected abstract boolean doEvaluate(JSONObject field, EvaluationContext context);
    
    /**
     * 获取条件类型，由子类实现
     * 
     * @return 条件类型
     */
    protected abstract String getConditionType();
    
    /**
     * 前置验证，子类可以重写
     * 
     * @param field 字段配置
     * @param context 评估上下文
     * @return 验证结果
     */
    protected boolean preValidate(JSONObject field, EvaluationContext context) {
        return field != null && context != null;
    }
    
    /**
     * 添加评估详情，子类可以重写
     * 
     * @param result 评估结果
     * @param field 字段配置
     * @param context 评估上下文
     * @param evaluationResult 评估结果值
     */
    protected void addEvaluationDetails(EvaluationResult result, JSONObject field, 
                                      EvaluationContext context, boolean evaluationResult) {
        // 添加基础详情
        result.addDetail("conditionType", getConditionType())
              .addDetail("evaluationResult", evaluationResult)
              .addDetail("debugMode", context.isDebugMode());
        
        // 添加字段信息
        if (field != null) {
            result.addDetail("column", field.getString("column"))
                  .addDetail("operator", field.getString("opt"))
                  .addDetail("value", field.get("value"));
        }
        
        // 添加临时结果
        if (context.getTempResults() != null && !context.getTempResults().isEmpty()) {
            result.addDetail("tempResults", new HashMap<>(context.getTempResults()));
        }
    }
    
    /**
     * 后置处理，子类可以重写
     * 
     * @param result 评估结果
     * @param field 字段配置
     * @param context 评估上下文
     */
    protected void postProcess(EvaluationResult result, JSONObject field, EvaluationContext context) {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 构建描述信息
     * 
     * @param field 字段配置
     * @return 描述信息
     */
    protected String buildDescription(JSONObject field) {
        if (field == null) {
            return getConditionType() + " evaluation";
        }
        
        String column = field.getString("column");
        String operator = field.getString("opt");
        Object value = field.get("value");
        
        return String.format("%s: %s %s %s", getConditionType(), column, operator, value);
    }
    
    /**
     * 转换旧版本上下文为新版本上下文
     * 
     * @param oldContext 旧版本上下文
     * @return 新版本上下文
     */
    protected EvaluationContext convertToEvaluationContext(AnalysisContext oldContext) {
        if (oldContext == null) {
            return EvaluationContext.create("unknown");
        }
        
        EvaluationContext newContext = EvaluationContext.create(oldContext.getIdentifier());
        
        // 转换数据
        Map<String, Object> data = new HashMap<>();
        data.put("analysisContext", oldContext);
        data.put("rules", oldContext.getRules());
        data.put("identifier", oldContext.getIdentifier());
        
        newContext.setData(data);
        
        return newContext;
    }
    
    /**
     * 验证字段配置
     * 
     * @param field 字段配置
     * @return 验证结果
     */
    protected boolean validateFieldConfig(JSONObject field) {
        if (field == null) {
            log.warn("Field configuration is null");
            return false;
        }
        
        String column = field.getString("column");
        if (column == null || column.trim().isEmpty()) {
            log.warn("Field column is null or empty");
            return false;
        }
        
        String operator = field.getString("opt");
        if (operator == null || operator.trim().isEmpty()) {
            log.warn("Field operator is null or empty");
            return false;
        }
        
        return true;
    }
    
    /**
     * 记录调试信息
     * 
     * @param context 评估上下文
     * @param message 消息
     * @param args 参数
     */
    protected void logDebug(EvaluationContext context, String message, Object... args) {
        if (context.isDebugMode()) {
            log.debug("[{}] {}", getConditionType(), String.format(message, args));
        }
    }
    
    /**
     * 记录警告信息
     * 
     * @param context 评估上下文
     * @param message 消息
     * @param args 参数
     */
    protected void logWarn(EvaluationContext context, String message, Object... args) {
        log.warn("[{}] {}", getConditionType(), String.format(message, args));
    }
    
    /**
     * 记录错误信息
     * 
     * @param context 评估上下文
     * @param message 消息
     * @param throwable 异常
     */
    protected void logError(EvaluationContext context, String message, Throwable throwable) {
        log.error("[{}] {}", getConditionType(), message, throwable);
    }
    
    @Override
    public boolean supports(String fieldType) {
        return getConditionType().equalsIgnoreCase(fieldType);
    }
    
    @Override
    public String getEvaluatorType() {
        return getConditionType();
    }
}
