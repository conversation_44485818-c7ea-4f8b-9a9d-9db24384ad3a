package com.hl.rule.service.evaluator.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;

/**
 * 评估结果
 * 
 * 封装条件评估的详细结果，包括执行时间、详细信息、错误信息等
 * 支持链式调用，便于构建复杂的结果对象
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationResult {
    
    /**
     * 评估结果（true/false）
     */
    private boolean result;
    
    /**
     * 评估器类型
     */
    private String evaluatorType;
    
    /**
     * 条件描述
     */
    private String description;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 详细信息
     */
    private Map<String, Object> details;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 是否成功执行（无异常）
     */
    private boolean success;
    
    /**
     * 创建成功的评估结果
     * 
     * @param result 评估结果
     * @param evaluatorType 评估器类型
     * @param description 描述
     * @return 评估结果对象
     */
    public static EvaluationResult success(boolean result, String evaluatorType, String description) {
        LocalDateTime now = LocalDateTime.now();
        return EvaluationResult.builder()
                .result(result)
                .evaluatorType(evaluatorType)
                .description(description)
                .startTime(now)
                .endTime(now)
                .success(true)
                .details(new HashMap<String, Object>())
                .build();
    }
    
    /**
     * 创建失败的评估结果
     * 
     * @param evaluatorType 评估器类型
     * @param description 描述
     * @param errorMessage 错误信息
     * @return 评估结果对象
     */
    public static EvaluationResult failure(String evaluatorType, String description, String errorMessage) {
        LocalDateTime now = LocalDateTime.now();
        return EvaluationResult.builder()
                .result(false)
                .evaluatorType(evaluatorType)
                .description(description)
                .errorMessage(errorMessage)
                .startTime(now)
                .endTime(now)
                .success(false)
                .details(new HashMap<String, Object>())
                .build();
    }
    
    /**
     * 添加详细信息
     * 
     * @param key 键
     * @param value 值
     * @return 当前结果对象（支持链式调用）
     */
    public EvaluationResult addDetail(String key, Object value) {
        if (this.details == null) {
            this.details = new HashMap<>();
        }
        this.details.put(key, value);
        return this;
    }
    
    /**
     * 批量添加详细信息
     * 
     * @param details 详细信息映射
     * @return 当前结果对象（支持链式调用）
     */
    public EvaluationResult addDetails(Map<String, Object> details) {
        if (this.details == null) {
            this.details = new HashMap<>();
        }
        if (details != null) {
            this.details.putAll(details);
        }
        return this;
    }
    
    /**
     * 设置执行时间
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 当前结果对象（支持链式调用）
     */
    public EvaluationResult setExecutionTime(LocalDateTime startTime, LocalDateTime endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
        return this;
    }
    
    /**
     * 获取执行时长（毫秒）
     * 
     * @return 执行时长
     */
    public long getDuration() {
        if (startTime != null && endTime != null) {
            return ChronoUnit.MILLIS.between(startTime, endTime);
        }
        return 0;
    }
    
    /**
     * 获取执行时长（格式化字符串）
     * 
     * @return 格式化的执行时长
     */
    public String getFormattedDuration() {
        long duration = getDuration();
        if (duration < 1000) {
            return duration + "ms";
        } else if (duration < 60000) {
            return String.format("%.2fs", duration / 1000.0);
        } else {
            return String.format("%.2fm", duration / 60000.0);
        }
    }
    
    /**
     * 获取详细信息
     * 
     * @param key 键
     * @return 值
     */
    public Object getDetail(String key) {
        return details != null ? details.get(key) : null;
    }
    
    /**
     * 获取指定类型的详细信息
     * 
     * @param key 键
     * @param type 类型
     * @param <T> 泛型类型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public <T> T getDetail(String key, Class<T> type) {
        Object value = getDetail(key);
        if (value == null) {
            return null;
        }
        if (type.isInstance(value)) {
            return (T) value;
        }
        throw new ClassCastException("Cannot cast " + value.getClass() + " to " + type);
    }
    
    /**
     * 检查是否通过评估
     * 
     * @return 是否通过
     */
    public boolean isPassed() {
        return success && result;
    }
    
    /**
     * 检查是否失败
     * 
     * @return 是否失败
     */
    public boolean isFailed() {
        return !success || (success && !result);
    }
    
    /**
     * 获取执行摘要
     * 
     * @return 执行摘要
     */
    public String getExecutionSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Result: ").append(result ? "PASS" : "FAIL");
        summary.append(", Type: ").append(evaluatorType);
        summary.append(", Duration: ").append(getFormattedDuration());
        if (!success && errorMessage != null) {
            summary.append(", Error: ").append(errorMessage);
        }
        return summary.toString();
    }
    
    /**
     * 获取详细的结果报告
     * 
     * @return 详细报告
     */
    public Map<String, Object> getDetailedReport() {
        Map<String, Object> report = new HashMap<>();
        report.put("result", result);
        report.put("success", success);
        report.put("evaluatorType", evaluatorType);
        report.put("description", description);
        report.put("startTime", startTime);
        report.put("endTime", endTime);
        report.put("duration", getDuration());
        report.put("formattedDuration", getFormattedDuration());
        report.put("errorMessage", errorMessage);
        report.put("detailsCount", details != null ? details.size() : 0);
        
        if (details != null && !details.isEmpty()) {
            report.put("details", new HashMap<>(details));
        }
        
        return report;
    }
    
    /**
     * 转换为简单的布尔结果（兼容旧版本）
     * 
     * @return 布尔结果
     */
    public boolean toBoolean() {
        return isPassed();
    }
    
    /**
     * 合并另一个评估结果
     * 
     * @param other 另一个结果
     * @param operator 合并操作符（"and" 或 "or"）
     * @return 合并后的结果
     */
    public EvaluationResult merge(EvaluationResult other, String operator) {
        if (other == null) {
            return this;
        }
        
        boolean mergedResult;
        if ("and".equalsIgnoreCase(operator)) {
            mergedResult = this.result && other.result;
        } else if ("or".equalsIgnoreCase(operator)) {
            mergedResult = this.result || other.result;
        } else {
            throw new IllegalArgumentException("Unsupported operator: " + operator);
        }
        
        LocalDateTime earliestStart = this.startTime != null && other.startTime != null ?
                (this.startTime.isBefore(other.startTime) ? this.startTime : other.startTime) :
                (this.startTime != null ? this.startTime : other.startTime);
        
        LocalDateTime latestEnd = this.endTime != null && other.endTime != null ?
                (this.endTime.isAfter(other.endTime) ? this.endTime : other.endTime) :
                (this.endTime != null ? this.endTime : other.endTime);
        
        EvaluationResult merged = EvaluationResult.success(mergedResult, "MERGED", 
                String.format("Merged result using %s operator", operator.toUpperCase()));
        
        merged.setExecutionTime(earliestStart, latestEnd);
        merged.addDetail("operator", operator);
        merged.addDetail("leftResult", this.getDetailedReport());
        merged.addDetail("rightResult", other.getDetailedReport());
        
        return merged;
    }
    
    @Override
    public String toString() {
        return String.format("EvaluationResult{result=%s, type='%s', description='%s', duration=%s, success=%s}", 
                result, evaluatorType, description, getFormattedDuration(), success);
    }
}
