package com.hl.rule.service.evaluator.field.impl;

import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.evaluator.context.EvaluationContext;
import com.hl.rule.service.evaluator.field.AbstractFieldEvaluator;
import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 内容字段评估器
 * 
 * 处理内容相关字段(bjnr, cjnr, zxcjnr)的条件评估
 * 支持关键词匹配和内容分析
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@Component
public class ContentFieldEvaluator extends AbstractFieldEvaluator {
    
    private static final String[] SUPPORTED_FIELDS = {"bjnr", "cjnr", "zxcjnr"};
    
    @Override
    protected boolean doEvaluate(JSONObject field, EvaluationContext context) {
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            String column = field.getString("column");
            String operator = field.getString("opt");
            String searchValue = field.getString("value");
            
            if (searchValue == null || searchValue.trim().isEmpty()) {
                logWarn(context, "Search value is null or empty for field: {}", column);
                return false;
            }
            
            // 模拟内容匹配逻辑
            Set<String> matchedJjbhs = simulateContentMatching(context, column, operator, searchValue);
            
            // 更新匹配的警情编号
            updateMatchedJjbhs(context, matchedJjbhs);
            
            // 评估数量条件（如果有second_opt和second_value）
            boolean result;
            String secondOpt = field.getString("second_opt");
            Long secondValue = field.getLongValue("second_value");
            
            if (secondOpt != null && secondValue != null) {
                // 有数量条件，评估匹配数量
                result = evaluateNumberCondition(secondOpt, matchedJjbhs.size(), secondValue);
            } else {
                // 没有数量条件，只要有匹配就返回true
                result = !matchedJjbhs.isEmpty();
            }
            
            // 记录详细信息
            recordFieldEvaluation(column, searchValue, matchedJjbhs, result, startTime, context);
            
            return result;
            
        } catch (Exception e) {
            logError(context, "Error evaluating content field", e);
            return false;
        }
    }
    
    @Override
    protected String[] getFieldNames() {
        return SUPPORTED_FIELDS;
    }
    
    @Override
    public String getFieldType() {
        return "CONTENT_FIELD";
    }
    
    @Override
    protected void addFieldEvaluationDetails(EvaluationResult result, JSONObject field, 
                                           EvaluationContext context, boolean evaluationResult) {
        super.addFieldEvaluationDetails(result, field, context, evaluationResult);
        
        String column = field.getString("column");
        String searchValue = field.getString("value");
        
        // 添加内容字段特有的详情
        result.addDetail("fieldCategory", "内容字段")
              .addDetail("evaluationType", "内容匹配")
              .addDetail("businessType", "警情分析")
              .addDetail("searchValue", searchValue);
        
        // 根据字段类型添加具体描述
        switch (column) {
            case "bjnr":
                result.addDetail("contentFieldType", "报警内容");
                break;
            case "cjnr":
                result.addDetail("contentFieldType", "处警内容");
                break;
            case "zxcjnr":
                result.addDetail("contentFieldType", "最新处警内容");
                break;
        }
        
        // 添加搜索关键词分析
        if (searchValue != null) {
            String[] keywords = searchValue.split("[,，\\s]+");
            result.addDetail("keywordCount", keywords.length)
                  .addDetail("keywords", Arrays.asList(keywords));
        }
        
        // 添加匹配信息
        if (context.getMatchedJjbhs() != null && !context.getMatchedJjbhs().isEmpty()) {
            result.addDetail("matchedJjbhs", new ArrayList<>(context.getMatchedJjbhs()))
                  .addDetail("matchedCount", context.getMatchedJjbhs().size());
        }
        
        // 添加数量条件信息
        String secondOpt = field.getString("second_opt");
        Long secondValue = field.getLongValue("second_value");
        if (secondOpt != null && secondValue != null) {
            result.addDetail("hasQuantityCondition", true)
                  .addDetail("quantityOperator", secondOpt)
                  .addDetail("quantityValue", secondValue);
        } else {
            result.addDetail("hasQuantityCondition", false);
        }
    }
    
    /**
     * 模拟内容匹配逻辑
     */
    private Set<String> simulateContentMatching(EvaluationContext context, String column, 
                                              String operator, String searchValue) {
        Set<String> matchedJjbhs = new HashSet<>();
        
        // 分割搜索关键词
        String[] keywords = searchValue.split("[,，\\s]+");
        
        // 模拟内容库
        String[] simulatedContents = {
            "发生盗窃案件，嫌疑人已逃离现场",
            "交通事故处理，现场已清理完毕",
            "邻里纠纷调解，双方已达成和解",
            "抢劫案件紧急处警，嫌疑人已抓获",
            "火灾事故处理，消防队已到场",
            "醉酒驾驶查处，当事人已带回",
            "家庭暴力报警，已进行调解",
            "诈骗案件受理，正在调查中"
        };
        
        // 根据操作符进行匹配
        for (int i = 0; i < simulatedContents.length; i++) {
            String content = simulatedContents[i];
            boolean matches = false;
            
            switch (operator) {
                case "contains":
                    matches = containsAnyKeyword(content, keywords);
                    break;
                case "equals":
                    matches = content.equals(searchValue);
                    break;
                case "starts_with":
                    matches = startsWithAnyKeyword(content, keywords);
                    break;
                case "ends_with":
                    matches = endsWithAnyKeyword(content, keywords);
                    break;
                default:
                    // 默认使用包含匹配
                    matches = containsAnyKeyword(content, keywords);
            }
            
            if (matches) {
                matchedJjbhs.add(column.toUpperCase() + "_CONTENT_" + System.currentTimeMillis() + "_" + i);
            }
        }
        
        logDebug(context, "Simulated {} matched jjbhs for content field: {} with keywords: {}", 
                matchedJjbhs.size(), column, Arrays.toString(keywords));
        
        return matchedJjbhs;
    }
    
    /**
     * 检查内容是否包含任意关键词
     */
    private boolean containsAnyKeyword(String content, String[] keywords) {
        for (String keyword : keywords) {
            if (keyword != null && !keyword.trim().isEmpty() && content.contains(keyword.trim())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查内容是否以任意关键词开头
     */
    private boolean startsWithAnyKeyword(String content, String[] keywords) {
        for (String keyword : keywords) {
            if (keyword != null && !keyword.trim().isEmpty() && content.startsWith(keyword.trim())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查内容是否以任意关键词结尾
     */
    private boolean endsWithAnyKeyword(String content, String[] keywords) {
        for (String keyword : keywords) {
            if (keyword != null && !keyword.trim().isEmpty() && content.endsWith(keyword.trim())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 更新匹配的警情编号
     */
    private void updateMatchedJjbhs(EvaluationContext context, Set<String> newJjbhs) {
        // 将匹配的警情编号添加到上下文
        for (String jjbh : newJjbhs) {
            context.addMatchedJjbh(jjbh);
        }
        logDebug(context, "Updated matched jjbhs: {}", newJjbhs);
    }
    
    /**
     * 评估数量条件
     */
    private boolean evaluateNumberCondition(String operator, int actualValue, Long expectedValue) {
        if (expectedValue == null) {
            return false;
        }
        
        switch (operator) {
            case "equals":
            case "=":
                return actualValue == expectedValue.longValue();
            case "greater_than":
            case ">":
                return actualValue > expectedValue.longValue();
            case "less_than":
            case "<":
                return actualValue < expectedValue.longValue();
            case "greater_equal":
            case ">=":
                return actualValue >= expectedValue.longValue();
            case "less_equal":
            case "<=":
                return actualValue <= expectedValue.longValue();
            case "not_equals":
            case "!=":
                return actualValue != expectedValue.longValue();
            default:
                log.warn("Unsupported number operator: {}", operator);
                return false;
        }
    }
    
    /**
     * 记录字段评估信息
     */
    private void recordFieldEvaluation(String fieldName, String searchValue, 
                                     Set<String> matchedJjbhs, boolean result, 
                                     LocalDateTime startTime, EvaluationContext context) {
        long duration = java.time.temporal.ChronoUnit.MILLIS.between(startTime, LocalDateTime.now());
        
        // 记录到临时结果
        context.setTempResult("lastFieldEvaluation", fieldName);
        context.setTempResult("lastSearchValue", searchValue);
        context.setTempResult("lastMatchedJjbhs", matchedJjbhs);
        context.setTempResult("lastFieldResult", result);
        context.setTempResult("lastFieldDuration", duration);
        
        log.info("Content field evaluation completed - Field: {}, SearchValue: {}, MatchedCount: {}, Result: {}, Duration: {}ms",
                fieldName, searchValue, matchedJjbhs.size(), result, duration);
        
        if (log.isDebugEnabled()) {
            log.debug("Content field evaluation details - Field: {}, SearchValue: {}, MatchedJjbhs: {}", 
                    fieldName, searchValue, matchedJjbhs);
        }
    }
}
