package com.hl.rule.service.evaluator;

import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.AnalysisContext;
import com.hl.rule.service.evaluator.context.EvaluationContext;
import com.hl.rule.service.evaluator.field.FieldEvaluatorFactory;
import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 人员条件评估器
 *
 * 处理人员相关的条件评估，包括报警、接警、处警等各种业务场景
 * 使用策略模式管理不同字段的评估逻辑
 * 提供详细的评估过程记录
 *
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PersonConditionEvaluator extends AbstractConditionEvaluator {

    /**
     * 字段评估器工厂
     */
    private final FieldEvaluatorFactory fieldEvaluatorFactory;

    @PostConstruct
    public void init() {
        log.info("PersonConditionEvaluator initialized with FieldEvaluatorFactory");
        log.info("Supported field types: {}", String.join(", ", fieldEvaluatorFactory.getSupportedFieldTypes()));
        log.info("Supported field names: {}", String.join(", ", fieldEvaluatorFactory.getSupportedFieldNames()));
    }

    @Override
    protected boolean doEvaluate(JSONObject field, EvaluationContext context) {
        String column = field.getString("column");

        // 记录评估开始
        logDebug(context, "Starting evaluation for column: {}", column);

        // 使用字段评估器工厂获取对应的评估器
        FieldEvaluator fieldEvaluator = fieldEvaluatorFactory.getEvaluatorByFieldName(column);
        if (fieldEvaluator == null) {
            logWarn(context, "No field evaluator found for column: {}", column);
            // 如果没有找到专门的字段评估器，使用默认逻辑
            return evaluateWithDefaultLogic(field, context);
        }

        try {
            // 使用字段评估器进行评估
            EvaluationResult fieldResult = fieldEvaluator.evaluateWithDetails(field, context);

            // 记录字段评估结果
            context.addEvaluationResult(fieldResult);

            // 记录评估详情
            recordEvaluationDetails(context, column, field, fieldResult.isResult());

            logDebug(context, "Field evaluation completed for column: {}, result: {}, evaluator: {}",
                    column, fieldResult.isResult(), fieldEvaluator.getFieldType());

            return fieldResult.isResult();

        } catch (Exception e) {
            logError(context, "Error evaluating column: " + column, e);
            return false;
        }
    }

    @Override
    protected String getConditionType() {
        return "PERSON_CONDITION";
    }

    @Override
    protected void addEvaluationDetails(EvaluationResult result, JSONObject field,
                                      EvaluationContext context, boolean evaluationResult) {
        super.addEvaluationDetails(result, field, context, evaluationResult);

        String column = field.getString("column");

        // 添加人员条件特有的详情
        result.addDetail("fieldType", "person")
              .addDetail("evaluatorClass", this.getClass().getSimpleName())
              .addDetail("fieldEvaluatorFactory", fieldEvaluatorFactory.getClass().getSimpleName())
              .addDetail("supportedFieldTypes", fieldEvaluatorFactory.getSupportedFieldTypes())
              .addDetail("supportedFieldNames", fieldEvaluatorFactory.getSupportedFieldNames());

        // 添加匹配的警情编号
        if (context.getMatchedJjbhs() != null && !context.getMatchedJjbhs().isEmpty()) {
            result.addDetail("matchedJjbhs", new ArrayList<>(context.getMatchedJjbhs()))
                  .addDetail("matchedCount", context.getMatchedJjbhs().size());
        }

        // 添加字段特定的详情
        addFieldSpecificDetails(result, column, field, context);

        // 添加字段评估器信息
        com.hl.rule.service.evaluator.FieldEvaluator fieldEvaluator = fieldEvaluatorFactory.getEvaluatorByFieldName(column);
        if (fieldEvaluator != null) {
            result.addDetail("usedFieldEvaluator", fieldEvaluator.getFieldType())
                  .addDetail("fieldEvaluatorClass", fieldEvaluator.getClass().getSimpleName());
        }
    }

    /**
     * 添加字段特定的详情
     */
    private void addFieldSpecificDetails(EvaluationResult result, String column,
                                       JSONObject field, EvaluationContext context) {
        switch (column) {
            case "bjlx":
            case "cjlx":
            case "zxcjlx":
                addTypeFieldDetails(result, field, "类型字段");
                break;
            case "jjdw":
            case "cjdw":
            case "zxjjdw":
            case "zxcjdw":
                addUnitFieldDetails(result, field, "单位字段");
                break;
            case "djsj":
            case "cjsj":
                addTimeFieldDetails(result, field, "时间字段");
                break;
            case "bjnr":
            case "cjnr":
            case "zxcjnr":
                addContentFieldDetails(result, field, "内容字段");
                break;
            case "stbq":
            case "zxstbq":
            case "fjbq":
            case "zxfjbq":
                addTagFieldDetails(result, field, "标签字段");
                break;
            default:
                result.addDetail("fieldCategory", "其他字段");
        }
    }

    /**
     * 默认评估逻辑（当没有找到专门的字段评估器时使用）
     */
    private boolean evaluateWithDefaultLogic(JSONObject field, EvaluationContext context) {
        String column = field.getString("column");
        LocalDateTime startTime = LocalDateTime.now();

        logDebug(context, "Using default evaluation logic for column: {}", column);

        try {
            // 根据字段名称选择合适的默认逻辑
//            switch (column) {
//                case "cjlx":
//                    return evaluateCjlxDefault(field, context);
//                case "zxjjdw":
//                    return evaluateZxjjdwDefault(field, context);
//                case "zxcjdw":
//                    return evaluateZxcjdwDefault(field, context);
//                case "bjnr":
//                case "cjnr":
//                case "zxcjnr":
//                    return evaluateContentDefault(field, context);
//                case "stbq":
//                case "zxstbq":
//                case "fjbq":
//                case "zxfjbq":
//                    return evaluateTagDefault(field, context);
//                case "zxcjlx":
//                    return evaluateZxcjlxDefault(field, context);
//                default:
//                    logWarn(context, "No default logic available for column: {}", column);
//                    return false;
//            }
        } catch (Exception e) {
            logError(context, "Error in default evaluation for column: " + column, e);
            return false;
        }
        return true;
    }
//
//    /**
//     * 默认处警类型评估
//     */
//    private boolean evaluateCjlxDefault(JSONObject field, EvaluationContext context) {
//        if (!"count".equals(field.getString("type"))) {
//            return false;
//        }
//
//        List<String> validValues = extractValidValues(field, "dict_value");
//        Set<String> matchedJjbhs = simulateMatchedJjbhs(context, validValues, "cjlx");
//        updateMatchedJjbhs(context, matchedJjbhs);
//
//        return evaluateNumberCondition(
//                field.getString("opt"),
//                matchedJjbhs.size(),
//                field.getLongValue("value")
//        );
//    }
//
//    /**
//     * 默认最新接警单位评估
//     */
//    private boolean evaluateZxjjdwDefault(JSONObject field, EvaluationContext context) {
//        List<String> allowedPoliceStations = extractValidValues(field, "dict_value");
//        if (allowedPoliceStations.isEmpty()) {
//            return false;
//        }
//
//        // 模拟最新接警单位匹配
//        String latestJjdw = simulateLatestJjdw(context);
//        boolean matches = allowedPoliceStations.contains(latestJjdw);
//
//        if (matches) {
//            context.addMatchedJjbh("LATEST_JJ_" + System.currentTimeMillis());
//        }
//
//        return matches;
//    }
//
//    /**
//     * 默认最新处警单位评估
//     */
//    private boolean evaluateZxcjdwDefault(JSONObject field, EvaluationContext context) {
//        List<String> allowedPoliceStations = extractValidValues(field, "value");
//        if (allowedPoliceStations.isEmpty()) {
//            return false;
//        }
//
//        // 模拟最新处警单位匹配
//        String latestCjdw = simulateLatestCjdw(context);
//        boolean matches = allowedPoliceStations.contains(latestCjdw);
//
//        if (matches) {
//            context.addMatchedJjbh("LATEST_CJ_" + System.currentTimeMillis());
//        }
//
//        return matches;
//    }
//
//    /**
//     * 默认内容字段评估
//     */
//    private boolean evaluateContentDefault(JSONObject field, EvaluationContext context) {
//        String value = field.getString("value");
//        if (value == null || value.trim().isEmpty()) {
//            return false;
//        }
//
//        // 模拟内容匹配
//        boolean matches = simulateContentMatching(context, value);
//
//        if (matches) {
//            context.addMatchedJjbh("CONTENT_" + System.currentTimeMillis());
//        }
//
//        return matches;
//    }
//
////    private void updateMatchedJjbhs(AnalysisContext context, Set<String> newJjbhs) {
////        if (context.getMatchedJjbhs() == null) {
////            context.setMatchedJjbhs(new HashSet<>(newJjbhs));
////        } else {
////            context.getMatchedJjbhs().retainAll(newJjbhs);
////        }
////    }
//
//    /**
//     * 默认标签字段评估
//     */
//    private boolean evaluateTagDefault(JSONObject field, EvaluationContext context) {
//        List<String> validValues = extractValidValues(field, "dict_value");
//        if (validValues.isEmpty()) {
//            return false;
//        }
//
//        // 模拟标签匹配
//        Set<String> matchedJjbhs = simulateTagMatching(context, validValues);
//        updateMatchedJjbhs(context, matchedJjbhs);
//
//        return evaluateNumberCondition(
//                field.getString("opt"),
//                matchedJjbhs.size(),
//                field.getLongValue("value")
//        );
//    }
//
//    /**
//     * 默认最新处警类型评估
//     */
//    private boolean evaluateZxcjlxDefault(JSONObject field, EvaluationContext context) {
//        List<String> validValues = extractValidValues(field, "value");
//        if (validValues.isEmpty()) {
//            return false;
//        }
//
//        // 模拟最新处警类型匹配
//        String latestCjlx = simulateLatestCjlx(context);
//        boolean matches = validValues.contains(latestCjlx);
//
//        if (matches) {
//            context.addMatchedJjbh("LATEST_CJLX_" + System.currentTimeMillis());
//        }
//
//        return matches;
//    }
//
//    // ==================== 模拟方法（用于演示） ====================
//
//    /**
//     * 模拟最新接警单位
//     */
//    private String simulateLatestJjdw(EvaluationContext context) {
//        // 模拟逻辑：返回一个模拟的接警单位
//        String[] units = {"派出所A", "派出所B", "派出所C"};
//        return units[(int) (Math.random() * units.length)];
//    }
//
//    /**
//     * 模拟最新处警单位
//     */
//    private String simulateLatestCjdw(EvaluationContext context) {
//        // 模拟逻辑：返回一个模拟的处警单位
//        String[] units = {"处警队A", "处警队B", "处警队C"};
//        return units[(int) (Math.random() * units.length)];
//    }
//
//    /**
//     * 模拟最新处警类型
//     */
//    private String simulateLatestCjlx(EvaluationContext context) {
//        // 模拟逻辑：返回一个模拟的处警类型
//        String[] types = {"一般处警", "紧急处警", "重大处警"};
//        return types[(int) (Math.random() * types.length)];
//    }
//
//    /**
//     * 模拟内容匹配
//     */
//    private boolean simulateContentMatching(EvaluationContext context, String content) {
//        // 模拟逻辑：简单的关键词匹配
//        String[] keywords = {"盗窃", "抢劫", "交通", "纠纷"};
//        for (String keyword : keywords) {
//            if (content.contains(keyword)) {
//                return true;
//            }
//        }
//        return false;
//    }
//
//    /**
//     * 模拟标签匹配
//     */
//    private Set<String> simulateTagMatching(EvaluationContext context, List<String> validTags) {
//        Set<String> matchedJjbhs = new HashSet<>();
//
//        // 模拟逻辑：根据标签生成匹配的警情编号
//        for (String tag : validTags) {
//            if (Math.random() > 0.5) { // 50%的概率匹配
//                matchedJjbhs.add("TAG_" + tag + "_" + System.currentTimeMillis());
//            }
//        }
//
//        return matchedJjbhs;
//    }

    // ==================== 重构后的辅助方法 ====================

    /**
     * 提取分析上下文（兼容旧版本）
     */
    private AnalysisContext extractAnalysisContext(EvaluationContext context) {
        return context.getData("analysisContext", AnalysisContext.class);
    }

    /**
     * 记录评估详情
     */
    private void recordEvaluationDetails(EvaluationContext context, String column,
                                       JSONObject field, boolean result) {
        context.setTempResult("lastEvaluatedColumn", column);
        context.setTempResult("lastEvaluationResult", result);
        context.setTempResult("lastEvaluationTime", LocalDateTime.now());

        // 记录字段配置
        Map<String, Object> fieldConfig = new HashMap<>();
        fieldConfig.put("column", column);
        fieldConfig.put("operator", field.getString("opt"));
        fieldConfig.put("value", field.get("value"));
        fieldConfig.put("type", field.getString("type"));
        context.setTempResult("lastFieldConfig", fieldConfig);
    }

    /**
     * 提取有效值列表
     */
    private List<String> extractValidValues(JSONObject field, String arrayKey) {
        try {
            if (field.getJSONArray(arrayKey) != null) {
                return field.getJSONArray(arrayKey)
                        .toJavaList(String.class)
                        .stream()
                        .map(str -> str.length() > 3 ? str.substring(3) : str)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("Error extracting valid values from field: {}", arrayKey, e);
        }
        return new ArrayList<>();
    }

    /**
     * 模拟匹配的警情编号（用于演示）
     */
    private Set<String> simulateMatchedJjbhs(AnalysisContext context, List<String> validValues, String fieldType) {
        Set<String> matchedJjbhs = new HashSet<>();

        // 模拟逻辑：根据字段类型和有效值生成一些匹配的警情编号
        for (int i = 0; i < validValues.size() && i < 3; i++) {
            matchedJjbhs.add(fieldType.toUpperCase() + "_" + System.currentTimeMillis() + "_" + i);
        }

        log.debug("Simulated {} matched jjbhs for field type: {}", matchedJjbhs.size(), fieldType);
        return matchedJjbhs;
    }

    /**
     * 更新匹配的警情编号
     */
    private void updateMatchedJjbhs(AnalysisContext context, Set<String> newJjbhs) {
        // 这里应该更新上下文中的匹配警情编号
        // 由于原始代码被注释，这里提供一个模拟实现
        log.debug("Updated matched jjbhs: {}", newJjbhs);
    }

    /**
     * 评估数量条件
     */
    private boolean evaluateNumberCondition(String operator, int actualValue, Long expectedValue) {
        if (expectedValue == null) {
            return false;
        }

        switch (operator) {
            case "equals":
            case "=":
                return actualValue == expectedValue.longValue();
            case "greater_than":
            case ">":
                return actualValue > expectedValue.longValue();
            case "less_than":
            case "<":
                return actualValue < expectedValue.longValue();
            case "greater_equal":
            case ">=":
                return actualValue >= expectedValue.longValue();
            case "less_equal":
            case "<=":
                return actualValue <= expectedValue.longValue();
            case "not_equals":
            case "!=":
                return actualValue != expectedValue.longValue();
            default:
                log.warn("Unsupported number operator: {}", operator);
                return false;
        }
    }

    /**
     * 记录字段评估信息
     */
    private void recordFieldEvaluation(String fieldName, List<String> validValues,
                                     Set<String> matchedJjbhs, boolean result, LocalDateTime startTime) {
        long duration = java.time.temporal.ChronoUnit.MILLIS.between(startTime, LocalDateTime.now());

        log.info("Field evaluation completed - Field: {}, ValidValues: {}, MatchedCount: {}, Result: {}, Duration: {}ms",
                fieldName, validValues.size(), matchedJjbhs.size(), result, duration);

        if (log.isDebugEnabled()) {
            log.debug("Field evaluation details - Field: {}, ValidValues: {}, MatchedJjbhs: {}",
                    fieldName, validValues, matchedJjbhs);
        }
    }

    /**
     * 添加类型字段详情
     */
    private void addTypeFieldDetails(EvaluationResult result, JSONObject field, String category) {
        result.addDetail("fieldCategory", category);
        result.addDetail("fieldSubType", "类型选择");
        if (field.getJSONArray("dict_value") != null) {
            result.addDetail("dictValueCount", field.getJSONArray("dict_value").size());
        }
    }

    /**
     * 添加单位字段详情
     */
    private void addUnitFieldDetails(EvaluationResult result, JSONObject field, String category) {
        result.addDetail("fieldCategory", category);
        result.addDetail("fieldSubType", "单位选择");
        if (field.getJSONArray("dict_value") != null) {
            result.addDetail("unitCount", field.getJSONArray("dict_value").size());
        }
    }

    /**
     * 添加时间字段详情
     */
    private void addTimeFieldDetails(EvaluationResult result, JSONObject field, String category) {
        result.addDetail("fieldCategory", category);
        result.addDetail("fieldSubType", "时间比较");
        result.addDetail("timeValue", field.getString("value"));
        result.addDetail("timeOperator", field.getString("opt"));
    }

    /**
     * 添加内容字段详情
     */
    private void addContentFieldDetails(EvaluationResult result, JSONObject field, String category) {
        result.addDetail("fieldCategory", category);
        result.addDetail("fieldSubType", "内容匹配");
        result.addDetail("contentValue", field.getString("value"));
        result.addDetail("contentOperator", field.getString("opt"));
    }

    /**
     * 添加标签字段详情
     */
    private void addTagFieldDetails(EvaluationResult result, JSONObject field, String category) {
        result.addDetail("fieldCategory", category);
        result.addDetail("fieldSubType", "标签匹配");
        if (field.getJSONArray("dict_value") != null) {
            result.addDetail("tagCount", field.getJSONArray("dict_value").size());
        }
    }
}