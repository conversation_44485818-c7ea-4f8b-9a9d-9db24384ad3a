package com.hl.rule.service.evaluator.field.impl;

import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.evaluator.context.EvaluationContext;
import com.hl.rule.service.evaluator.field.AbstractFieldEvaluator;
import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 接警单位字段评估器
 * 
 * 处理接警单位(jjdw)相关的条件评估
 * 支持单位匹配和数量比较
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@Component
public class JjdwFieldEvaluator extends AbstractFieldEvaluator {
    
    private static final String[] SUPPORTED_FIELDS = {"jjdw"};
    
    @Override
    protected boolean doEvaluate(JSONObject field, EvaluationContext context) {
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            // 提取有效值列表
            List<String> validValues = extractValidValues(field, "dict_value");
            if (validValues.isEmpty()) {
                logWarn(context, "No valid values found for jjdw evaluation");
                return false;
            }

            // 模拟接警单位匹配逻辑
            Set<String> matchedJjbhs = simulateJjdwMatching(context, validValues);
            
            // 更新匹配的警情编号
            updateMatchedJjbhs(context, matchedJjbhs);
            
            // 评估数量条件
            boolean result = evaluateNumberCondition(
                    field.getString("opt"),
                    matchedJjbhs.size(),
                    field.getLongValue("value")
            );
            
            // 记录详细信息
            recordFieldEvaluation("jjdw", validValues, matchedJjbhs, result, startTime, context);
            
            return result;
            
        } catch (Exception e) {
            logError(context, "Error evaluating jjdw field", e);
            return false;
        }
    }
    
    @Override
    protected String[] getFieldNames() {
        return SUPPORTED_FIELDS;
    }
    
    @Override
    public String getFieldType() {
        return "JJDW_FIELD";
    }
    
    @Override
    protected void addFieldEvaluationDetails(EvaluationResult result, JSONObject field, 
                                           EvaluationContext context, boolean evaluationResult) {
        super.addFieldEvaluationDetails(result, field, context, evaluationResult);
        
        // 添加接警单位特有的详情
        result.addDetail("fieldCategory", "接警单位")
              .addDetail("evaluationType", "单位匹配")
              .addDetail("businessType", "警情分析");
        
        // 添加字典值信息
        if (field.getJSONArray("dict_value") != null) {
            result.addDetail("unitCount", field.getJSONArray("dict_value").size());
            result.addDetail("units", field.getJSONArray("dict_value").toJavaList(String.class));
        }
        
        // 添加匹配信息
        if (context.getMatchedJjbhs() != null && !context.getMatchedJjbhs().isEmpty()) {
            result.addDetail("matchedJjbhs", new ArrayList<>(context.getMatchedJjbhs()))
                  .addDetail("matchedCount", context.getMatchedJjbhs().size());
        }
    }
    
    /**
     * 提取有效值列表
     */
    private List<String> extractValidValues(JSONObject field, String arrayKey) {
        try {
            if (field.getJSONArray(arrayKey) != null) {
                return field.getJSONArray(arrayKey)
                        .toJavaList(String.class)
                        .stream()
                        .filter(str -> str != null && !str.trim().isEmpty())
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("Error extracting valid values from field: {}", arrayKey, e);
        }
        return new ArrayList<>();
    }
    
    /**
     * 模拟接警单位匹配逻辑
     */
    private Set<String> simulateJjdwMatching(EvaluationContext context, List<String> validUnits) {
        Set<String> matchedJjbhs = new HashSet<>();
        
        // 模拟逻辑：根据接警单位生成匹配的警情编号
        for (String unit : validUnits) {
            // 模拟每个单位可能匹配1-2个警情
            int matchCount = (int) (Math.random() * 2) + 1;
            for (int i = 0; i < matchCount; i++) {
                matchedJjbhs.add("JJDW_" + unit + "_" + System.currentTimeMillis() + "_" + i);
            }
        }
        
        logDebug(context, "Simulated {} matched jjbhs for {} units", matchedJjbhs.size(), validUnits.size());
        return matchedJjbhs;
    }
    
    /**
     * 更新匹配的警情编号
     */
    private void updateMatchedJjbhs(EvaluationContext context, Set<String> newJjbhs) {
        // 将匹配的警情编号添加到上下文
        for (String jjbh : newJjbhs) {
            context.addMatchedJjbh(jjbh);
        }
        logDebug(context, "Updated matched jjbhs: {}", newJjbhs);
    }
    
    /**
     * 评估数量条件
     */
    private boolean evaluateNumberCondition(String operator, int actualValue, Long expectedValue) {
        if (expectedValue == null) {
            return false;
        }
        
        switch (operator) {
            case "equals":
            case "=":
                return actualValue == expectedValue.longValue();
            case "greater_than":
            case ">":
                return actualValue > expectedValue.longValue();
            case "less_than":
            case "<":
                return actualValue < expectedValue.longValue();
            case "greater_equal":
            case ">=":
                return actualValue >= expectedValue.longValue();
            case "less_equal":
            case "<=":
                return actualValue <= expectedValue.longValue();
            case "not_equals":
            case "!=":
                return actualValue != expectedValue.longValue();
            default:
                log.warn("Unsupported number operator: {}", operator);
                return false;
        }
    }
    
    /**
     * 记录字段评估信息
     */
    private void recordFieldEvaluation(String fieldName, List<String> validValues, 
                                     Set<String> matchedJjbhs, boolean result, 
                                     LocalDateTime startTime, EvaluationContext context) {
        long duration = java.time.temporal.ChronoUnit.MILLIS.between(startTime, LocalDateTime.now());
        
        // 记录到临时结果
        context.setTempResult("lastFieldEvaluation", fieldName);
        context.setTempResult("lastValidUnits", validValues);
        context.setTempResult("lastMatchedJjbhs", matchedJjbhs);
        context.setTempResult("lastFieldResult", result);
        context.setTempResult("lastFieldDuration", duration);
        
        log.info("Field evaluation completed - Field: {}, ValidUnits: {}, MatchedCount: {}, Result: {}, Duration: {}ms",
                fieldName, validValues.size(), matchedJjbhs.size(), result, duration);
        
        if (log.isDebugEnabled()) {
            log.debug("Field evaluation details - Field: {}, ValidUnits: {}, MatchedJjbhs: {}", 
                    fieldName, validValues, matchedJjbhs);
        }
    }
}
