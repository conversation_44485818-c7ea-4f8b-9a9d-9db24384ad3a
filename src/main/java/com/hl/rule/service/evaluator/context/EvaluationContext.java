package com.hl.rule.service.evaluator.context;

import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 评估上下文
 * 
 * 封装评估过程中的所有信息，包括数据、历史记录、调试信息等
 * 支持详细的条件记录和执行追踪
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationContext {
    
    /**
     * 评估目标标识符
     */
    private String identifier;
    
    /**
     * 评估数据存储
     */
    private Map<String, Object> data;
    
    /**
     * 评估历史记录
     */
    private List<EvaluationResult> evaluationHistory;
    
    /**
     * 临时结果存储
     */
    private Map<String, Object> tempResults;
    
    /**
     * 匹配的警情编号集合
     */
    private List<String> matchedJjbhs;
    
    /**
     * 评估开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 是否启用调试模式
     */
    private boolean debugMode;
    
    /**
     * 当前正在评估的字段配置
     */
    private Object currentField;
    
    /**
     * 创建评估上下文
     * 
     * @param identifier 标识符
     * @return 评估上下文
     */
    public static EvaluationContext create(String identifier) {
        return EvaluationContext.builder()
                .identifier(identifier)
                .data(new HashMap<String, Object>())
                .evaluationHistory(new ArrayList<EvaluationResult>())
                .tempResults(new HashMap<String, Object>())
                .matchedJjbhs(new ArrayList<String>())
                .startTime(LocalDateTime.now())
                .debugMode(false)
                .build();
    }
    
    /**
     * 创建带数据的评估上下文
     * 
     * @param identifier 标识符
     * @param data 初始数据
     * @return 评估上下文
     */
    public static EvaluationContext createWithData(String identifier, Map<String, Object> data) {
        EvaluationContext context = create(identifier);
        if (data != null) {
            context.data.putAll(data);
        }
        return context;
    }
    
    /**
     * 获取数据
     * 
     * @param key 键
     * @return 值
     */
    public Object getData(String key) {
        return data != null ? data.get(key) : null;
    }
    
    /**
     * 获取指定类型的数据
     * 
     * @param key 键
     * @param type 类型
     * @param <T> 泛型类型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public <T> T getData(String key, Class<T> type) {
        Object value = getData(key);
        if (value == null) {
            return null;
        }
        if (type.isInstance(value)) {
            return (T) value;
        }
        throw new ClassCastException("Cannot cast " + value.getClass() + " to " + type);
    }
    
    /**
     * 设置数据
     * 
     * @param key 键
     * @param value 值
     * @return 当前上下文（支持链式调用）
     */
    public EvaluationContext setData(String key, Object value) {
        if (this.data == null) {
            this.data = new HashMap<>();
        }
        this.data.put(key, value);
        return this;
    }
    
    /**
     * 批量设置数据
     * 
     * @param data 数据映射
     * @return 当前上下文（支持链式调用）
     */
    public EvaluationContext setData(Map<String, Object> data) {
        if (this.data == null) {
            this.data = new HashMap<>();
        }
        if (data != null) {
            this.data.putAll(data);
        }
        return this;
    }
    
    /**
     * 设置临时结果
     * 
     * @param key 键
     * @param value 值
     * @return 当前上下文（支持链式调用）
     */
    public EvaluationContext setTempResult(String key, Object value) {
        if (this.tempResults == null) {
            this.tempResults = new HashMap<>();
        }
        this.tempResults.put(key, value);
        return this;
    }
    
    /**
     * 获取临时结果
     * 
     * @param key 键
     * @return 值
     */
    public Object getTempResult(String key) {
        return tempResults != null ? tempResults.get(key) : null;
    }
    
    /**
     * 添加评估结果到历史记录
     * 
     * @param result 评估结果
     * @return 当前上下文（支持链式调用）
     */
    public EvaluationContext addEvaluationResult(EvaluationResult result) {
        if (this.evaluationHistory == null) {
            this.evaluationHistory = new ArrayList<>();
        }
        this.evaluationHistory.add(result);
        return this;
    }
    
    /**
     * 添加匹配的警情编号
     * 
     * @param jjbh 警情编号
     * @return 当前上下文（支持链式调用）
     */
    public EvaluationContext addMatchedJjbh(String jjbh) {
        if (this.matchedJjbhs == null) {
            this.matchedJjbhs = new ArrayList<>();
        }
        if (jjbh != null && !this.matchedJjbhs.contains(jjbh)) {
            this.matchedJjbhs.add(jjbh);
        }
        return this;
    }
    
    /**
     * 批量添加匹配的警情编号
     * 
     * @param jjbhs 警情编号列表
     * @return 当前上下文（支持链式调用）
     */
    public EvaluationContext addMatchedJjbhs(List<String> jjbhs) {
        if (jjbhs != null) {
            for (String jjbh : jjbhs) {
                addMatchedJjbh(jjbh);
            }
        }
        return this;
    }
    
    /**
     * 启用调试模式
     * 
     * @return 当前上下文（支持链式调用）
     */
    public EvaluationContext enableDebug() {
        this.debugMode = true;
        return this;
    }
    
    /**
     * 禁用调试模式
     * 
     * @return 当前上下文（支持链式调用）
     */
    public EvaluationContext disableDebug() {
        this.debugMode = false;
        return this;
    }
    
    /**
     * 设置当前字段
     * 
     * @param field 字段配置
     * @return 当前上下文（支持链式调用）
     */
    public EvaluationContext setCurrentField(Object field) {
        this.currentField = field;
        return this;
    }
    
    /**
     * 获取评估摘要
     * 
     * @return 摘要信息
     */
    public Map<String, Object> getSummary() {
        Map<String, Object> summary = new HashMap<>();
        summary.put("identifier", identifier);
        summary.put("startTime", startTime);
        summary.put("debugMode", debugMode);
        summary.put("dataSize", data != null ? data.size() : 0);
        summary.put("historySize", evaluationHistory != null ? evaluationHistory.size() : 0);
        summary.put("tempResultsSize", tempResults != null ? tempResults.size() : 0);
        summary.put("matchedJjbhsSize", matchedJjbhs != null ? matchedJjbhs.size() : 0);
        return summary;
    }
    
    /**
     * 清理临时数据
     * 
     * @return 当前上下文（支持链式调用）
     */
    public EvaluationContext clearTempResults() {
        if (this.tempResults != null) {
            this.tempResults.clear();
        }
        return this;
    }
}
