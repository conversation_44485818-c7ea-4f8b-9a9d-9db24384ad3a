package com.hl.rule.service.evaluator.field.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.evaluator.context.EvaluationContext;
import com.hl.rule.service.evaluator.field.AbstractFieldEvaluator;
import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 时间字段评估器
 * 
 * 处理时间相关字段(djsj, cjsj)的条件评估
 * 支持时间比较和时间范围匹配
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@Component
public class TimeFieldEvaluator extends AbstractFieldEvaluator {
    
    private static final String[] SUPPORTED_FIELDS = {"djsj", "cjsj"};
    
    @Override
    protected boolean doEvaluate(JSONObject field, EvaluationContext context) {
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            String column = field.getString("column");
            String operator = field.getString("opt");
            String timeValue = field.getString("value");
            
            if (timeValue == null || timeValue.trim().isEmpty()) {
                logWarn(context, "Time value is null or empty for field: {}", column);
                return false;
            }
            
            // 解析期望时间
            Date expectedDate = parseTimeValue(timeValue);
            if (expectedDate == null) {
                logWarn(context, "Cannot parse time value: {}", timeValue);
                return false;
            }
            
            // 模拟时间匹配逻辑
            Set<String> matchedJjbhs = simulateTimeMatching(context, column, operator, expectedDate);
            
            // 更新匹配的警情编号
            updateMatchedJjbhs(context, matchedJjbhs);
            
            // 时间字段的结果基于是否有匹配的记录
            boolean result = !matchedJjbhs.isEmpty();
            
            // 记录详细信息
            recordFieldEvaluation(column, timeValue, expectedDate, matchedJjbhs, result, startTime, context);
            
            return result;
            
        } catch (Exception e) {
            logError(context, "Error evaluating time field", e);
            return false;
        }
    }
    
    @Override
    protected String[] getFieldNames() {
        return SUPPORTED_FIELDS;
    }
    
    @Override
    public String getFieldType() {
        return "TIME_FIELD";
    }
    
    @Override
    protected void addFieldEvaluationDetails(EvaluationResult result, JSONObject field, 
                                           EvaluationContext context, boolean evaluationResult) {
        super.addFieldEvaluationDetails(result, field, context, evaluationResult);
        
        String column = field.getString("column");
        String timeValue = field.getString("value");
        
        // 添加时间字段特有的详情
        result.addDetail("fieldCategory", "时间字段")
              .addDetail("evaluationType", "时间比较")
              .addDetail("businessType", "警情分析")
              .addDetail("timeValue", timeValue);
        
        // 根据字段类型添加具体描述
        if ("djsj".equals(column)) {
            result.addDetail("timeFieldType", "接警时间");
        } else if ("cjsj".equals(column)) {
            result.addDetail("timeFieldType", "处警时间");
        }
        
        // 添加解析后的时间信息
        Date parsedDate = parseTimeValue(timeValue);
        if (parsedDate != null) {
            result.addDetail("parsedTime", parsedDate.toString())
                  .addDetail("timeFormat", "yyyy-MM-dd HH:mm:ss");
        }
        
        // 添加匹配信息
        if (context.getMatchedJjbhs() != null && !context.getMatchedJjbhs().isEmpty()) {
            result.addDetail("matchedJjbhs", new ArrayList<>(context.getMatchedJjbhs()))
                  .addDetail("matchedCount", context.getMatchedJjbhs().size());
        }
    }
    
    /**
     * 解析时间值
     */
    private Date parseTimeValue(String timeValue) {
        try {
            return DateUtil.parse(timeValue);
        } catch (Exception e) {
            log.warn("Error parsing time value: {}", timeValue, e);
            return null;
        }
    }
    
    /**
     * 模拟时间匹配逻辑
     */
    private Set<String> simulateTimeMatching(EvaluationContext context, String column, 
                                           String operator, Date expectedDate) {
        Set<String> matchedJjbhs = new HashSet<>();
        
        // 模拟逻辑：根据时间条件生成匹配的警情编号
        // 这里简化处理，实际应该查询数据库
        
        // 模拟当前时间前后的一些记录
        Date currentTime = new Date();
        long timeDiff = Math.abs(currentTime.getTime() - expectedDate.getTime());
        
        // 根据时间差和操作符决定匹配数量
        int matchCount = 0;
        switch (operator) {
            case "equals":
            case "=":
                // 精确匹配，模拟少量匹配
                matchCount = timeDiff < 3600000 ? 1 : 0; // 1小时内
                break;
            case "greater_than":
            case ">":
                // 大于指定时间，模拟一些匹配
                matchCount = expectedDate.before(currentTime) ? 2 : 0;
                break;
            case "less_than":
            case "<":
                // 小于指定时间，模拟一些匹配
                matchCount = expectedDate.after(currentTime) ? 2 : 0;
                break;
            case "between":
                // 时间范围，模拟更多匹配
                matchCount = 3;
                break;
            default:
                matchCount = 1;
        }
        
        // 生成匹配的警情编号
        for (int i = 0; i < matchCount; i++) {
            matchedJjbhs.add(column.toUpperCase() + "_TIME_" + System.currentTimeMillis() + "_" + i);
        }
        
        logDebug(context, "Simulated {} matched jjbhs for time field: {} {} {}", 
                matchedJjbhs.size(), column, operator, expectedDate);
        
        return matchedJjbhs;
    }
    
    /**
     * 更新匹配的警情编号
     */
    private void updateMatchedJjbhs(EvaluationContext context, Set<String> newJjbhs) {
        // 将匹配的警情编号添加到上下文
        for (String jjbh : newJjbhs) {
            context.addMatchedJjbh(jjbh);
        }
        logDebug(context, "Updated matched jjbhs: {}", newJjbhs);
    }
    
    /**
     * 评估时间条件
     */
    private boolean evaluateTimeCondition(Date actualDate, String operator, Date expectedDate) {
        if (actualDate == null || expectedDate == null) {
            return false;
        }
        
        switch (operator) {
            case "equals":
            case "=":
                // 时间相等（考虑到毫秒差异，使用1秒的容差）
                return Math.abs(actualDate.getTime() - expectedDate.getTime()) < 1000;
                
            case "greater_than":
            case ">":
                return actualDate.after(expectedDate);
                
            case "less_than":
            case "<":
                return actualDate.before(expectedDate);
                
            case "greater_equal":
            case ">=":
                return actualDate.equals(expectedDate) || actualDate.after(expectedDate);
                
            case "less_equal":
            case "<=":
                return actualDate.equals(expectedDate) || actualDate.before(expectedDate);
                
            case "between":
                // 简化处理，这里应该有两个时间值
                return true;
                
            default:
                log.warn("Unsupported time operator: {}", operator);
                return false;
        }
    }
    
    /**
     * 记录字段评估信息
     */
    private void recordFieldEvaluation(String fieldName, String timeValue, Date expectedDate,
                                     Set<String> matchedJjbhs, boolean result, 
                                     LocalDateTime startTime, EvaluationContext context) {
        long duration = java.time.temporal.ChronoUnit.MILLIS.between(startTime, LocalDateTime.now());
        
        // 记录到临时结果
        context.setTempResult("lastFieldEvaluation", fieldName);
        context.setTempResult("lastTimeValue", timeValue);
        context.setTempResult("lastExpectedDate", expectedDate);
        context.setTempResult("lastMatchedJjbhs", matchedJjbhs);
        context.setTempResult("lastFieldResult", result);
        context.setTempResult("lastFieldDuration", duration);
        
        log.info("Time field evaluation completed - Field: {}, TimeValue: {}, MatchedCount: {}, Result: {}, Duration: {}ms",
                fieldName, timeValue, matchedJjbhs.size(), result, duration);
        
        if (log.isDebugEnabled()) {
            log.debug("Time field evaluation details - Field: {}, ExpectedDate: {}, MatchedJjbhs: {}", 
                    fieldName, expectedDate, matchedJjbhs);
        }
    }
}
