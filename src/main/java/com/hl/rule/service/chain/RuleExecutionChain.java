package com.hl.rule.service.chain;

import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.AnalysisContext;
import com.hl.rule.service.evaluator.ConditionEvaluator;
import com.hl.rule.service.evaluator.factory.EvaluatorFactory;
import com.hl.rule.service.evaluator.result.EvaluationResult;
import com.hl.rule.service.recorder.RuleExecutionRecorder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 规则执行链
 * 
 * 使用责任链模式处理复杂的规则执行流程
 * 支持：
 * - AND/OR 逻辑组合
 * - 短路评估优化
 * - 详细的执行记录
 * - 嵌套条件组
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@RequiredArgsConstructor
public class RuleExecutionChain {
    
    private final EvaluatorFactory evaluatorFactory;
    private final RuleExecutionRecorder executionRecorder;
    
    /**
     * 执行规则组
     * 
     * @param context 分析上下文
     * @param rules 规则列表
     * @return 执行结果
     */
    public EvaluationResult executeRuleGroup(AnalysisContext context, List<JSONObject> rules) {
        String identifier = context.getIdentifier();
        LocalDateTime startTime = LocalDateTime.now();
        
        // 开始执行记录
        executionRecorder.startExecution(identifier, "Rule group execution");
        
        try {
            if (rules == null || rules.isEmpty()) {
                EvaluationResult result = EvaluationResult.success(true, "RULE_GROUP", "Empty rule group defaults to true");
                executionRecorder.completeExecution(identifier, true, "Empty rule group");
                return result;
            }
            
            // 获取逻辑操作符
            String operator = extractOperator(rules);
            
            // 记录执行开始
            executionRecorder.recordStep(identifier, "START", 
                    String.format("Starting rule group execution with %d rules, operator: %s", rules.size(), operator), true);
            
            // 执行规则组
            EvaluationResult result = executeRulesWithOperator(context, rules, operator);
            
            // 记录执行完成
            executionRecorder.recordEvaluation(identifier, result);
            executionRecorder.completeExecution(identifier, result.isResult(), 
                    String.format("Rule group executed with result: %s", result.isResult()));
            
            return result;
            
        } catch (Exception e) {
            log.error("Error executing rule group for context: {}", identifier, e);
            executionRecorder.recordError(identifier, "Rule group execution failed: " + e.getMessage(), e);
            
            return EvaluationResult.failure("RULE_GROUP", "Rule group execution", e.getMessage())
                    .setExecutionTime(startTime, LocalDateTime.now());
        }
    }
    
    /**
     * 执行单个规则
     * 
     * @param context 分析上下文
     * @param rule 规则配置
     * @return 执行结果
     */
    public EvaluationResult executeRule(AnalysisContext context, JSONObject rule) {
        String identifier = context.getIdentifier();
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            String type = rule.getString("type");
            
            if ("group".equals(type)) {
                // 处理嵌套规则组
                List<JSONObject> subRules = rule.getList("value", JSONObject.class);
                
                executionRecorder.recordStep(identifier, "NESTED_GROUP", 
                        String.format("Processing nested group with %d rules", subRules != null ? subRules.size() : 0), true);
                
                return executeRuleGroup(context, subRules);
                
            } else {
                // 处理单个条件
                JSONObject field = rule.getJSONObject("field");
                if (field == null) {
                    String error = "Field configuration is null";
                    executionRecorder.recordStep(identifier, "VALIDATION_ERROR", error, false);
                    return EvaluationResult.failure("RULE", "Single rule execution", error);
                }
                
                // 获取评估器
                String ruleType = context.getRules().getRuleType();
                ConditionEvaluator evaluator = evaluatorFactory.getEvaluatorByRuleType(ruleType);
                
                if (evaluator == null) {
                    String error = "No evaluator found for rule type: " + ruleType;
                    executionRecorder.recordStep(identifier, "EVALUATOR_ERROR", error, false);
                    return EvaluationResult.failure("RULE", "Single rule execution", error);
                }
                
                // 记录评估开始
                String column = field.getString("column");
                executionRecorder.recordStep(identifier, "EVALUATE_CONDITION", 
                        String.format("Evaluating condition: %s", column), true);
                
                // 执行评估
                boolean result = evaluator.evaluate(field, context);
                
                // 创建结果
                EvaluationResult evaluationResult = EvaluationResult.success(result, "RULE", 
                        String.format("Rule evaluation: %s", column))
                        .setExecutionTime(startTime, LocalDateTime.now())
                        .addDetail("ruleType", ruleType)
                        .addDetail("column", column)
                        .addDetail("evaluatorType", evaluator.getEvaluatorType());
                
                // 记录评估结果
                executionRecorder.recordEvaluation(identifier, evaluationResult);
                
                return evaluationResult;
            }
            
        } catch (Exception e) {
            log.error("Error executing rule for context: {}", identifier, e);
            executionRecorder.recordError(identifier, "Rule execution failed: " + e.getMessage(), e);
            
            return EvaluationResult.failure("RULE", "Single rule execution", e.getMessage())
                    .setExecutionTime(startTime, LocalDateTime.now());
        }
    }
    
    /**
     * 使用指定操作符执行规则列表
     * 
     * @param context 分析上下文
     * @param rules 规则列表
     * @param operator 逻辑操作符
     * @return 执行结果
     */
    private EvaluationResult executeRulesWithOperator(AnalysisContext context, List<JSONObject> rules, String operator) {
        String identifier = context.getIdentifier();
        LocalDateTime startTime = LocalDateTime.now();
        
        List<EvaluationResult> results = new ArrayList<>();
        
        for (int i = 0; i < rules.size(); i++) {
            JSONObject rule = rules.get(i);
            
            // 记录规则执行开始
            executionRecorder.recordStep(identifier, "RULE_" + (i + 1), 
                    String.format("Executing rule %d of %d", i + 1, rules.size()), true);
            
            // 执行规则
            EvaluationResult ruleResult = executeRule(context, rule);
            results.add(ruleResult);
            
            // 短路评估优化
            if (shouldShortCircuit(operator, ruleResult.isResult(), i, rules.size())) {
                String shortCircuitMsg = String.format("Short-circuit evaluation: %s operator, result: %s at rule %d", 
                        operator, ruleResult.isResult(), i + 1);
                
                executionRecorder.recordStep(identifier, "SHORT_CIRCUIT", shortCircuitMsg, true);
                log.debug("Short-circuit evaluation triggered: {}", shortCircuitMsg);
                break;
            }
        }
        
        // 计算最终结果
        boolean finalResult = calculateFinalResult(results, operator);
        
        // 创建组合结果
        EvaluationResult groupResult = EvaluationResult.success(finalResult, "RULE_GROUP", 
                String.format("Rule group evaluation with %s operator", operator.toUpperCase()))
                .setExecutionTime(startTime, LocalDateTime.now())
                .addDetail("operator", operator)
                .addDetail("ruleCount", rules.size())
                .addDetail("evaluatedCount", results.size())
                .addDetail("individualResults", results);
        
        // 添加详细的结果统计
        addResultStatistics(groupResult, results);
        
        return groupResult;
    }
    
    /**
     * 提取逻辑操作符
     * 
     * @param rules 规则列表
     * @return 逻辑操作符
     */
    private String extractOperator(List<JSONObject> rules) {
        if (rules.size() > 1) {
            String link = rules.get(1).getString("link");
            return link != null ? link : "and";
        }
        return "and";
    }
    
    /**
     * 判断是否应该短路评估
     * 
     * @param operator 操作符
     * @param currentResult 当前结果
     * @param currentIndex 当前索引
     * @param totalRules 总规则数
     * @return 是否短路
     */
    private boolean shouldShortCircuit(String operator, boolean currentResult, int currentIndex, int totalRules) {
        // 如果是最后一个规则，不需要短路
        if (currentIndex >= totalRules - 1) {
            return false;
        }
        
        // AND 操作：遇到 false 就短路
        if ("and".equals(operator) && !currentResult) {
            return true;
        }
        
        // OR 操作：遇到 true 就短路
        if ("or".equals(operator) && currentResult) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 计算最终结果
     * 
     * @param results 结果列表
     * @param operator 操作符
     * @return 最终结果
     */
    private boolean calculateFinalResult(List<EvaluationResult> results, String operator) {
        if (results.isEmpty()) {
            return true;
        }
        
        if ("and".equals(operator)) {
            return results.stream().allMatch(EvaluationResult::isResult);
        } else if ("or".equals(operator)) {
            return results.stream().anyMatch(EvaluationResult::isResult);
        } else {
            log.warn("Unknown operator: {}, defaulting to AND", operator);
            return results.stream().allMatch(EvaluationResult::isResult);
        }
    }
    
    /**
     * 添加结果统计信息
     * 
     * @param groupResult 组结果
     * @param results 个别结果
     */
    private void addResultStatistics(EvaluationResult groupResult, List<EvaluationResult> results) {
        long successCount = results.stream().mapToLong(r -> r.isResult() ? 1 : 0).sum();
        long failureCount = results.size() - successCount;
        long totalDuration = results.stream().mapToLong(EvaluationResult::getDuration).sum();
        
        groupResult.addDetail("successCount", successCount)
                  .addDetail("failureCount", failureCount)
                  .addDetail("successRate", results.size() > 0 ? (double) successCount / results.size() : 0.0)
                  .addDetail("totalDuration", totalDuration)
                  .addDetail("averageDuration", results.size() > 0 ? (double) totalDuration / results.size() : 0.0);
    }
}
