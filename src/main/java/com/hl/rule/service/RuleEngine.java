package com.hl.rule.service;

import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.chain.RuleExecutionChain;
import com.hl.rule.service.evaluator.ConditionEvaluator;
import com.hl.rule.service.evaluator.factory.EvaluatorFactory;
import com.hl.rule.service.evaluator.result.EvaluationResult;
import com.hl.rule.service.recorder.RuleExecutionRecorder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 规则引擎
 *
 * 重构后的规则引擎，整合了多种设计模式：
 * - 工厂模式：管理评估器
 * - 责任链模式：处理规则执行流程
 * - 观察者模式：记录执行过程
 * - 策略模式：不同类型的条件评估
 *
 * 提供详细的执行记录和错误处理
 *
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RuleEngine {

    private final EvaluatorFactory evaluatorFactory;
    private final RuleExecutionRecorder executionRecorder;

    private RuleExecutionChain executionChain;

    @PostConstruct
    public void init() {
        // 初始化执行链
        this.executionChain = new RuleExecutionChain(evaluatorFactory, executionRecorder);

        log.info("RuleEngine initialized with {} evaluators", evaluatorFactory.getEvaluatorCount());
        log.info("Supported evaluator types: {}", String.join(", ", evaluatorFactory.getSupportedTypes()));
    }

    /**
     * 评估规则（兼容旧版本接口）
     *
     * @param context 分析上下文
     * @return 评估结果
     */
    public boolean evaluate(AnalysisContext context) {
        EvaluationResult result = evaluateWithDetails(context);
        return result.isResult();
    }

    /**
     * 评估规则并返回详细结果（新版本接口）
     *
     * @param context 分析上下文
     * @return 详细的评估结果
     */
    public EvaluationResult evaluateWithDetails(AnalysisContext context) {
        LocalDateTime startTime = LocalDateTime.now();
        String identifier = context != null ? context.getIdentifier() : "unknown";

        // 开始执行记录
        executionRecorder.startExecution(identifier, "Rule engine evaluation");

        try {
            // 验证输入参数
            if (context == null || context.getRules() == null) {
                String error = "Invalid context or rules";
                log.warn("Rule evaluation failed: {}", error);
                executionRecorder.recordError(identifier, error, null);
                return EvaluationResult.failure("RULE_ENGINE", "Rule evaluation", error);
            }

            String ruleType = context.getRules().getRuleType();

            // 记录评估开始
            executionRecorder.recordStep(identifier, "VALIDATION",
                    String.format("Starting rule evaluation for type: %s", ruleType), true);

            // 获取评估器
            ConditionEvaluator evaluator = evaluatorFactory.getEvaluatorByRuleType(ruleType);
            if (evaluator == null) {
                String error = "Unsupported rule type: " + ruleType;
                log.error("Rule evaluation failed: {}", error);
                executionRecorder.recordError(identifier, error, null);
                return EvaluationResult.failure("RULE_ENGINE", "Rule evaluation", error);
            }

            // 提取根规则
            JSONObject rootRule = context.getRules().getRules().getJSONObject(0);
            List<JSONObject> rules = rootRule.getList("value", JSONObject.class);

            // 记录规则信息
            executionRecorder.recordStep(identifier, "RULE_EXTRACTION",
                    String.format("Extracted %d rules from root rule", rules != null ? rules.size() : 0), true);

            // 使用执行链处理规则
            EvaluationResult result = executionChain.executeRuleGroup(context, rules);

            // 添加引擎级别的详情
            result.addDetail("ruleType", ruleType)
                  .addDetail("evaluatorType", evaluator.getEvaluatorType())
                  .addDetail("engineVersion", "2.0")
                  .addDetail("identifier", identifier);

            // 完成执行记录
            executionRecorder.completeExecution(identifier, result.isResult(),
                    String.format("Rule evaluation completed with result: %s", result.isResult()));

            log.info("Rule evaluation completed - Target: {}, Type: {}, Result: {}, Duration: {}ms",
                    identifier, ruleType, result.isResult(), result.getDuration());

            return result;

        } catch (Exception e) {
            log.error("Error evaluating rules for context: {}", identifier, e);
            executionRecorder.recordError(identifier, "Rule evaluation failed: " + e.getMessage(), e);

            return EvaluationResult.failure("RULE_ENGINE", "Rule evaluation", e.getMessage())
                    .setExecutionTime(startTime, LocalDateTime.now())
                    .addDetail("identifier", identifier);
        }
    }

    /**
     * 获取执行统计信息
     *
     * @return 统计信息
     */
    public RuleEngineStatistics getStatistics() {
        return new RuleEngineStatistics(
                evaluatorFactory.getEvaluatorCount(),
                evaluatorFactory.getSupportedTypes(),
                executionRecorder.getExecutionStatistics()
        );
    }

    /**
     * 获取支持的规则类型
     *
     * @return 支持的规则类型数组
     */
    public String[] getSupportedRuleTypes() {
        return evaluatorFactory.getSupportedTypes();
    }

    /**
     * 检查是否支持指定的规则类型
     *
     * @param ruleType 规则类型
     * @return 是否支持
     */
    public boolean supportsRuleType(String ruleType) {
        return evaluatorFactory.supports(ruleType);
    }

    /**
     * 获取执行记录
     *
     * @param identifier 执行标识符
     * @return 执行记录
     */
    public String getExecutionRecord(String identifier) {
        var record = executionRecorder.getExecutionRecord(identifier);
        return record != null ? record.getDetailedReport() : null;
    }

    /**
     * 清理执行记录
     *
     * @param identifier 执行标识符
     */
    public void clearExecutionRecord(String identifier) {
        executionRecorder.clearExecutionRecord(identifier);
    }

    /**
     * 规则引擎统计信息
     */
    public static class RuleEngineStatistics {
        private final int evaluatorCount;
        private final String[] supportedTypes;
        private final java.util.Map<String, Object> executionStats;

        public RuleEngineStatistics(int evaluatorCount, String[] supportedTypes,
                                  java.util.Map<String, Object> executionStats) {
            this.evaluatorCount = evaluatorCount;
            this.supportedTypes = supportedTypes;
            this.executionStats = executionStats;
        }

        public int getEvaluatorCount() { return evaluatorCount; }
        public String[] getSupportedTypes() { return supportedTypes; }
        public java.util.Map<String, Object> getExecutionStats() { return executionStats; }

        @Override
        public String toString() {
            return String.format("RuleEngineStatistics{evaluators=%d, types=%s, executions=%s}",
                    evaluatorCount, java.util.Arrays.toString(supportedTypes), executionStats);
        }
    }
}