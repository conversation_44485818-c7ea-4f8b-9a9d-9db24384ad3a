package com.hl.rule.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 规则预警结果
 */
@ApiModel(description = "规则预警结果")
@Data
@TableName(value = "wj_jq_analysis_result")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WjJqAnalysisResult {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 规则id
     */
    @TableField(value = "rule_id")
    @ApiModelProperty(value = "规则id")
    private String ruleId;


    @TableField(value = "result")
    @ApiModelProperty(value = "预警结果")
    private String result;

    @TableField(value = "link_data")
    @ApiModelProperty(value = "关联数据")
    private String linkData;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @TableField(value = "warning_unit")
    private String warningUnit;


    @TableField(exist = false)
    @ApiModelProperty(value = "预警单位名称")
    //@Translation(type = TransConstant.ORG_ID_TO_NAME, mapper = "warningUnit")
    private String warningUnitName;

    @TableField(value = "latest_warn_data")
    private String latestWarnData;

    @TableField(value = "notify_status")
    @ApiModelProperty(value = "短信通知状态 0 待通知 1 无需通知 2已通知  3 通知失败")
    private Integer notifyStatus;

    @TableField(value = "latest_warn_time")
    private String latestWarnTime;

    @TableField(value = "task_create_status")
    private Integer taskCreateStatus;

    @TableField(value = "task_id")
    private Integer taskId;


    @TableField(value = "match_count")
    private Integer matchCount;

    @TableField(value = "total_count")
    private Integer totalCount;


    @TableField(value = "link_data_type")
    private String linkDataType;
}