package com.hl.rule.domain;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "wj_jq_analysis_rule_template")
@Data
@TableName(value = "wj_jq_analysis_rule_template", autoResultMap = true)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WjJqAnalysisRuleTemplate {
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "")
    private String id;

    /**
     * 模板名称
     */
    @TableField(value = "template_name")
    @ApiModelProperty(value = "模板名称")
    private String templateName;

    /**
     * 配置
     */
    @TableField(value = "config", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "配置")
    private JSONObject config;

    /**
     * 响应头
     */
    @TableField(value = "`header`", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "响应头")
    private JSONArray header;


    @TableField(value = "class_name")
    private String className;
}