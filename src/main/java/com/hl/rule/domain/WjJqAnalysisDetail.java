package com.hl.rule.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 警情预警人员详情
 */
@ApiModel(description="警情预警人员详情")
@Data
@TableName(value = "wj_jq_analysis_detail")
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WjJqAnalysisDetail {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value="id")
    private String id;

    /**
     * 结果id
     */
    @TableField(value = "result_id")
    @ApiModelProperty(value="结果id")
    private String resultId;

    /**
     * 接警编号
     */
    @TableField(value = "jjbh")
    @ApiModelProperty(value="接警编号")
    private String jjbh;
}