package com.hl.rule.domain;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "wj_jq_analysis_rules")
@Data
@TableName(value = "wj_jq_analysis_rules", autoResultMap = true)
@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WjJqAnalysisRules {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 规则名称
     */
    @TableField(value = "rule_name")
    @ApiModelProperty(value = "规则名称")

    private String ruleName;

    /**
     * 规则开始时间
     */
    @TableField(value = "start_time")
    @ApiModelProperty(value = "规则开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 规则结束时间
     */
    @TableField(value = "end_time")
    @ApiModelProperty(value = "规则结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 配置
     */
    @TableField(value = "rules", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "配置")
    private JSONArray rules;

    /**
     * 创建用户
     */
    @TableField(value = "create_user")
    @ApiModelProperty(value = "创建用户")
    private String createUser;

    @TableField(exist = false)
    //@Translation(type = TransConstant.ID_CARD_TO_NAME, mapper = "createUser")
    private String createUserName;

    /**
     * 更新用户
     */
    @TableField(value = "update_user")
    @ApiModelProperty(value = "更新用户")
    private String updateUser;

    //@Translation(type = TransConstant.ID_CARD_TO_NAME, mapper = "updateUser")
    @TableField(exist = false)
    private String updateUserName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 状态 -1 删除  0 正常  1 停用
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "状态 -1 删除  0 正常  1 停用")
    private Integer status;


    @TableField(value = "rule_type")
    private String ruleType;

    /**
     * 预警单位类型：1-动态单位，2-固定单位
     */
    @TableField(value = "warning_unit_type")
    private String warningUnitType;
    

    @TableField(value = "warning_unit")
    private String warningUnit;

    @TableField(value = "rule_execute_type")
    @ApiModelProperty(value = "规则执行类型：1-预警任务，2-分析任务")
    private Integer ruleExecuteType;

    @TableField(value = "rule_execute_status")
    @ApiModelProperty(value = "规则执行状态：0-待执行，1-执行中，2-执行完成，3-执行失败")
    private Integer ruleExecuteStatus;

    @TableField(value = "message_config", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "消息配置")
    private JSONObject messageConfig;

    @TableField(value = "task_config", typeHandler = Fastjson2TypeHandler.class)
    private JSONObject taskConfig;

    @TableField(value = "message_notification_enabled")
    private Integer messageNotificationEnabled;

    @TableField(value = "task_distribution_enabled")
    private Integer taskDistributionEnabled;

}