package com.hl.rule.config;

import com.hl.rule.service.recorder.DefaultRuleExecutionListener;
import com.hl.rule.service.recorder.RuleExecutionRecorder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 规则引擎配置类
 * 
 * 负责初始化规则引擎的各种组件和配置
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class RuleEngineConfiguration {
    
    private final RuleExecutionRecorder ruleExecutionRecorder;
    private final DefaultRuleExecutionListener defaultRuleExecutionListener;
    
    @PostConstruct
    public void init() {
        // 注册默认监听器
        ruleExecutionRecorder.addListener(defaultRuleExecutionListener);
        
        log.info("Rule engine configuration initialized");
        log.info("Default execution listener registered");
    }
}
