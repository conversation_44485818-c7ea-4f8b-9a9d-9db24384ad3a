package com.hl.rule.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.domain.WjJqAnalysisRules;
import com.hl.rule.service.AnalysisContext;
import com.hl.rule.service.RuleEngine;
import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 规则引擎测试控制器
 * 
 * 提供测试接口来验证重构后的规则引擎功能
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@RestController
@RequestMapping("/api/rule-engine/test")
@RequiredArgsConstructor
public class RuleEngineTestController {
    
    private final RuleEngine ruleEngine;
    
    /**
     * 测试规则评估（兼容旧版本）
     */
    @PostMapping("/evaluate/legacy")
    public Map<String, Object> testLegacyEvaluation(@RequestBody Map<String, Object> request) {
        try {
            // 构建测试上下文
            AnalysisContext context = buildTestContext(request);
            
            // 执行评估
            boolean result = ruleEngine.evaluate(context);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("result", result);
            response.put("message", "Legacy evaluation completed");
            response.put("identifier", context.getIdentifier());
            
            return response;
            
        } catch (Exception e) {
            log.error("Error in legacy evaluation test", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return response;
        }
    }
    
    /**
     * 测试规则评估（新版本，带详细结果）
     */
    @PostMapping("/evaluate/detailed")
    public Map<String, Object> testDetailedEvaluation(@RequestBody Map<String, Object> request) {
        try {
            // 构建测试上下文
            AnalysisContext context = buildTestContext(request);
            
            // 执行详细评估
            EvaluationResult result = ruleEngine.evaluateWithDetails(context);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("result", result.isResult());
            response.put("details", result.getDetailedReport());
            response.put("duration", result.getDuration());
            response.put("formattedDuration", result.getFormattedDuration());
            response.put("evaluatorType", result.getEvaluatorType());
            response.put("description", result.getDescription());
            response.put("identifier", context.getIdentifier());
            
            return response;
            
        } catch (Exception e) {
            log.error("Error in detailed evaluation test", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return response;
        }
    }
    
    /**
     * 获取规则引擎统计信息
     */
    @GetMapping("/statistics")
    public Map<String, Object> getStatistics() {
        try {
            RuleEngine.RuleEngineStatistics stats = ruleEngine.getStatistics();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("evaluatorCount", stats.getEvaluatorCount());
            response.put("supportedTypes", stats.getSupportedTypes());
            response.put("executionStats", stats.getExecutionStats());
            
            return response;
            
        } catch (Exception e) {
            log.error("Error getting statistics", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return response;
        }
    }
    
    /**
     * 获取执行记录
     */
    @GetMapping("/execution-record/{identifier}")
    public Map<String, Object> getExecutionRecord(@PathVariable String identifier) {
        try {
            String record = ruleEngine.getExecutionRecord(identifier);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("identifier", identifier);
            response.put("record", record);
            response.put("hasRecord", record != null);
            
            return response;
            
        } catch (Exception e) {
            log.error("Error getting execution record", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return response;
        }
    }
    
    /**
     * 清理执行记录
     */
    @DeleteMapping("/execution-record/{identifier}")
    public Map<String, Object> clearExecutionRecord(@PathVariable String identifier) {
        try {
            ruleEngine.clearExecutionRecord(identifier);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Execution record cleared");
            response.put("identifier", identifier);
            
            return response;
            
        } catch (Exception e) {
            log.error("Error clearing execution record", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return response;
        }
    }
    
    /**
     * 检查规则类型支持
     */
    @GetMapping("/supports/{ruleType}")
    public Map<String, Object> checkRuleTypeSupport(@PathVariable String ruleType) {
        try {
            boolean supported = ruleEngine.supportsRuleType(ruleType);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("ruleType", ruleType);
            response.put("supported", supported);
            response.put("allSupportedTypes", ruleEngine.getSupportedRuleTypes());
            
            return response;
            
        } catch (Exception e) {
            log.error("Error checking rule type support", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return response;
        }
    }
    
    /**
     * 构建测试上下文
     */
    private AnalysisContext buildTestContext(Map<String, Object> request) {
        String identifier = (String) request.getOrDefault("identifier", "test_" + System.currentTimeMillis());
        String ruleType = (String) request.getOrDefault("ruleType", "person");
        
        // 构建测试规则
        WjJqAnalysisRules rules = new WjJqAnalysisRules();
        rules.setRuleType(ruleType);
        
        // 构建简单的测试规则JSON
        JSONObject testRule = new JSONObject();
        testRule.put("type", "condition");
        
        JSONObject field = new JSONObject();
        field.put("column", request.getOrDefault("column", "bjlx"));
        field.put("opt", request.getOrDefault("operator", "equals"));
        field.put("value", request.getOrDefault("value", "test"));
        testRule.put("field", field);
        
        JSONObject rootRule = new JSONObject();
        rootRule.put("value", new Object[]{testRule});
        
        JSONArray rulesArray = new JSONArray();
        rulesArray.add(rootRule);
        rules.setRules(rulesArray);
        
        // 构建分析上下文
        AnalysisContext context = new AnalysisContext();
        context.setIdentifier(identifier);
        context.setRules(rules);
        
        return context;
    }
}
