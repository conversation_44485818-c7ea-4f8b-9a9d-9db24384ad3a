package com.hl.rule.controller;

import com.alibaba.fastjson2.JSONObject;
import com.hl.rule.service.evaluator.FieldEvaluator;
import com.hl.rule.service.evaluator.context.EvaluationContext;
import com.hl.rule.service.evaluator.field.FieldEvaluatorFactory;
import com.hl.rule.service.evaluator.result.EvaluationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 字段评估器测试控制器
 * 
 * 提供测试接口来验证重构后的字段评估器功能
 * 
 * <AUTHOR> Team
 * @version 2.0
 * @since JDK8
 */
@Slf4j
@RestController
@RequestMapping("/api/field-evaluator/test")
@RequiredArgsConstructor
public class FieldEvaluatorTestController {
    
    private final FieldEvaluatorFactory fieldEvaluatorFactory;
    
    /**
     * 获取字段评估器工厂统计信息
     */
    @GetMapping("/factory/summary")
    public Map<String, Object> getFactorySummary() {
        try {
            Map<String, Object> summary = fieldEvaluatorFactory.getSummary();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.putAll(summary);
            
            return response;
            
        } catch (Exception e) {
            log.error("Error getting factory summary", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return response;
        }
    }
    
    /**
     * 测试字段评估器
     */
    @PostMapping("/evaluate")
    public Map<String, Object> testFieldEvaluator(@RequestBody Map<String, Object> request) {
        try {
            String fieldName = (String) request.get("fieldName");
            String operator = (String) request.getOrDefault("operator", "contains");
            Object value = request.get("value");
            String identifier = (String) request.getOrDefault("identifier", "test_" + System.currentTimeMillis());
            
            if (fieldName == null || fieldName.trim().isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("error", "Field name is required");
                return response;
            }
            
            // 获取字段评估器
            FieldEvaluator fieldEvaluator = fieldEvaluatorFactory.getEvaluatorByFieldName(fieldName);
            if (fieldEvaluator == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("error", "No field evaluator found for field: " + fieldName);
                response.put("supportedFields", fieldEvaluatorFactory.getSupportedFieldNames());
                return response;
            }
            
            // 构建字段配置
            JSONObject field = new JSONObject();
            field.put("column", fieldName);
            field.put("opt", operator);
            field.put("value", value);
            field.put("type", request.getOrDefault("type", "string"));
            
            // 添加可选的第二级条件
            if (request.containsKey("secondOperator") && request.containsKey("secondValue")) {
                field.put("second_opt", request.get("secondOperator"));
                field.put("second_value", request.get("secondValue"));
            }
            
            // 创建评估上下文
            EvaluationContext context = EvaluationContext.create(identifier);
            context.enableDebug();
            
            // 添加测试数据
            Map<String, Object> testData = new HashMap<>();
            testData.put("testMode", true);
            testData.put("requestData", request);
            context.setData(testData);
            
            // 执行评估
            EvaluationResult result = fieldEvaluator.evaluateWithDetails(field, context);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("fieldName", fieldName);
            response.put("fieldEvaluatorType", fieldEvaluator.getFieldType());
            response.put("result", result.isResult());
            response.put("details", result.getDetailedReport());
            response.put("duration", result.getDuration());
            response.put("formattedDuration", result.getFormattedDuration());
            response.put("contextSummary", context.getSummary());
            
            return response;
            
        } catch (Exception e) {
            log.error("Error testing field evaluator", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return response;
        }
    }
    
    /**
     * 检查字段支持情况
     */
    @GetMapping("/supports/{fieldName}")
    public Map<String, Object> checkFieldSupport(@PathVariable String fieldName) {
        try {
            boolean supported = fieldEvaluatorFactory.supportsFieldName(fieldName);
            FieldEvaluator evaluator = fieldEvaluatorFactory.getEvaluatorByFieldName(fieldName);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("fieldName", fieldName);
            response.put("supported", supported);
            
            if (evaluator != null) {
                response.put("evaluatorType", evaluator.getFieldType());
                response.put("evaluatorClass", evaluator.getClass().getSimpleName());
                response.put("supportedFields", evaluator.getSupportedFields());
            }
            
            response.put("allSupportedFields", fieldEvaluatorFactory.getSupportedFieldNames());
            
            return response;
            
        } catch (Exception e) {
            log.error("Error checking field support", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return response;
        }
    }
    
    /**
     * 获取字段评估器详细信息
     */
    @GetMapping("/evaluator/{fieldType}")
    public Map<String, Object> getEvaluatorDetails(@PathVariable String fieldType) {
        try {
            Map<String, Object> details = fieldEvaluatorFactory.getEvaluatorDetails(fieldType);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("fieldType", fieldType);
            
            if (details != null) {
                response.putAll(details);
            } else {
                response.put("found", false);
                response.put("message", "No evaluator found for type: " + fieldType);
            }
            
            response.put("allSupportedTypes", fieldEvaluatorFactory.getSupportedFieldTypes());
            
            return response;
            
        } catch (Exception e) {
            log.error("Error getting evaluator details", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return response;
        }
    }
    
    /**
     * 批量测试多个字段
     */
    @PostMapping("/batch-test")
    public Map<String, Object> batchTestFields(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            java.util.List<Map<String, Object>> fieldTests = (java.util.List<Map<String, Object>>) request.get("fields");
            
            if (fieldTests == null || fieldTests.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("error", "Fields array is required");
                return response;
            }
            
            java.util.List<Map<String, Object>> results = new java.util.ArrayList<>();
            
            for (Map<String, Object> fieldTest : fieldTests) {
                try {
                    Map<String, Object> testResult = testFieldEvaluator(fieldTest);
                    results.add(testResult);
                } catch (Exception e) {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("fieldName", fieldTest.get("fieldName"));
                    errorResult.put("error", e.getMessage());
                    results.add(errorResult);
                }
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("totalTests", fieldTests.size());
            response.put("results", results);
            
            long successCount = results.stream().mapToLong(r -> Boolean.TRUE.equals(r.get("success")) ? 1 : 0).sum();
            response.put("successCount", successCount);
            response.put("failureCount", fieldTests.size() - successCount);
            
            return response;
            
        } catch (Exception e) {
            log.error("Error in batch field testing", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return response;
        }
    }
    
    /**
     * 获取字段评估器使用示例
     */
    @GetMapping("/examples")
    public Map<String, Object> getUsageExamples() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        
        Map<String, Object> examples = new HashMap<>();
        
        // 报警类型示例
        Map<String, Object> bjlxExample = new HashMap<>();
        bjlxExample.put("fieldName", "bjlx");
        bjlxExample.put("operator", "equals");
        bjlxExample.put("value", 3);
        bjlxExample.put("type", "count");
        bjlxExample.put("description", "报警类型数量等于3");
        examples.put("bjlx", bjlxExample);
        
        // 接警单位示例
        Map<String, Object> jjdwExample = new HashMap<>();
        jjdwExample.put("fieldName", "jjdw");
        jjdwExample.put("operator", "greater_than");
        jjdwExample.put("value", 1);
        jjdwExample.put("description", "接警单位数量大于1");
        examples.put("jjdw", jjdwExample);
        
        // 时间字段示例
        Map<String, Object> djsjExample = new HashMap<>();
        djsjExample.put("fieldName", "djsj");
        djsjExample.put("operator", "greater_than");
        djsjExample.put("value", "2024-01-01 00:00:00");
        djsjExample.put("description", "接警时间大于指定时间");
        examples.put("djsj", djsjExample);
        
        // 内容字段示例
        Map<String, Object> bjnrExample = new HashMap<>();
        bjnrExample.put("fieldName", "bjnr");
        bjnrExample.put("operator", "contains");
        bjnrExample.put("value", "盗窃,抢劫");
        bjnrExample.put("secondOperator", "greater_equal");
        bjnrExample.put("secondValue", 2);
        bjnrExample.put("description", "报警内容包含关键词且匹配数量大于等于2");
        examples.put("bjnr", bjnrExample);
        
        response.put("examples", examples);
        response.put("supportedFields", fieldEvaluatorFactory.getSupportedFieldNames());
        response.put("supportedTypes", fieldEvaluatorFactory.getSupportedFieldTypes());
        
        return response;
    }
}
