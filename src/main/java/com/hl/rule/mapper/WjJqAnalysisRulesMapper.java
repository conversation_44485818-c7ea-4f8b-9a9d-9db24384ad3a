package com.hl.rule.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.hl.rule.domain.WjJqAnalysisRules;
import org.apache.ibatis.annotations.Param;

public interface WjJqAnalysisRulesMapper extends BaseMapper<WjJqAnalysisRules> {
//    Page<WjJqAnalysisRules> page(@Param("page") Page<Object> objectPage, @Param("param") WjJqAnalysisRulesReqVO analysisRulesReqVO);
}