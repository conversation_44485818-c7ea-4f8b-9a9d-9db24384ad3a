package com.hl.rule.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.hl.rule.domain.WjJqAnalysisDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

@Mapper
public interface WjJqAnalysisDetailMapper extends BaseMapper<WjJqAnalysisDetail> {
    
//    @Select("<script>" +
//            "SELECT * FROM wj_jq_analysis_detail " +
//            "WHERE result_id IN " +
//            "<foreach collection='resultIds' item='id' open='(' separator=',' close=')'>" +
//            "#{id}" +
//            "</foreach>" +
//            "</script>")
//    List<WjJqAnalysisDetail> selectDetailsByResultIds(@Param("resultIds") Collection<String> resultIds);

}