package com.hl.rule.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.hl.rule.domain.WjJqAnalysisResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WjJqAnalysisResultMapper extends BaseMapper<WjJqAnalysisResult> {
//    Page<WjJqAnalysisResultResVO> pageResult(@Param("page") Page<Object> objectPage, @Param("param") WjJqAnalysisResultReqVO analysisResultReqVO);
//
//    Page<WjJqAnalysisResultResVO> pageResultByOrder(@Param("page") Page<Object> objectPage, @Param("param") WjJqAnalysisResultReqVO analysisResultReqVO);
//
//    Page<WjJqAnalysisResultResVO> pageResultByCountOrder(@Param("page") Page<Object> objectPage, @Param("param") WjJqAnalysisResultReqVO analysisResultReqVO);
//
//    List<WjJqAnalysisResultResVO> queryCountDetailByIds(@Param("ids") List<String> ids);
}