package com.hl.analysis.handler;

import com.hl.analysis.domain.ExecutionRecord;
import com.hl.analysis.domain.RuleContext;
import com.hl.analysis.domain.RuleNode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultRuleUnitHandler implements RuleUnitHandler {

    @Override
    public boolean process(RuleNode.FieldInfo fieldInfo, String link, RuleContext context) {
        String tableFieldId = fieldInfo.getTable_field();
        log.warn("未找到 table_field '{}' 的处理器，此规则单元返回 false。", tableFieldId);

        ExecutionRecord record = ExecutionRecord.builder()
                .ruleId(fieldInfo.getTable_field()) // 使用table_field作为ID
                .fieldName("N/A")
                .description("未找到对应的规则单元处理器实现: " + tableFieldId)
                .expected("N/A")
                .actual("N/A")
                .result(false)
                .build();
        context.addRecord(record);
        return false;
    }

    @Override
    public String getHandledTableField() {
        return "default";
    }
}