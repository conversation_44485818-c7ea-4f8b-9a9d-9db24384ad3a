package com.hl.analysis.handler;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 规则单元处理器注册中心。
 * Spring启动时，会自动收集所有 RuleUnitHandler 类型的Bean，
 * 并以它们各自声明的 table_field ID 为键存入Map。
 */
@Component
public class RuleUnitHandlerRegistry {

    private final List<RuleUnitHandler> handlers;
    private final Map<String, RuleUnitHandler> handlerMap = new HashMap<>();
    private final RuleUnitHandler defaultHandler = new DefaultRuleUnitHandler();

    public RuleUnitHandlerRegistry(List<RuleUnitHandler> handlers) {
        this.handlers = handlers;
    }

    @PostConstruct
    public void init() {
        // 使用流将 List 转换为 Map，key 是 handler 声明的 table_field ID
        handlerMap.putAll(handlers.stream()
                .collect(Collectors.toMap(RuleUnitHandler::getHandledTableField, Function.identity())));
    }

    /**
     * 根据 table_field ID 获取对应的处理器。
     * @param tableFieldId 规则单元中定义的 table_field
     * @return 对应的处理器，如果找不到则返回默认处理器
     */
    public RuleUnitHandler getHandler(String tableFieldId) {
        return handlerMap.getOrDefault(tableFieldId, defaultHandler);
    }
}