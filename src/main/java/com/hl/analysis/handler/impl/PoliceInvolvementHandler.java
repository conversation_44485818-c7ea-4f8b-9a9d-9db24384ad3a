package com.hl.analysis.handler.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.DbUtil;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.hl.analysis.domain.ExecutionRecord;
import com.hl.analysis.domain.RuleContext;
import com.hl.analysis.domain.RuleNode;
import com.hl.analysis.handler.RuleUnitHandler;
import com.hl.analysis.util.RuleUtils;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.jq.domain.WjscJqSjxx;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class PoliceInvolvementHandler implements RuleUnitHandler {

    private static final String TABLE_FIELD_ID = "7cbb7be08204436eb284481ea0c7009f";


    @Override
    public String getHandledTableField() {
        return TABLE_FIELD_ID;
    }

    @Override
    public boolean process(RuleNode.FieldInfo fieldInfo, String link, RuleContext context) {
        List<RuleNode.FieldCondition> conditions = fieldInfo.getFields();
        String identifier = context.getInitialIdentifier();

        // 为方便查找，将 List<FieldCondition> 转为 Map<String, FieldCondition>
        Map<String, RuleNode.FieldCondition> params = conditions.stream()
                .collect(Collectors.toMap(RuleNode.FieldCondition::getColumn, Function.identity()));

        String days = "";
        if (params.get("recent_day") != null) {
            RuleNode.FieldCondition recentDay = params.get("recent_day");
            DateTime dateTime = DateUtil.offsetDay(DateUtil.date(), -(int) recentDay.getValue());
            days = DateUtil.format(dateTime, "yyyyMMddHHmmss");
        }


        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE1));
        List<WjscJqSjxx> list = Db.lambdaQuery(WjscJqSjxx.class)
                .eq(WjscJqSjxx::getGmsfhm, identifier)
                .ge(StrUtil.isNotBlank(days), WjscJqSjxx::getDjsjTime, days)
                .list();
        DynamicDataSourceContextHolder.clearDataSourceType();

        RuleNode.FieldCondition sjCount = params.get("sj_count");
        if (sjCount == null || sjCount.getValue() == null) {
            return false;
        }

        int count = Integer.parseInt(sjCount.getValue().toString());

        boolean b = RuleUtils.evaluateNumberCondition(sjCount.getCondition_value(), list.size(), count);
        if (b){
            Map<String,Object>  extend = new HashMap<>();
            extend.put("jq",list.stream().map(WjscJqSjxx::getJjbh).collect(Collectors.toSet()));
            context.addExtend(extend);
        }
        recordDetail(context, sjCount, String.valueOf(list.size()), b);
        return b;
    }

    /**
     * 辅助方法，用于记录执行详情
     */
    private void recordDetail(RuleContext context, RuleNode.FieldCondition condition, String actualValue, boolean result) {
        String description = String.format("在规则单元[%s]中, 判断字段'%s' %s '%s'",
                "涉警情况", condition.getColumn(), condition.getCondition(), condition.getValue());

        ExecutionRecord record = ExecutionRecord.builder()
                .ruleId(condition.getId())
                .fieldName(condition.getColumn())
                .description(description)
                .expected(String.valueOf(condition.getValue()))
                .actual(actualValue)
                .result(result)
                .build();
        context.addRecord(record);
    }
}