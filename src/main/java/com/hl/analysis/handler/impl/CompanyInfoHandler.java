package com.hl.analysis.handler.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.hl.analysis.domain.ExecutionRecord;
import com.hl.analysis.domain.RuleContext;
import com.hl.analysis.domain.RuleNode;
import com.hl.analysis.handler.RuleUnitHandler;
import com.hl.building.domain.WjPersonInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class CompanyInfoHandler implements RuleUnitHandler {

    private static final String TABLE_FIELD_ID = "81e1cc50d459409091e8cf004a3ef4e6";

    @Override
    public boolean process(RuleNode.FieldInfo fieldInfo, String link, RuleContext context) {
        List<RuleNode.FieldCondition> conditions = fieldInfo.getFields();
        // 身份证号码
        String initialIdentifier = context.getInitialIdentifier();

        Map<String, RuleNode.FieldCondition> params = conditions.stream()
                .collect(Collectors.toMap(RuleNode.FieldCondition::getColumn, Function.identity()));

        boolean isFr = true;
        if (params.containsKey("is_fr")) {
            RuleNode.FieldCondition frCondition = params.get("is_fr");
            String conditionValue = frCondition.getCondition_value();
            boolean value;
            if ("true".equals(conditionValue)) {
                value = true;
            }else {
                value = false;
            }
            WjPersonInfo one = Db.lambdaQuery(WjPersonInfo.class)
                    .eq(WjPersonInfo::getIdCard, initialIdentifier)
                    .last("limit 1")
                    .one();
            if (one != null && StrUtil.isNotBlank(one.getZwMc()) && one.getZwMc().contains("法人")) {
                if (!value) {
                    isFr = false;
                }
            } else {
                isFr = false;
            }
        }

        return isFr;
    }

    @Override
    public String getHandledTableField() {
        // 企业信息
        return TABLE_FIELD_ID;
    }
}
