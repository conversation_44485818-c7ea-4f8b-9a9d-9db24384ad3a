package com.hl.analysis.handler.impl;

import com.hl.analysis.domain.RuleContext;
import com.hl.analysis.domain.RuleNode;
import com.hl.analysis.handler.RuleUnitHandler;

public class CaseInfoHandler implements RuleUnitHandler {

    private static final String TABLE_FIELD_ID = "96533498bea6421fbdbdc38c8a08e876";


    @Override
    public boolean process(RuleNode.FieldInfo fieldInfo, String link, RuleContext context) {
        return false;

    }

    @Override
    public String getHandledTableField() {
        return TABLE_FIELD_ID;
    }
}
