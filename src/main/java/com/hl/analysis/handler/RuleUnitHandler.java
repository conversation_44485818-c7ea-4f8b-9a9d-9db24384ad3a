package com.hl.analysis.handler;


import com.hl.analysis.domain.RuleContext;
import com.hl.analysis.domain.RuleNode;

/**
 * 规则单元处理器接口（新的策略模式接口）。
 * 每个实现类负责处理一个特定的 "table_field" 对应的业务逻辑。
 */
public interface RuleUnitHandler {

    /**
     * 处理一个完整的规则单元（由 table_field 标识）。
     *
     * @param fieldInfo 包含参数列表(fields)的字段信息对象
     * @param link      这个规则单元内部各条件之间的连接符 ("and" / "or")
     * @param context   规则执行上下文
     * @return 返回该规则单元的布尔判断结果
     */
    boolean process(RuleNode.FieldInfo fieldInfo, String link, RuleContext context);

    /**
     * 每个处理器必须声明它负责处理哪个 table_field ID。
     * @return table_field 的唯一标识符
     */
    String getHandledTableField();
}