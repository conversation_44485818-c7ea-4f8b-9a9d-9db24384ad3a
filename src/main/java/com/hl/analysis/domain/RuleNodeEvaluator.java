package com.hl.analysis.domain;

import com.hl.analysis.engine.RuleComponent;
import com.hl.analysis.handler.RuleUnitHandler;
import com.hl.analysis.handler.RuleUnitHandlerRegistry;
import lombok.RequiredArgsConstructor;

/**
 * 单个规则节点的评估器（组合模式的叶子节点）。
 * 新版本：根据 table_field 寻找并委托给一个专门的 Handler 处理。
 */
@RequiredArgsConstructor
public class RuleNodeEvaluator implements RuleComponent {

    private final RuleNode ruleNode;
    private final RuleUnitHandlerRegistry handlerRegistry;

    @Override
    public boolean evaluate(RuleContext context) {
        RuleNode.FieldInfo fieldInfo = ruleNode.getField();
        if (fieldInfo == null || fieldInfo.getTable_field() == null) {
            // 如果没有 table_field，无法处理，视为 false
            return false;
        }

        // 1. 根据 table_field 从注册中心获取对应的处理器
        String tableFieldId = fieldInfo.getTable_field();
        RuleUnitHandler handler = handlerRegistry.getHandler(tableFieldId);

        // 2. 将整个 fieldInfo 和 link 委托给 handler 处理
        return handler.process(fieldInfo, ruleNode.getLink(), context);
    }
}