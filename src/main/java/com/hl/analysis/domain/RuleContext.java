package com.hl.analysis.domain;


import com.hl.building.domain.WjLyActionsConfig;
import com.hl.building.domain.WjLyWarnTaskConfig;
import lombok.Data;
import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 规则执行上下文。
 * 贯穿整个规则执行过程，用于传递初始参数和记录执行详情。
 */
@Data
public class RuleContext {

    /**
     * 初始上下文参数，人员分析时为身份证号，企业分析时为企业信用代码。
     */
    private final String initialIdentifier;

    /**
     * 分析类型, "PERSON" 或 "COMPANY"
     */
    private final String analysisType;

    private boolean finalResult;

    private WjLyActionsConfig actionsConfig;

    private WjLyWarnTaskConfig taskConfig;
    /**
     * 存储每一步字段处理的详细记录。
     */
    @Getter
    private final List<ExecutionRecord> executionRecords = new ArrayList<>();

    @Getter
    private final Map<String, Object> extendData = new HashMap<>();

    public RuleContext(String initialIdentifier, String analysisType,WjLyActionsConfig actionsConfig) {
        this.initialIdentifier = initialIdentifier;
        this.analysisType = analysisType;
        this.actionsConfig = actionsConfig;
    }

    /**
     * 添加一条执行记录到日志中
     *
     * @param record 执行记录
     */
    public void addRecord(ExecutionRecord record) {
        this.executionRecords.add(record);
    }

    public void addExtend(Map<String, Object> extendData) {
        this.extendData.putAll(extendData);
    }


}