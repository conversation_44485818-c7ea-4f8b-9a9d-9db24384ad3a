package com.hl.analysis.domain;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.DatabindContext;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.annotation.JsonTypeIdResolver;
import com.fasterxml.jackson.databind.jsontype.impl.TypeIdResolverBase;
import lombok.Data;

import java.io.IOException;

/**
 * 这是一个包装类，用于Jackson进行多态反序列化。
 * 它会根据JSON对象中的 "type" 字段来决定将其解析为 GroupNode 还是 RuleNode。
 */
@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.CUSTOM, property = "type", visible = true)
@JsonTypeIdResolver(RuleDefinition.RuleTypeIdResolver.class)
public abstract class RuleDefinition {
    private String uuid;
    private String type;
    private String link;

    /**
     * 自定义TypeIdResolver，根据'type'字段的值决定实例化的具体类。
     */
    public static class RuleTypeIdResolver extends TypeIdResolverBase {
        private JavaType superType;

        @Override
        public void init(JavaType baseType) {
            this.superType = baseType;
        }

        @Override
        public String idFromValue(Object value) {
            return idFromValueAndType(value, value.getClass());
        }

        @Override
        public String idFromValueAndType(Object value, Class<?> suggestedType) {
            if (value instanceof GroupNode) {
                return "group";
            } else if (value instanceof RuleNode) {
                return "rule";
            }
            return null;
        }

        @Override
        public JavaType typeFromId(DatabindContext context, String id) throws IOException {
            if ("group".equals(id)) {
                return context.constructSpecializedType(superType, GroupNode.class);
            } else if ("rule".equals(id)) {
                return context.constructSpecializedType(superType, RuleNode.class);
            }
            throw new IOException("无法识别的规则类型: " + id);
        }

        @Override
        public JsonTypeInfo.Id getMechanism() {
            return JsonTypeInfo.Id.CUSTOM;
        }
    }
}