package com.hl.analysis.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class RuleNode extends RuleDefinition {

    private FieldInfo field;

    @Data
    public static class FieldInfo {
        private String table_field; // 关联的表或数据源标识
        private List<FieldCondition> fields;
    }

    @Data
    public static class FieldCondition {
        private String id;
        private String column; // 核心：字段标识，如 "recent_day"
        private String type;
        private String condition;
        private String condition_value;
        private Object value; // 值可以是 int, boolean, string 等
        private String start;
        private String end;
    }
}