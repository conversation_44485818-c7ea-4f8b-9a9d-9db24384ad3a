package com.hl.analysis.domain;

import lombok.Builder;
import lombok.Value;

/**
 * 单个字段的执行结果记录。
 * 使用 @Value 注解（Lombok）使其成为一个不可变的值对象。
 */
@Value
@Builder
public class ExecutionRecord {
    /**
     * 对应规则或条件的UUID
     */
    String ruleId;

    /**
     * 被检查的字段名，如 "recent_day"
     */
    String fieldName;

    /**
     * 规则描述，说明了判断的逻辑
     */
    String description;

    /**
     * 期望的值或条件
     */
    String expected;

    /**
     * 实际从上下文中获取的值
     */
    String actual;

    /**
     * 本次判断的结果
     */
    boolean result;
}