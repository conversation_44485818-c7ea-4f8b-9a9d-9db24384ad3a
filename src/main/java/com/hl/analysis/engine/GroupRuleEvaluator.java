package com.hl.analysis.engine;

import com.hl.analysis.domain.RuleContext;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * 规则组的评估器（组合模式的容器节点）。
 * 负责处理一个 "type":"group" 的节点。
 */
@RequiredArgsConstructor
public class GroupRuleEvaluator implements RuleComponent {

    private final List<RuleComponent> children;
    private final String link; // "and" or "or"

    @Override
    public boolean evaluate(RuleContext context) {
        if (children == null || children.isEmpty()) {
            return true; // 空的group默认也为true
        }

        boolean isAnd = "and".equalsIgnoreCase(link);

        for (RuleComponent component : children) {
            boolean result = component.evaluate(context);
            if (isAnd && !result) {
                // AND逻辑，遇到false直接短路，返回false
                return false;
            }
            if (!isAnd && result) {
                // OR逻辑，遇到true直接短路，返回true
                return true;
            }
        }
        // 如果循环正常结束：
        // AND逻辑意味着所有子组件都为true
        // OR逻辑意味着所有子组件都为false
        return isAnd;
    }
}