package com.hl.analysis;


import com.alibaba.fastjson2.JSONObject;
import com.hl.analysis.service.RuleEngineService;
import com.hl.building.domain.WjLyActionsConfig;
import com.hl.building.service.WjLyActionsConfigService;
import com.hl.common.domain.R;
import com.hl.security.utils.PassToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController
@RequestMapping("/analysis")
@Slf4j
public class AnalysisController {

    @Autowired
    private RuleEngineService ruleEngineService;

    @Autowired
    private WjLyActionsConfigService wjLyActionsConfigService;

    @GetMapping("/test")
    public void testRuleEngine() {
        WjLyActionsConfig actionsConfigServiceById = wjLyActionsConfigService.getById("757a9ca29e72ce652a75079a6d4e005d");
        // 假设是人员分析
        String idCard = "******************";
        try {
            ruleEngineService.execute(actionsConfigServiceById, idCard, "PERSON");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @PostMapping("/webhook")
    @PassToken
    public R<?> webhook(@RequestParam(value = "phone") String phone,
            @RequestBody String json) {

        log.error(json);

        JSONObject object = JSONObject.parseObject(json);
        log.info(object.toString());

        log.info("手机号码:{}", phone);
        String string = object.getByPath("alerts[0].content").toString();
        log.info(string);

        return R.ok();
    }
}
