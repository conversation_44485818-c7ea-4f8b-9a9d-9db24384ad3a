package com.hl.analysis.util;

import cn.hutool.core.util.ReUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Date;

@Slf4j
public class RuleUtils {
    public static boolean evaluateNumberCondition(String opt, long actualValue, long targetValue) {
        switch (opt) {
            case ">=": return actualValue >= targetValue;
            case "<=": return actualValue <= targetValue;
            case ">": return actualValue > targetValue;
            case "<": return actualValue < targetValue;
            case "=": return actualValue == targetValue;
            default: return false;
        }
    }

    public static boolean evaluateDateCondition(String opt, Date actualDate, Date targetDate) {
        if (actualDate == null || targetDate == null) return false;
        switch (opt) {
            case "gt_equal": return actualDate.compareTo(targetDate) >= 0;
            case "lt_equal": return actualDate.compareTo(targetDate) <= 0;
            case "gt": return actualDate.compareTo(targetDate) > 0;
            case "lt": return actualDate.compareTo(targetDate) < 0;
            case "equal": return actualDate.compareTo(targetDate) == 0;
            default: return false;
        }
    }

    /**
     * 评估字符串内容是否匹配条件
     * @param content 待评估内容
     * @param opt 操作符
     * @param value 比较值
     * @return 是否匹配
     */
    public static boolean evaluateStringCondition(String content, String opt, String value) {
        if (content == null) return false;

        String[] keywords = value.split("[,，、]");
        switch (opt) {
            case "equal":  // 等于
                return content.equals(value);
            case "le_equal": // 不等于
                return !content.equals(value);
            case "like": // 包含
                return Arrays.stream(keywords).anyMatch(content::contains);
            case "not_like": // 不包含
                return Arrays.stream(keywords).noneMatch(content::contains);
            case "reg_exp": // 符合正则
                try {
                   return ReUtil.isMatch(value,content);
                } catch (Exception e) {
                    log.warn("正则表达式格式错误: {}", value);
                    return false;
                }
            default:
                log.error("不支持的操作符: {}", opt);
                return false;
        }
    }


} 