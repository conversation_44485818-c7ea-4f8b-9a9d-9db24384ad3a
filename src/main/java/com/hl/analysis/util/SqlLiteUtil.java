package com.hl.analysis.util;

import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.List;

public class SqlLiteUtil {

    public static void main(String[] args) throws SQLException {
        DataSource dataSource = getDataSource("conf/sql-data/zww_data.db");
        List<Entity> query = DbUtil.use(dataSource).query("select * from wj_employee_insurance_info");
        System.out.println(query.size());
        DbUtil.close(dataSource);
    }

    public static DataSource getDataSource(String dbPath) {
        DriverManagerDataSource ds = new DriverManagerDataSource();
        ds.setDriverClassName("org.sqlite.JDBC");
        ds.setUrl("jdbc:sqlite:" + dbPath);
        return ds;
    }
}
