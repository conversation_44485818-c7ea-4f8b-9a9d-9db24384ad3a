package com.hl.analysis.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.analysis.domain.RuleContext;
import com.hl.building.domain.WjLyActionsConfig;
import com.hl.building.domain.WjLyWarnTaskConfig;
import com.hl.building.domain.WjPersonInfo;
import com.hl.building.service.WjLyActionsConfigService;
import com.hl.building.service.WjLyWarnTaskConfigService;
import com.hl.building.service.WjPersonInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class AnalysisTaskService {

    private final WjLyWarnTaskConfigService wjLyWarnTaskConfigService;

    private final WjLyActionsConfigService wjLyActionsConfigService;
    private final WjPersonInfoService wjPersonInfoService;

    private final RuleEngineService ruleEngineService;

    private final ApplicationEventPublisher applicationEventPublisher;

//    @EventListener(ApplicationReadyEvent.class)
    public void analysisPerson(){
        // 定时执行人员分析任务
        List<WjLyWarnTaskConfig> list = wjLyWarnTaskConfigService.list(Wrappers.<WjLyWarnTaskConfig>lambdaQuery()
                .eq(WjLyWarnTaskConfig::getTaskAction, 1)
                .eq(WjLyWarnTaskConfig::getTaskStatus, 1));

        for (WjLyWarnTaskConfig taskConfig : list) {
            String actionsConfig = taskConfig.getActionsConfig();
            WjLyActionsConfig actionsConfigServiceById = wjLyActionsConfigService.getById(actionsConfig);
            submitPersonTask(actionsConfigServiceById, taskConfig);
        }
    }

    public void submitPersonTask(WjLyActionsConfig action,WjLyWarnTaskConfig taskConfig){
        List<WjPersonInfo> list = wjPersonInfoService.list();
        for (WjPersonInfo personInfo : list) {
            String idCard = personInfo.getIdCard();
            try {
                RuleContext person = ruleEngineService.execute(action, idCard, "PERSON");
                if (person.isFinalResult()){
                    person.setTaskConfig(taskConfig);
                    applicationEventPublisher.publishEvent(person);
                    log.info("任务比对成功");
                    log.info(person.toString());
                }
            } catch (Exception e) {
                log.error(e.getMessage(),e);
            }
        }

    }
}
