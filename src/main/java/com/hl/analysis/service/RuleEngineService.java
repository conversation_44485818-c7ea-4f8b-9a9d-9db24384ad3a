package com.hl.analysis.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hl.analysis.domain.*;
import com.hl.analysis.engine.GroupRuleEvaluator;
import com.hl.analysis.engine.RuleComponent;
import com.hl.analysis.handler.RuleUnitHandlerRegistry;
import com.hl.building.domain.WjLyActionsConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 规则引擎主服务。
 * 负责解析规则、构建执行树并启动评估过程。
 */
@Service
@RequiredArgsConstructor
public class RuleEngineService {

    private final ObjectMapper objectMapper; // Spring Boot 自动配置
    private final RuleUnitHandlerRegistry handlerRegistry;

    /**
     * 执行规则分析
     *
     * @param jsonRule         规则定义的JSON字符串
     * @param initialIdentifier 初始上下文标识（身份证或企业代码）
     * @param analysisType     分析类型 ("PERSON" 或 "COMPANY")
     * @return 返回最终的布尔结果
     * @throws IOException JSON解析失败时抛出
     */
    public RuleContext execute(WjLyActionsConfig actionsConfig, String initialIdentifier, String analysisType) throws IOException {
        // 1. 解析JSON
        List<RuleDefinition> definitions = objectMapper.readValue(actionsConfig.getConfig().toJSONString(), new TypeReference<List<RuleDefinition>>() {});

        // 2. 创建上下文
        RuleContext context = new RuleContext(initialIdentifier, analysisType,actionsConfig);

        // 3. 构建执行树 (根节点是一个隐式的 "AND" group)
        List<RuleComponent> components = definitions.stream()
                .map(def -> buildComponent(def, context))
                .collect(Collectors.toList());

        RuleComponent root = new GroupRuleEvaluator(components, "and");

        // 4. 执行并返回结果
        boolean finalResult = root.evaluate(context);

        // 5. 可以在这里处理或打印执行记录
        System.out.println("------ 执行详情记录 ------");
        context.getExecutionRecords().forEach(System.out::println);
        System.out.println("-------------------------");
        System.out.println("最终分析结果: " + finalResult);
        context.setFinalResult(finalResult);

        return context;
    }

    /**
     * 递归构建规则组件树
     *
     * @param definition  从JSON解析出的规则定义
     * @param context     执行上下文
     * @return 构建好的规则组件
     */
    /**
     * 递归构建规则组件树 (已更新)
     */
    private RuleComponent buildComponent(RuleDefinition definition, RuleContext context) {
        if (definition instanceof GroupNode) {
            GroupNode group = (GroupNode) definition;
            List<RuleComponent> children = group.getValue().stream()
                    .map(childDef -> buildComponent(childDef, context))
                    .collect(Collectors.toList());
            return new GroupRuleEvaluator(children, group.getLink());
        } else if (definition instanceof RuleNode) {
            RuleNode rule = (RuleNode) definition;
            // 使用新的评估器和注册中心
            return new RuleNodeEvaluator(rule, handlerRegistry);
        }
        throw new IllegalArgumentException("未知的规则定义类型: " + definition.getClass().getName());
    }
}
