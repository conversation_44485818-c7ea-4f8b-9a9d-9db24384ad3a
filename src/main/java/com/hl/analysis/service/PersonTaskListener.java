package com.hl.analysis.service;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.analysis.domain.RuleContext;
import com.hl.building.domain.WjPersonInfo;
import com.hl.building.service.WjLyTaskResultPersonService;
import com.hl.building.service.WjPersonInfoService;
import com.hl.warn.domain.WjLyTaskResultPerson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PersonTaskListener {

    private final WjLyTaskResultPersonService wjLyTaskResultPersonService;

    private final WjPersonInfoService wjPersonInfoService;

    @EventListener(RuleContext.class)
    public void receivePersonResult(RuleContext ruleContext) {
        log.info("ruleContext:{}", ruleContext);
        WjLyTaskResultPerson wjLyTaskResultPerson = new WjLyTaskResultPerson();
        wjLyTaskResultPerson.setGmsfhm(ruleContext.getInitialIdentifier());
        wjLyTaskResultPerson.setTaskId(ruleContext.getTaskConfig().getId());
        wjLyTaskResultPerson.setTaskName(ruleContext.getTaskConfig().getTaskName());

        WjPersonInfo one = wjPersonInfoService.getOne(Wrappers.<WjPersonInfo>lambdaQuery()
                .eq(WjPersonInfo::getIdCard, ruleContext.getInitialIdentifier()));
        wjLyTaskResultPerson.setXm(one.getName());

        String detail = "";

        Map<String, Object> extendData = ruleContext.getExtendData();
        if (extendData.containsKey("jq")) {
            // 警情信息
            JSONArray jq = JSONArray.from(extendData.get("jq"));
            List<String> javaList = jq.toJavaList(String.class);
            detail += "警情:"+String.join(",", javaList);
        }


        wjLyTaskResultPerson.setDetails(detail);

        wjLyTaskResultPerson.setCreateTime(new Date());
        wjLyTaskResultPersonService.save(wjLyTaskResultPerson);

    }
}
