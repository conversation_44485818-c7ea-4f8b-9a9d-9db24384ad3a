package com.hl.building.services.impl;

import com.hl.building.domain.dto.AddOrUpdHouseReq;
import com.hl.building.domain.dto.HouseDetailReq;
import com.hl.building.domain.tables.HouseInfo;
import com.hl.building.domain.vo.HouseInfoVo;
import com.hl.building.mapper.BuildingMapper;
import com.hl.building.mapper.CompanyMapper;
import com.hl.building.mapper.HouseMapper;
import com.hl.building.services.HouseService;
import com.hl.common.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class HouseServiceImpl implements HouseService {
    @Resource
    HouseMapper houseMapper;
    @Resource
    BuildingMapper buildingMapper;
    @Resource
    CompanyMapper companyMapper;

    @Override
    public void add(AddOrUpdHouseReq req) {
        HouseInfo houseInfo = new HouseInfo();
        BeanUtils.copyProperties(req, houseInfo);
        houseMapper.insert(houseInfo);
    }

    @Override
    public void update(AddOrUpdHouseReq req) {
        HouseInfo houseInfo = new HouseInfo();
        BeanUtils.copyProperties(req, houseInfo);
        houseMapper.updateById(houseInfo);
    }

    @Override
    public HouseInfoVo detail(HouseDetailReq req) {
        HouseInfoVo houseInfo = houseMapper.detail(req);
        return houseInfo;
    }
}
