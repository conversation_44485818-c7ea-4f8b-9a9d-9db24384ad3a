package com.hl.building.services.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hl.building.domain.dto.ListPersonReq;
import com.hl.building.domain.tables.CompanyInfo;
import com.hl.building.domain.tables.PersonInfo;
import com.hl.building.mapper.CompanyMapper;
import com.hl.building.mapper.PersonMapper;
import com.hl.building.services.PersonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class PersonServiceImpl implements PersonService {
    @Resource
    PersonMapper personMapper;
    @Resource
    CompanyMapper companyMapper;
    @Override
    public List<PersonInfo> list(ListPersonReq req) {
        List<CompanyInfo> companyInfoList = companyMapper.selectList(new LambdaQueryWrapper<CompanyInfo>().eq(CompanyInfo::getBuildId, req.getBuildId()).eq(CompanyInfo::getIsLogout,0));
        List<PersonInfo> personInfos = personMapper.selectList(null);
        Map<Integer,PersonInfo> personInfoMap = new HashMap<>();
        if (!personInfos.isEmpty()){
            for (PersonInfo personInfo : personInfos) {
                personInfoMap.put(Math.toIntExact(personInfo.getId()),personInfo);
            }
        }
        Set<Integer> ids = new HashSet<>();
        if (!companyInfoList.isEmpty()){
            for (CompanyInfo companyInfo : companyInfoList) {
                if ((req.getIsCorporate() == 1 || req.getIsFocus() == 1)&& !companyInfo.getCorporate().isEmpty()){
                    for (Integer i : companyInfo.getCorporate()) {
                        if (personInfoMap.containsKey(i))
                            ids.add(i);
                    }
                }
                if (req.getIsFocus() == 1) {
                    if (!companyInfo.getFinance().isEmpty()) {
                        for (Integer i : companyInfo.getFinance()) {
                            if (personInfoMap.containsKey(i))
                                ids.add(i);
                        }
                    }
                    if (!companyInfo.getExecutives().isEmpty()) {
                        for (Integer i : companyInfo.getExecutives()) {
                            if (personInfoMap.containsKey(i))
                                ids.add(i);
                        }
                    }
                    if (!companyInfo.getOtherPerson().isEmpty()) {
                        for (Integer i : companyInfo.getOtherPerson()) {
                            if (personInfoMap.containsKey(i))
                                ids.add(i);
                        }
                    }
                }
            }
        }
        if (ids.isEmpty())
            return  new ArrayList<>();
        return personMapper.selectVoList(req,ids);
    }
}
