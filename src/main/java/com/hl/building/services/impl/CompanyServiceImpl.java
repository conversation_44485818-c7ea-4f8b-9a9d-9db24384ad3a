package com.hl.building.services.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hl.building.domain.dto.*;
import com.hl.building.domain.tables.CompanyInfo;
import com.hl.building.domain.tables.HouseInfo;
import com.hl.building.domain.tables.PersonInfo;
import com.hl.building.domain.vo.CompanyDetailVo;
import com.hl.building.domain.vo.CompanyDistributionVo;
import com.hl.building.domain.vo.CompanyInfoVo;
import com.hl.building.domain.vo.CountNatureVo;
import com.hl.building.mapper.CompanyMapper;
import com.hl.building.mapper.HouseMapper;
import com.hl.building.mapper.PersonMapper;
import com.hl.building.services.CompanyService;
import com.hl.common.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CompanyServiceImpl implements CompanyService {
    @Resource
    CompanyMapper companyMapper;
    @Resource
    HouseMapper houseMapper;
    @Resource
    PersonMapper personMapper;

    @Override
    public void update(AddOrUpdCompanyReq req) {
        CompanyInfo companyInfo = new CompanyInfo();
        BeanUtils.copyProperties(req, companyInfo);
        companyMapper.updateById(companyInfo);
    }

    @Override
    public List<CompanyInfoVo> list(ListCompanyReq req) {

        List<CompanyInfoVo> list = companyMapper.selectVoList(req);
        Map<Integer, PersonInfo> personInfoMap = new HashMap<>();
        Map<Integer, HouseInfo> houseInfoMap = new HashMap<>();
        List<PersonInfo> personInfos = personMapper.selectList(null);
        if (!personInfos.isEmpty()) {
            for (PersonInfo personInfo : personInfos) {
                personInfoMap.put(Math.toIntExact(personInfo.getId()), personInfo);
            }
        }
        List<HouseInfo> houseInfos = houseMapper.selectList(null);
        if (!houseInfos.isEmpty()) {
            for (HouseInfo houseInfo : houseInfos) {
                houseInfoMap.put((int) houseInfo.getId(), houseInfo);
            }
        }

        for (CompanyInfoVo companyInfoVo : list) {
            if (ObjectUtils.isNotEmpty(companyInfoVo.getHouseId()) && !companyInfoVo.getHouseId().isEmpty()) {
                List<HouseInfo> houseInfoList = new ArrayList<>();
                for (Integer string : companyInfoVo.getHouseId()) {
                    if (houseInfoMap.containsKey(string))
                        houseInfoList.add(houseInfoMap.get(string));
                }
                companyInfoVo.setHouseInfo(houseInfoList);
            }
            if (ObjectUtils.isNotEmpty(companyInfoVo.getOtherPerson()) && !companyInfoVo.getOtherPerson().isEmpty()) {
                List<PersonInfo> otherPersonInfo = new ArrayList<>();
                for (Integer string : companyInfoVo.getOtherPerson()) {
                    if (personInfoMap.containsKey(string))
                        otherPersonInfo.add(personInfoMap.get(string));
                }
                companyInfoVo.setOtherPersonInfo(otherPersonInfo);
            }
            if (ObjectUtils.isNotEmpty(companyInfoVo.getCorporate()) && !companyInfoVo.getCorporate().isEmpty()) {
                List<PersonInfo> corporateInfo = new ArrayList<>();
                for (Integer string : companyInfoVo.getCorporate()) {
                    if (personInfoMap.containsKey(string))
                        corporateInfo.add(personInfoMap.get(string));
                }
                companyInfoVo.setCorporateInfo(corporateInfo);
            }
            if (ObjectUtils.isNotEmpty(companyInfoVo.getExecutives()) && !companyInfoVo.getExecutives().isEmpty()) {
                List<PersonInfo> executivesInfo = new ArrayList<>();
                for (Integer string : companyInfoVo.getExecutives()) {
                    if (personInfoMap.containsKey(string))
                        executivesInfo.add(personInfoMap.get(string));
                }
                companyInfoVo.setExecutivesInfo(executivesInfo);
            }
            if (ObjectUtils.isNotEmpty(companyInfoVo.getFinance()) && !companyInfoVo.getFinance().isEmpty()) {
                List<PersonInfo> financeInfo = new ArrayList<>();
                for (Integer string : companyInfoVo.getFinance()) {
                    if (personInfoMap.containsKey(string))
                        financeInfo.add(personInfoMap.get(string));
                }
                companyInfoVo.setFinanceInfo(financeInfo);
            }
        }
        //查询企业人员
        if (StringUtils.isNotBlank(req.getQuery())) {
            return list.stream().filter(l -> {
                if ((ObjectUtils.isNotEmpty(l.getBuildName()) && l.getBuildName().contains(req.getQuery())) || l.getCompanyName().contains(req.getQuery()) || (StringUtils.isNotBlank(l.getCompanyAddress()) && l.getCompanyAddress().contains(req.getQuery())))
                    return true;
                if (ObjectUtils.isNotEmpty(l.getCorporateInfo())) {
                    for (PersonInfo personInfo : l.getCorporateInfo()) {
                        if (personInfo.toString().contains(req.getQuery()))
                            return true;
                    }
                }
                if (ObjectUtils.isNotEmpty(l.getExecutivesInfo())) {
                    for (PersonInfo personInfo : l.getExecutivesInfo()) {
                        if (personInfo.toString().contains(req.getQuery()))
                            return true;
                    }
                }
                if (ObjectUtils.isNotEmpty(l.getFinanceInfo())) {
                    for (PersonInfo personInfo : l.getFinanceInfo()) {
                        if (personInfo.toString().contains(req.getQuery()))
                            return true;
                    }
                }
                if (ObjectUtils.isNotEmpty(l.getOtherPersonInfo())) {
                    for (PersonInfo personInfo : l.getOtherPersonInfo()) {
                        if (personInfo.toString().contains(req.getQuery()))
                            return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<CompanyDistributionVo> company_distribution(DistributionReq req) {
        List<CompanyDistributionVo> list = companyMapper.company_distribution(req);
        for (CompanyDistributionVo companyDistributionVo : list) {
            List<Integer> corporate = companyDistributionVo.getCorporate();
            if (ObjectUtils.isNotEmpty(corporate)) {
                List<PersonInfo> personInfos = personMapper.selectBatchIds(corporate);
                companyDistributionVo.setCorporateInfo(personInfos);
            }
            List<HouseInfo> houseInfoList = new ArrayList<>();
            if (ObjectUtils.isNotEmpty(companyDistributionVo.getHouseId())) {
                for (Integer string : companyDistributionVo.getHouseId()) {
                    List<HouseInfo> houseInfoList1 = houseMapper.selectList(new LambdaQueryWrapper<HouseInfo>().eq(HouseInfo::getId, string));
                    if (!houseInfoList1.isEmpty())
                        houseInfoList.add(houseInfoList1.get(0));
                }
                companyDistributionVo.setHouseInfoList(houseInfoList);
            }
        }
        return list;
    }

    @Override
    public List<CountNatureVo> count_nature(BuildDetailReq req) {
        List<CompanyInfo> list = companyMapper.selectCountNature(req);
        List<CountNatureVo> countNatureVos = new ArrayList<>();
        if (!list.isEmpty()) {
            Map<String, List<CompanyInfo>> houseNature = list.stream().collect(Collectors.groupingBy(CompanyInfo::getHouseNature));
            for (Map.Entry<String, List<CompanyInfo>> stringListEntry : houseNature.entrySet()) {
                CountNatureVo countNatureVo = new CountNatureVo();
                countNatureVo.setHouseNature(stringListEntry.getKey());
                Set<Integer> id = new HashSet<>();
                for (CompanyInfo companyInfo : stringListEntry.getValue()) {
//                    log.warn(companyInfo.toString());
                    if (ObjectUtils.isNotEmpty(companyInfo.getHouseId()) && !companyInfo.getHouseId().isEmpty()) {
                        for (Integer i : companyInfo.getHouseId()) {
                            HouseInfo houseInfo = houseMapper.selectById(i);
                            if (ObjectUtils.isNotEmpty(houseInfo))
                                id.add(i);
                        }

                    }
                }
                countNatureVo.setCount(id.size());
                countNatureVos.add(countNatureVo);
            }
        }
        return countNatureVos;
    }

    @Override
    public CompanyDetailVo detail(CompanyDetailReq req) {
        CompanyDetailVo companyDetailVo = new CompanyDetailVo();
        CompanyDistributionVo companyDistributionVo = companyMapper.detail(req);
        companyDetailVo.setCompanyName(companyDistributionVo.getCompanyName());
        companyDetailVo.setSjInfo(companyDistributionVo.getSjInfo());
        companyDetailVo.setSaInfo(companyDistributionVo.getSaInfo());
        if (ObjectUtils.isNotEmpty(companyDistributionVo.getHouseId()) && !companyDistributionVo.getHouseId().isEmpty()) {
            List<HouseInfo> houseInfoList = new ArrayList<>();
            for (Integer string : companyDistributionVo.getHouseId()) {
                List<HouseInfo> houseInfoList1 = houseMapper.selectList(new LambdaQueryWrapper<HouseInfo>().eq(HouseInfo::getId, string));
                if (!houseInfoList1.isEmpty()) {
                    HouseInfo houseInfo = houseInfoList1.get(0);
                    HouseDetailReq houseDetailReq = new HouseDetailReq();
                    houseDetailReq.setId((int) houseInfo.getId());
                    houseInfo.setHistoryCompanyNum(history_company(houseDetailReq).size());
                    PersonInfo personInfo = personMapper.selectOne(new LambdaQueryWrapper<PersonInfo>().eq(PersonInfo::getId, houseInfo.getPerson()));
                    if (ObjectUtils.isNotEmpty(personInfo))
                        houseInfo.setPersonInfo(personInfo);
                    houseInfoList.add(houseInfo);
                }
            }
            companyDetailVo.setHouseInfo(houseInfoList);
        }
        if (ObjectUtils.isNotEmpty(companyDistributionVo.getOtherPerson()) && !companyDistributionVo.getOtherPerson().isEmpty()) {
            List<PersonInfo> otherPersonInfo = new ArrayList<>();
            for (Integer string : companyDistributionVo.getOtherPerson()) {
                List<PersonInfo> personInfo1 = personMapper.selectList(new LambdaQueryWrapper<PersonInfo>().eq(PersonInfo::getId, string));
                if (!personInfo1.isEmpty())
                    otherPersonInfo.add(personInfo1.get(0));
            }
            companyDetailVo.setOtherPersonInfo(otherPersonInfo);
        }
        if (ObjectUtils.isNotEmpty(companyDistributionVo.getCorporate()) && !companyDistributionVo.getCorporate().isEmpty()) {
            List<PersonInfo> corporateInfo = new ArrayList<>();
            for (Integer string : companyDistributionVo.getCorporate()) {
                List<PersonInfo> personInfo2 = personMapper.selectList(new LambdaQueryWrapper<PersonInfo>().eq(PersonInfo::getId, string));
                if (!personInfo2.isEmpty())
                    corporateInfo.add(personInfo2.get(0));
            }
            companyDetailVo.setCorporateInfo(corporateInfo);
        }
        if (ObjectUtils.isNotEmpty(companyDistributionVo.getExecutives()) && !companyDistributionVo.getExecutives().isEmpty()) {
            List<PersonInfo> executivesInfo = new ArrayList<>();
            for (Integer string : companyDistributionVo.getExecutives()) {
                List<PersonInfo> personInfo3 = personMapper.selectList(new LambdaQueryWrapper<PersonInfo>().eq(PersonInfo::getId, string));
                if (!personInfo3.isEmpty())
                    executivesInfo.add(personInfo3.get(0));
            }
            companyDetailVo.setExecutivesInfo(executivesInfo);
        }
        if (ObjectUtils.isNotEmpty(companyDistributionVo.getFinance()) && !companyDistributionVo.getFinance().isEmpty()) {
            List<PersonInfo> financeInfo = new ArrayList<>();
            for (Integer string : companyDistributionVo.getFinance()) {
                List<PersonInfo> personInfo4 = personMapper.selectList(new LambdaQueryWrapper<PersonInfo>().eq(PersonInfo::getId, string));
                if (!personInfo4.isEmpty())
                    financeInfo.add(personInfo4.get(0));
            }
            companyDetailVo.setFinanceInfo(financeInfo);
        }
        return companyDetailVo;
    }

    @Override
    public List<CompanyInfo> history_company(HouseDetailReq req) {
        Map<Integer, PersonInfo> personInfoMap = new HashMap<>();
        List<PersonInfo> personInfos = personMapper.selectList(null);
        if (!personInfos.isEmpty()) {
            for (PersonInfo personInfo : personInfos) {
                personInfoMap.put(Math.toIntExact(personInfo.getId()), personInfo);
            }
        }

        List<CompanyInfo> list = new ArrayList<>();
        List<CompanyInfo> companyInfoList = companyMapper.selectDelList();
        if (!companyInfoList.isEmpty()) {
            for (CompanyInfo companyInfo : companyInfoList) {
                if (ObjectUtils.isNotEmpty(companyInfo.getOtherPerson()) && !companyInfo.getOtherPerson().isEmpty()) {
                    List<PersonInfo> otherPersonInfo = new ArrayList<>();
                    for (Integer string : companyInfo.getOtherPerson()) {
                        if (personInfoMap.containsKey(string))
                            otherPersonInfo.add(personInfoMap.get(string));
                    }
                    companyInfo.setOtherPersonInfo(otherPersonInfo);
                }
                if (ObjectUtils.isNotEmpty(companyInfo.getCorporate()) && !companyInfo.getCorporate().isEmpty()) {
                    List<PersonInfo> corporateInfo = new ArrayList<>();
                    for (Integer string : companyInfo.getCorporate()) {
                        if (personInfoMap.containsKey(string))
                            corporateInfo.add(personInfoMap.get(string));
                    }
                    companyInfo.setCorporateInfo(corporateInfo);
                }
                if (ObjectUtils.isNotEmpty(companyInfo.getExecutives()) && !companyInfo.getExecutives().isEmpty()) {
                    List<PersonInfo> executivesInfo = new ArrayList<>();
                    for (Integer string : companyInfo.getExecutives()) {
                        if (personInfoMap.containsKey(string))
                            executivesInfo.add(personInfoMap.get(string));
                    }
                    companyInfo.setExecutivesInfo(executivesInfo);
                }
                if (ObjectUtils.isNotEmpty(companyInfo.getFinance()) && !companyInfo.getFinance().isEmpty()) {
                    List<PersonInfo> financeInfo = new ArrayList<>();
                    for (Integer string : companyInfo.getFinance()) {
                        if (personInfoMap.containsKey(string))
                            financeInfo.add(personInfoMap.get(string));
                    }
                    companyInfo.setFinanceInfo(financeInfo);
                }
                if (ObjectUtils.isNotEmpty(companyInfo.getHouseId()) && !companyInfo.getHouseId().isEmpty()) {
                    for (Integer i : companyInfo.getHouseId()) {
                        if (i == req.getId()) {
                            list.add(companyInfo);
                        }
                    }
                }
            }
        }
        return list;
    }
}
