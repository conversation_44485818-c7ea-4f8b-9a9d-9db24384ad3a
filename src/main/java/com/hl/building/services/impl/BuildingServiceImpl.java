package com.hl.building.services.impl;

import com.hl.building.domain.dto.AddOrUpdBuildReq;
import com.hl.building.domain.dto.BuildDetailReq;
import com.hl.building.domain.tables.BuildingInfo;
import com.hl.building.mapper.BuildingMapper;
import com.hl.building.mapper.HouseMapper;
import com.hl.building.services.BuildingService;
import com.hl.common.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class BuildingServiceImpl implements BuildingService {
    @Resource
    BuildingMapper buildingMapper;
    @Resource
    HouseMapper houseMapper;

    @Override
    public void add(AddOrUpdBuildReq req) {
        BuildingInfo buildingInfo = new BuildingInfo();
        BeanUtils.copyProperties(req, buildingInfo);
        buildingInfo.setCreateTime(new Date());
        buildingInfo.setGridX(req.getGridX());
        buildingInfo.setGridY(req.getGridY());
        buildingMapper.insert(buildingInfo);
        //生成初始楼宇示意图
//        for (int i = 1; i <= req.getLayerNum(); i++) {
//            for (int j = 1; j <= req.getGridX()*req.getGridY(); j++) {
//                HouseInfo houseInfo = new HouseInfo();
//                houseInfo.setHouseId(buildingInfo.getBuildId()+"-"+i+"-"+j);
//                houseInfo.setFloor(i);
//                houseInfo.setBuildId(buildingInfo.getBuildId());
//                houseMapper.insert(houseInfo);
//            }
//        }

    }

    @Override
    public void update(AddOrUpdBuildReq req) {
        //原始数据
        BuildingInfo info = buildingMapper.selectById(req.getBuildId());
        info.setBuildName(req.getBuildName());
        info.setAddress(req.getAddress());
//        info.setLayerNum(req.getLayerNum());
//        info.setGridNum(req.getGridNum());
//        info.setHouseNum(req.getLayerNum()*req.getGridNum());
        buildingMapper.updateById(info);
        //更新楼宇层数和户室数量
//        if ((info.getLayerNum() != req.getLayerNum()) || (info.getHouseNum() != req.getHouseNum())) {
//            //生成初始楼宇示意图
//            for (int i = info.getLayerNum() + 1; i <= req.getLayerNum(); i++) {
//                for (int j = 1; j <= req.getHouseNum() / 2; j++) {
//                    HouseInfo houseInfo = new HouseInfo();
//                    houseInfo.setHouseId(req.getBuildId() + "-" + i + "-" + j);
//                    houseInfo.setBuildId(buildingInfo.getBuildId());
//                    houseInfo.setLayerNo(i);
//                    houseInfo.setAreaNum(1);
//                    houseInfo.setCreateTime(new Date());
//                    houseMapper.insert(houseInfo);
//                }
//                //户室数量奇数
//                if (req.getHouseNum() % 2 != 0 && i == req.getLayerNum()) {
//                    HouseInfo houseInfo = new HouseInfo();
//                    houseInfo.setHouseId(req.getBuildId() + "-" + i + "-" + (req.getHouseNum() / 2 + 1));
//                    houseInfo.setBuildId(buildingInfo.getBuildId());
//                    houseInfo.setLayerNo(i);
//                    houseInfo.setAreaNum(1);
//                    houseInfo.setCreateTime(new Date());
//                    houseMapper.insert(houseInfo);
//                }
//            }
//        }
//        //原始层数和更新后的层数比较
//        if (info.getLayerNum() > req.getLayerNum()){
//            houseMapper.delete(new LambdaQueryWrapper<HouseInfo>().ge(HouseInfo::getLayerNo,info.getLayerNum()));
//        }
//        else if (info.getLayerNum() < req.getLayerNum()){
//            //生成初始楼宇示意图
//            for (int i = info.getLayerNum()+1; i <= req.getLayerNum(); i++) {
//                for (int j = 1; j <= req.getHouseNum()/2; j++) {
//                    HouseInfo houseInfo = new HouseInfo();
//                    houseInfo.setHouseId(req.getBuildName()+"-"+i+"-"+j);
//                    houseInfo.setBuildId(buildingInfo.getBuildId());
//                    houseInfo.setLayerNo(i);
//                    houseInfo.setAreaNum(1);
//                    houseInfo.setCreateTime(new Date());
//                    houseMapper.insert(houseInfo);
//                }
//                //户室数量奇数
//                if (req.getHouseNum()%2 != 0 && i == req.getLayerNum()){
//                    HouseInfo houseInfo = new HouseInfo();
//                    houseInfo.setHouseId(req.getBuildName()+"-"+i+"-"+(req.getHouseNum()/2+1));
//                    houseInfo.setBuildId(buildingInfo.getBuildId());
//                    houseInfo.setLayerNo(i);
//                    houseInfo.setAreaNum(1);
//                    houseInfo.setCreateTime(new Date());
//                    houseMapper.insert(houseInfo);
//                }
//            }
//        }
//        //原始户室数和更新后的户室数比较
//        if (info.getHouseNum() > req.getHouseNum()){
//            houseMapper.deleteUpdHouse(req.getHouseNum(),req.getBuildId());
//        }
//        else if (info.getHouseNum() < req.getHouseNum()) {
//            //生成初始楼宇示意图
//            for (int j = 1; j <= (req.getHouseNum()-info.getHouseNum()); j++) {
//                HouseInfo houseInfo = new HouseInfo();
//                houseInfo.setHouseId(req.getBuildName()+"-"+req.getLayerNum()+"-"+j);
//                houseInfo.setBuildId(buildingInfo.getBuildId());
//                houseInfo.setLayerNo(req.getLayerNum());
//                houseInfo.setAreaNum(1);
//                houseInfo.setCreateTime(new Date());
//                houseMapper.insert(houseInfo);
//            }
//        }
    }

    @Override
    public BuildingInfo detail(BuildDetailReq req) {
        BuildingInfo buildingInfo = buildingMapper.selectById(req.getBuildId());
        return buildingInfo;
    }
}
