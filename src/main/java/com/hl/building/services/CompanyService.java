package com.hl.building.services;

import com.hl.building.domain.dto.*;
import com.hl.building.domain.tables.CompanyInfo;
import com.hl.building.domain.vo.CompanyDetailVo;
import com.hl.building.domain.vo.CompanyDistributionVo;
import com.hl.building.domain.vo.CompanyInfoVo;
import com.hl.building.domain.vo.CountNatureVo;

import java.util.List;

public interface CompanyService {
    void update(AddOrUpdCompanyReq req);

    List<CompanyInfoVo> list(ListCompanyReq req);

    List<CompanyDistributionVo> company_distribution(DistributionReq req);

    List<CountNatureVo>  count_nature(BuildDetailReq req);

    CompanyDetailVo detail(CompanyDetailReq req);

    List<CompanyInfo> history_company(HouseDetailReq req);
}
