package com.hl.building.client.dsj.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 大数据局API配置类
 * 
 * <AUTHOR> Assistant
 */
@Data
@Component
@ConfigurationProperties(prefix = "dsj.api")
public class DsjApiConfig {
    
    /**
     * API基础URL
     */
    private String baseUrl = "http://127.0.0.1:9090";
    
    /**
     * 认证URL
     */
    private String authUrl = "http://127.0.0.1:9090/auth/accesstoken/create";
    
    /**
     * 应用ID
     */
    private String appId = "saca_OrBcEF3Zy0";
    
    /**
     * 应用密钥
     */
    private String appSecret = "qqcaN2Ux295oXPSr138z9Tafgi4KL9bn";
    
    /**
     * 请求超时时间（毫秒）
     */
    private int timeout = 30000;
    
    /**
     * Token刷新间隔（毫秒）
     */
    private long tokenRefreshInterval = 3600000; // 1小时
    
    /**
     * 最大重试次数
     */
    private int maxRetries = 3;
    
    /**
     * 默认请求头配置
     */
    @Data
    public static class DefaultHeaders {
        private String authorityType = "2";
        private String elementsVersion = "1.00";
        private String heartbeat = "1";
        private String testMarker = "0";
    }
    
    private DefaultHeaders defaultHeaders = new DefaultHeaders();
}
