package com.hl.building.client;

import com.alibaba.fastjson2.JSONObject;
import com.hl.common.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "hl-wj-jq")
public interface JqFeignClient {




    @PostMapping("/jqbz/list")
    R<?> getPoliceData(@RequestBody JSONObject param, @RequestHeader("token") String token);



    @PostMapping("/dict/tree")
    R<?> getDictTree(@RequestBody JSONObject param,@RequestHeader("token") String token);

}
