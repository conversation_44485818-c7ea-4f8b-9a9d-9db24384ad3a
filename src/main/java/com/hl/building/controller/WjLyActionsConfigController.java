package com.hl.building.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjLyActionsConfig;
import com.hl.building.domain.WjLyDatasourceInfo;
import com.hl.building.domain.vo.WjLyActionConfigPageReqVO;
import com.hl.building.service.WjLyActionsConfigService;
import com.hl.building.service.WjLyDatasourceInfoService;
import com.hl.building.util.SsoUtil;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/lyActionsConfig")
@Slf4j
@Api(tags = "行为配置管理")
@RequiredArgsConstructor
public class WjLyActionsConfigController {

    private final WjLyActionsConfigService wjLyActionsConfigService;


    @PostMapping("/add")
    @ApiOperation(value = "新增行为配置")
    public R<Boolean> addActionsConfig(@RequestBody WjLyActionsConfig actionsConfig) {
        actionsConfig.setCreateUser(SsoUtil.getUserIdCard());
        actionsConfig.setUpdateUser(SsoUtil.getUserIdCard());
        actionsConfig.setCreateTime(new Date());
        actionsConfig.setUpdateTime(new Date());
        return R.ok(wjLyActionsConfigService.save(actionsConfig));
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改行为配置")
    public R<Boolean> updateActionsConfig(@RequestBody WjLyActionsConfig actionsConfig) {
        actionsConfig.setUpdateUser(SsoUtil.getUserIdCard());
        actionsConfig.setUpdateTime(new Date());
        return R.ok(wjLyActionsConfigService.updateById(actionsConfig));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除行为配置")
    public R<Boolean> deleteActionsConfig(@RequestBody WjLyActionsConfig actionsConfig) {
        return R.ok(wjLyActionsConfigService.removeById(actionsConfig.getId()));
    }

    @PostMapping("/list")
    @ApiOperation(value = "行为配置列表")
    public R<List<WjLyActionsConfig>> listActionsConfig(@RequestBody WjLyActionConfigPageReqVO actionConfigPageReqVO) {
        Page<WjLyActionsConfig> page = wjLyActionsConfigService.page(new Page<>(actionConfigPageReqVO.getPage(), actionConfigPageReqVO.getLimit()),
                Wrappers.<WjLyActionsConfig>lambdaQuery()
                        .like(StringUtils.isNotBlank(actionConfigPageReqVO.getActionsName()), WjLyActionsConfig::getActionsName, actionConfigPageReqVO.getActionsName())
                        .orderByDesc(WjLyActionsConfig::getId));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    private final WjLyDatasourceInfoService wjLyDatasourceInfoService;

    @PostMapping("/listDatasource")
    @ApiOperation(value = "列出数据源")
    public R<List<WjLyDatasourceInfo>> listDatasource(@RequestBody Map<String, Object> req) {
        Page<WjLyDatasourceInfo> page = wjLyDatasourceInfoService.page(new Page<>((int) req.get("page"), (int) req.get("limit")));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

}
