package com.hl.building.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.vo.WjLyTaskResultCompanyReqVO;
import com.hl.building.domain.vo.WjLyTaskResultPersonReqVO;
import com.hl.common.domain.R;
import com.hl.building.service.WjLyTaskResultCompanyService;
import com.hl.building.service.WjLyTaskResultPersonService;
import com.hl.warn.domain.WjLyTaskResultCompany;
import com.hl.warn.domain.WjLyTaskResultPerson;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/lyTaskResult")
@RequiredArgsConstructor
@Api(tags = "预警结果")
public class WjLyTaskResultController {


    private final WjLyTaskResultPersonService wjLyTaskResultPersonService;

    private final WjLyTaskResultCompanyService wjLyTaskResultCompanyService;


    @PostMapping("/listPersonResult")
    @ApiOperation(value = "人员预警结果")
    public R<List<WjLyTaskResultPerson>> listPersonResult(@RequestBody WjLyTaskResultPersonReqVO taskResultPersonReqVO) {

        Page<WjLyTaskResultPerson> resultPersonPage = wjLyTaskResultPersonService.listResult(taskResultPersonReqVO);
        return R.ok(resultPersonPage.getRecords(), (int) resultPersonPage.getTotal());
    }


    @PostMapping("/listCompanyResult")
    @ApiOperation(value = "公司预警结果")
    public R<List<WjLyTaskResultCompany>> listCompanyResult(@RequestBody WjLyTaskResultCompanyReqVO taskResultCompanyReqVO) {

        Page<WjLyTaskResultCompany> resultCompanyPage = wjLyTaskResultCompanyService.listResult(taskResultCompanyReqVO);

        return R.ok(resultCompanyPage.getRecords(), (int) resultCompanyPage.getTotal());

    }
}
