//package com.hl.building.controller;
//
//import cn.hutool.core.collection.ListUtil;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.hl.building.domain.request.ListPersonReq;
//import com.hl.building.domain.request.SelectByIdCardReq;
//import com.hl.building.domain.tables.PersonInfo;
//import com.hl.building.mapper.PersonMapper;
//import com.hl.building.services.PersonService;
//import com.hl.common.domain.R;
//import com.hl.common.utils.BeanUtils;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.ObjectUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import java.util.List;
//
//@RequestMapping("/person")
//@RestController
//@Api(tags = "人员信息")
//@Slf4j
//public class PersonController {
//
//    @Resource
//    PersonMapper personMapper;
//    @Resource
//    PersonService personService;
//
//    @PostMapping("/add")
//    @ApiOperation(value = "添加人员信息")
//    public R add_person(@RequestBody PersonInfo req) {
//
//        if (!req.getPersonType().isEmpty()){
//            List<String> personType = req.getPersonType();
//            for (String string : personType) {
//                if("finance".equals(string))
//                    req.setIsFinance(1);
//                if("executives".equals(string))
//                    req.setIsExecutives(1);
//                if("corporate".equals(string))
//                    req.setIsCorporate(1);
//                if("otherPerson".equals(string))
//                    req.setIsOtherPerson(1);
//            }
//        }
//
//        PersonInfo personInfo = new PersonInfo();
//        BeanUtils.copyProperties(req, personInfo);
//        if (StringUtils.isNotBlank(req.getIdCard())){
//            List<PersonInfo> personInfos = personMapper.selectList(new LambdaQueryWrapper<PersonInfo>().eq(PersonInfo::getIdCard, req.getIdCard()));
//            //该人员已存在
//            if (!personInfos.isEmpty()){
//                //更新
//                for (PersonInfo info : personInfos) {
//                    req.setId(info.getId());
//                    BeanUtils.copyProperties(req, info);
//                    personMapper.updateById(info);
//                    return R.ok(info.getId());
//                }
//            }
//            else {
//                if (StringUtils.isNotBlank(personInfo.getName()) || StringUtils.isNotBlank(personInfo.getIdCard()) || StringUtils.isNotBlank(personInfo.getPhone()) || StringUtils.isNotBlank(personInfo.getCurrent()) || StringUtils.isNotBlank(personInfo.getDomicile())) {
//                    personMapper.insert(personInfo);
//                    return R.ok(personInfo.getId());
//                }
//            }
//        }
//        else {
//            if (StringUtils.isNotBlank(personInfo.getName()) || StringUtils.isNotBlank(personInfo.getIdCard()) || StringUtils.isNotBlank(personInfo.getPhone()) || StringUtils.isNotBlank(personInfo.getCurrent()) || StringUtils.isNotBlank(personInfo.getDomicile())) {
//                personMapper.insert(personInfo);
//                return R.ok(personInfo.getId());
//            }
//        }
//        return R.ok();
//    }
//
//    @PostMapping("/update")
//    @ApiOperation(value = "修改人员信息")
//    public R update(@RequestBody PersonInfo req) throws Exception {
//        if (!req.getPersonType().isEmpty()){
//            List<String> personType = req.getPersonType();
//            for (String string : personType) {
//                if("finance".equals(string))
//                    req.setIsFinance(1);
//                if("executives".equals(string))
//                    req.setIsExecutives(1);
//                if("corporate".equals(string))
//                    req.setIsCorporate(1);
//                if("otherPerson".equals(string))
//                    req.setIsOtherPerson(1);
//            }
//        }
//        PersonInfo personInfo = new PersonInfo();
//        BeanUtils.copyProperties(req, personInfo);
//        PersonInfo personInfo1 = personMapper.selectById(req.getId());
//        if (ObjectUtils.isEmpty(personInfo1))
//            personMapper.insert(personInfo);
//        else
//            personMapper.updateById(personInfo);
//        return R.ok(req.getId());
//    }
//
//    @PostMapping("/selectByIdCard")
//    @ApiOperation(value = "根据身份证查询个人信息")
//    public R<PersonInfo> selectByIdCard(@RequestBody SelectByIdCardReq req) throws Exception {
//        PersonInfo personInfo = personMapper.selectOne(new LambdaQueryWrapper<PersonInfo>().like(PersonInfo::getIdCard,req.getIdCard()));
//        return R.ok(personInfo);
//    }
//
//    @PostMapping("/list")
//    @ApiOperation(value = "人员列表")
//    public R<List<PersonInfo>> list(@RequestBody ListPersonReq req) throws Exception {
//        List<PersonInfo> list = personService.list(req);
//        return R.ok(ListUtil.page(req.getPage() - 1, req.getLimit(), list), list.size());
//    }
//}
