package com.hl.building.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.nacos.common.packagescan.resource.ClassPathResource;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.dto.WjLyJqxxPageReq;
import com.hl.building.domain.vo.WjLyJqxxPageRespVO;
import com.hl.building.service.WjLyJqxxService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Scanner;

@RestController
@RequestMapping("/wjLyJqxx")
@Api(tags = "楼宇关联警情")
@RequiredArgsConstructor
public class WjLyJqxxController {

    private final WjLyJqxxService wjLyJqxxService;

    @ApiOperation(value = "分页查询")
    @PostMapping("/page")
    public R<List<WjLyJqxxPageRespVO>> page(@RequestBody WjLyJqxxPageReq req) {
        if(req.getBjlx() != null && req.getBjlx().length() < 6){
            req.setBjlx("0" + req.getBjlx());
        }
        Page<WjLyJqxxPageRespVO> page = wjLyJqxxService.pageList(req);
        return R.ok(page.getRecords(),(int) page.getTotal());
    }


    @ApiOperation(value = "报警类型字典")
    @GetMapping("/tree")
    public R<JSONArray> getTreeData() throws IOException {
        ClassPathResource classPathResource = new ClassPathResource("data/dictBjlx.json");
        InputStream inputStream = classPathResource.getInputStream();
        Scanner scanner = new Scanner(inputStream, "UTF-8").useDelimiter("\\A");
        String jsonStr = scanner.hasNext() ? scanner.next() : "";
        JSONArray jsonArray = JSONArray.parseArray(jsonStr);
        return R.ok(jsonArray);
    }

//    @PostMapping("/list")
//    public R<List<WjLyJqxxPageRespVO>> list(@RequestBody WjLyJqxxPageReq req) {
//        List<WjLyJqxxPageRespVO> list = wjLyJqxxService.selectList(req);
//
//        return R.ok(list, list.size());
//    }
}
