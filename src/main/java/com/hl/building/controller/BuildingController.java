package com.hl.building.controller;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.building.domain.dto.AddOrUpdBuildReq;
import com.hl.building.domain.dto.BuildDetailReq;
import com.hl.building.domain.dto.ListBuildReq;
import com.hl.building.domain.tables.BuildingInfo;
import com.hl.building.domain.tables.CompanyInfo;
import com.hl.building.domain.tables.HouseInfo;
import com.hl.building.mapper.BuildingMapper;
import com.hl.building.mapper.CompanyMapper;
import com.hl.building.mapper.HouseMapper;
import com.hl.building.services.BuildingService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("/building")
@RestController
@Api(tags = "楼宇信息")
@Slf4j
public class BuildingController {

    @Resource
    BuildingMapper buildingMapper;
    @Resource
    BuildingService buildingService;
    @Resource
    HouseMapper houseMapper;
    @Resource
    CompanyMapper companyMapper;

    @PostMapping("/add")
    @ApiOperation(value = "新增楼宇")
    public R add(@RequestBody AddOrUpdBuildReq req) throws Exception {
        buildingService.add(req);
        return R.ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "楼宇编辑")
    public R update(@RequestBody AddOrUpdBuildReq req) {
        buildingService.update(req);
        return R.ok();
    }

//    @PostMapping("/detail")
//    @ApiOperation(value = "详情")
//    public R<BuildingInfo> detail(@RequestBody BuildDetailReq req) {
//        BuildingInfo buildingInfo =  buildingService.detail(req);
//        return R.ok(buildingInfo);
//    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除楼宇")
    public R delete(@RequestBody BuildDetailReq req) {
        buildingMapper.deleteById(req.getBuildId());
        houseMapper.delete(new LambdaUpdateWrapper<HouseInfo>().eq(HouseInfo::getBuildId, req.getBuildId()));
        return R.ok();
    }

    @PostMapping("/list")
    @ApiOperation(value = "楼宇列表")
    public R<List<BuildingInfo>> list(@RequestBody ListBuildReq req) throws Exception {
//        List<BuildingInfo> list = buildingMapper.selectList(StringUtils.isNotBlank(req.getKeyword()) ? new LambdaQueryWrapper<BuildingInfo>().like(BuildingInfo::getBuildName, req.getKeyword()) : null);
        List<BuildingInfo> list = buildingMapper.selectList(Wrappers.<BuildingInfo>lambdaQuery()
                .eq(StringUtils.isNotBlank(req.getBuildId()), BuildingInfo::getBuildId, req.getBuildId())
                .like(StringUtils.isNotBlank(req.getQuery()), BuildingInfo::getBuildName, req.getQuery()));
        for (BuildingInfo buildingInfo : list) {
            Long l = companyMapper.selectCount(new LambdaQueryWrapper<CompanyInfo>().eq(CompanyInfo::getBuildId, buildingInfo.getBuildId()));
            buildingInfo.setCompanyNum(Math.toIntExact(l));
            l = houseMapper.selectCount(new LambdaQueryWrapper<HouseInfo>().eq(HouseInfo::getBuildId, buildingInfo.getBuildId()));
            buildingInfo.setHouseNum(Math.toIntExact(l));
        }
        //
        return R.ok(ListUtil.page(req.getPage() - 1, req.getLimit(), list), list.size());
    }
}
