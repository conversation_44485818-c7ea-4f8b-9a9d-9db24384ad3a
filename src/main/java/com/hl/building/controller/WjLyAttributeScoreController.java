package com.hl.building.controller;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.KgmArchivesInfo;
import com.hl.building.domain.WjLyAttributeScore;
import com.hl.building.domain.vo.WjLyPersonAttrScoreAddReqVO;
import com.hl.building.domain.vo.WjLyPersonAttrScorePageReqVO;
import com.hl.building.util.SsoUtil;
import com.hl.common.domain.R;
import com.hl.building.service.KgmArchivesInfoService;
import com.hl.building.service.WjLyPersonAttrScoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Map;

@RestController
@RequestMapping("/lyAttrScore")
@Api(tags = "属性分值管理")
@RequiredArgsConstructor
public class WjLyAttributeScoreController {


    private final WjLyPersonAttrScoreService wjLyPersonAttrScoreService;

    @PostMapping("/add")
    @ApiOperation(value = "新增属性分值")
    public R<Boolean> addAttrScore(@RequestBody WjLyPersonAttrScoreAddReqVO req) {
        Boolean result = wjLyPersonAttrScoreService.addAttrScore(req);
        return R.ok(result);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改属性分值")
    public R<Boolean> updateAttrScore(@RequestBody WjLyAttributeScore req) {
        req.setUpdateUser(SsoUtil.getUserIdCard());
        req.setUpdateTime(new Date());
        Boolean result = wjLyPersonAttrScoreService.updateById(req);
        return R.ok(result);
    }


    @PostMapping("/list")
    @ApiOperation(value = "属性分值列表")
    public R<?> listAttrScore(@RequestBody WjLyPersonAttrScorePageReqVO req) {
        Page<WjLyAttributeScore> page = wjLyPersonAttrScoreService.page(new Page<>(req.getPage(), req.getLimit()),
                Wrappers.<WjLyAttributeScore>lambdaQuery()
                        .like(StringUtils.isNotBlank(req.getAttributeName()),
                                WjLyAttributeScore::getAttributeName,
                                req.getAttributeName()));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    private final KgmArchivesInfoService kgmArchivesInfoService;

    @PostMapping("/listKgmArchivesInfo")
    @ApiOperation(value = "列出重点人员群体")
    public R<?> listKgmArchivesInfo(@RequestBody Map<String,Object> req) {
        Page<KgmArchivesInfo> page = kgmArchivesInfoService.page(new Page<>((int) req.get("page"), (int) req.get("limit")));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }



}
