package com.hl.building.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjLyActionsConfig;
import com.hl.building.domain.WjLyWarnTaskConfig;
import com.hl.building.domain.vo.WjLyWarnTaskConfigPageReqVO;
import com.hl.building.util.SsoUtil;
import com.hl.common.domain.R;
import com.hl.building.service.WjLyActionsConfigService;
import com.hl.building.service.WjLyWarnTaskConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/wjLyWarnTaskConfig")
@RequiredArgsConstructor
@Api(tags = "风险预警任务")
public class WjLyWarnTaskConfigController {


    private final WjLyWarnTaskConfigService wjLyWarnTaskConfigService;


    private final WjLyActionsConfigService wjLyActionsConfigService;

    @PostMapping("/add")
    @ApiOperation(value = "新增风险预警任务")
    public R<Boolean> addWarnTaskConfig(@RequestBody WjLyWarnTaskConfig warnTaskConfig) {

        warnTaskConfig.setCreateUser(SsoUtil.getUserIdCard());
        warnTaskConfig.setUpdateUser(SsoUtil.getUserIdCard());
        warnTaskConfig.setCreateTime(new Date());
        warnTaskConfig.setUpdateTime(new Date());

        return R.ok(wjLyWarnTaskConfigService.save(warnTaskConfig));
    }


    @PostMapping("/delete")
    @ApiOperation(value = "删除风险预警任务")
    public R<Boolean> deleteWarnTaskConfig(@RequestBody WjLyWarnTaskConfig warnTaskConfig) {
        return R.ok(wjLyWarnTaskConfigService.removeById(warnTaskConfig.getId()));
    }

    @PostMapping("/list")
    @ApiOperation(value = "风险预警任务列表")
    public R<List<WjLyWarnTaskConfig>> listWarnTaskConfig(@RequestBody WjLyWarnTaskConfigPageReqVO wjLyWarnTaskConfigPageReqVO) {
        Page<WjLyWarnTaskConfig> page = wjLyWarnTaskConfigService.page(new Page<>(wjLyWarnTaskConfigPageReqVO.getPage(), wjLyWarnTaskConfigPageReqVO.getLimit()),
                Wrappers.<WjLyWarnTaskConfig>lambdaQuery()
                        .like(StringUtils.isNotBlank(wjLyWarnTaskConfigPageReqVO.getTaskName()),
                                WjLyWarnTaskConfig::getTaskName,
                                wjLyWarnTaskConfigPageReqVO.getTaskName()));

        List<WjLyWarnTaskConfig> records = page.getRecords();
        if (!records.isEmpty()) {
            List<String> actionId = records.stream()
                    .map(WjLyWarnTaskConfig::getActionsConfig)
                    .collect(Collectors.toList());
            List<WjLyActionsConfig> wjLyActionsConfigs = wjLyActionsConfigService.listByIds(actionId);
            records.forEach(wjLyWarnTaskConfig -> {
                wjLyActionsConfigs.forEach(wjLyActionsConfig -> {
                    if (wjLyActionsConfig.getId().equals(wjLyWarnTaskConfig.getActionsConfig())) {
                        wjLyWarnTaskConfig.setActionName(wjLyActionsConfig.getActionsName());
                    }
                });
            });
        }

        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改风险预警任务")
    public R<Boolean> updateWarnTaskConfig(@RequestBody WjLyWarnTaskConfig warnTaskConfig) {
        warnTaskConfig.setUpdateUser(SsoUtil.getUserIdCard());
        warnTaskConfig.setUpdateTime(new Date());
        return R.ok(wjLyWarnTaskConfigService.updateById(warnTaskConfig));
    }

}
