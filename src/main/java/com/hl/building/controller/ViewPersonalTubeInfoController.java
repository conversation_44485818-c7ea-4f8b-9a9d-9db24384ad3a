package com.hl.building.controller;

import cn.hutool.core.lang.tree.Tree;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.ViewPersonalTubeInfo;
import com.hl.building.domain.dto.ListPersonalTubeReq;
import com.hl.building.service.KmPersonalDictionaryService;
import com.hl.building.service.ViewPersonalTubeInfoService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/importantPeople")
@Api(tags = "重点人员")
@Slf4j
@RequiredArgsConstructor
public class ViewPersonalTubeInfoController {

    private final ViewPersonalTubeInfoService viewPersonalTubeInfoService;

    private final KmPersonalDictionaryService kmPersonalDictionaryService;

    @ApiOperation(value = "重点人员列表")
    @PostMapping("/list")
    public R<List<ViewPersonalTubeInfo>> list(@RequestBody ListPersonalTubeReq req) {
        Page<ViewPersonalTubeInfo> pageList = viewPersonalTubeInfoService.pageList(req);
        return R.ok(pageList.getRecords(), (int) pageList.getTotal());
    }


    @ApiOperation(value = "查询字典")
    @PostMapping("/dictTree")
    public R<?> dict(@RequestBody JSONObject param) {
       List<Tree<String>> list = kmPersonalDictionaryService.dictTree(param);
        return R.ok(list);
    }
}
