package com.hl.building.controller;

import com.hl.building.domain.dto.BuildCountDetailDTO;
import com.hl.building.domain.dto.JqCountDetailInfoDTO;
import com.hl.common.domain.R;
import com.hl.es.domain.ViewEsJqBz;
import com.hl.es.service.JqCountAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/esCount")
@RequiredArgsConstructor
@Slf4j
@Api(tags = "警情统计")
public class EsCountController {

    private final JqCountAnalysisService jqCountAnalysisService;


    @PostMapping("/countJqDetail")
    @ApiOperation("详情接口")
    public R<JqCountDetailInfoDTO> countDetail(@RequestBody BuildCountDetailDTO buildCountDetailDTO) {
        JqCountDetailInfoDTO result = jqCountAnalysisService.countJqDetail(buildCountDetailDTO);
        return R.ok(result);
    }


    @PostMapping("/pageJqList")
    @ApiOperation("警情列表")
    public R<List<ViewEsJqBz>> pageJqList(@RequestBody BuildCountDetailDTO buildCountDetailDTO) {
        EsPageInfo<ViewEsJqBz> viewEsJqBzEsPageInfo = jqCountAnalysisService.pageJqList(buildCountDetailDTO);

        return R.ok(viewEsJqBzEsPageInfo.getList(), (int) viewEsJqBzEsPageInfo.getTotal());
    }
}
