//package com.hl.building.controller;
//
//import cn.hutool.core.collection.ListUtil;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import com.hl.building.domain.request.*;
//import com.hl.building.domain.tables.BuildingInfo;
//import com.hl.building.domain.tables.CompanyInfo;
//import com.hl.building.domain.tables.HouseInfo;
//import com.hl.building.domain.tables.PersonInfo;
//import com.hl.building.domain.vo.*;
//import com.hl.building.mapper.BuildingMapper;
//import com.hl.building.mapper.CompanyMapper;
//import com.hl.building.mapper.HouseMapper;
//import com.hl.building.mapper.PersonMapper;
//import com.hl.building.services.HouseService;
//import com.hl.common.domain.R;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.ObjectUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import java.util.*;
//import java.util.stream.Collectors;
//
//@RequestMapping("/house")
//@RestController
//@Api(tags = "户室信息")
//@Slf4j
//public class HouseController {
//
//    @Resource
//    HouseMapper houseMapper;
//    @Resource
//    HouseService houseService;
//    @Resource
//    CompanyMapper companyMapper;
//    @Resource
//    BuildingMapper buildingMapper;
//    @Resource
//    PersonMapper personMapper;
//    @PostMapping("/add")
//    @ApiOperation(value = "添加户室")
//    public R add(@RequestBody AddOrUpdHouseReq req){
//        houseService.add(req);
//        return R.ok();
//    }
//
//
//    @PostMapping("/update")
//    @ApiOperation(value = "修改户室")
//    public R update(@RequestBody AddOrUpdHouseReq req){
//        houseService.update(req);
//        return R.ok();
//    }
//
////    @PostMapping("/detail")
////    @ApiOperation(value = "户室详情")
////    public R<HouseInfoVo> detail(@RequestBody HouseDetailReq req)  {
////        HouseInfoVo houseInfo =  houseService.detail(req);
////        if (houseInfo.getCompanyId() != 0 ){
////            CompanyInfo companyInfo = companyMapper.selectById(houseInfo.getCompanyId());
////            if (ObjectUtils.isNotEmpty(companyInfo))
////                houseInfo.setCompanyInfo(companyInfo);
////        }
////        return R.ok(houseInfo);
////    }
//
//    @PostMapping("/delete")
//    @ApiOperation(value = "逻辑删除户室")
//    public R delete(@RequestBody HouseDetailReq req)  {
//        houseMapper.deleteById(req.getId());
//        return R.ok();
//    }
//
//
//
//    @PostMapping("/distribution")
//    @ApiOperation(value = "根据楼栋ID获取所有户室数据")
//    public R<HouseDistributionVo> house_distribution(@RequestBody DistributionReq req) throws Exception {
//        HouseDistributionVo houseDistributionVo = new HouseDistributionVo();
//        BuildingInfo buildingInfo = buildingMapper.selectById(req.getBuildId());
//
//        //公司数量
//        Long count = companyMapper.selectCount(new LambdaQueryWrapper<CompanyInfo>().eq(CompanyInfo::getBuildId, buildingInfo.getBuildId()).eq(CompanyInfo::getIsLogout,0));
//        buildingInfo.setCompanyNum(Math.toIntExact(count));
//        //户室数量
//        count = houseMapper.selectCount(new LambdaQueryWrapper<HouseInfo>().eq(HouseInfo::getBuildId, buildingInfo.getBuildId()));
//        buildingInfo.setHouseNum(Math.toIntExact(count));
//
//        List<CompanyInfo> companyInfoList = companyMapper.selectList(new LambdaQueryWrapper<CompanyInfo>().eq(CompanyInfo::getBuildId, req.getBuildId()).eq(CompanyInfo::getIsLogout,0));
//        //法人数量
//        Set<Integer> corporateCount = new HashSet<>();
//        //企业高管数量
//        Set<Integer> executivesCount = new HashSet<>();
//        //财务人员数量
//        Set<Integer> financeCount = new HashSet<>();
//        //其他人员数量
//        Set<Integer> otherPersonCount = new HashSet<>();
//        if (!companyInfoList.isEmpty()){
//            for (CompanyInfo companyInfo : companyInfoList) {
//                if (ObjectUtils.isNotEmpty(companyInfo.getCorporate()) &&!companyInfo.getCorporate().isEmpty())
//                    corporateCount.addAll(companyInfo.getCorporate());
//                if (ObjectUtils.isNotEmpty(companyInfo.getExecutives()) &&!companyInfo.getExecutives().isEmpty())
//                    executivesCount.addAll(companyInfo.getExecutives());
//                if (ObjectUtils.isNotEmpty(companyInfo.getFinance()) &&!companyInfo.getFinance().isEmpty())
//                    financeCount.addAll(companyInfo.getFinance());
//                if (ObjectUtils.isNotEmpty(companyInfo.getOtherPerson()) &&!companyInfo.getOtherPerson().isEmpty())
//                    otherPersonCount.addAll(companyInfo.getOtherPerson());
//            }
//            buildingInfo.setCorporateNum(corporateCount.size());
//            buildingInfo.setExecutivesNum(executivesCount.size());
//            buildingInfo.setFinanceNum(financeCount.size());
//            buildingInfo.setOtherPersonNum(otherPersonCount.size());
//        }
//
//        //重点企业数量
//        count = companyMapper.selectCount(new LambdaQueryWrapper<CompanyInfo>().eq(CompanyInfo::getBuildId, buildingInfo.getBuildId()).eq(CompanyInfo::getIsFocus,1));
//        buildingInfo.setFocusCompanyNum(count);
//        //重点人员数量
//        ListPersonReq listPersonReq = new ListPersonReq();
//        listPersonReq.setIsFocus(1);
//        List<CompanyInfo> InfoList = companyMapper.selectList(new LambdaQueryWrapper<CompanyInfo>().eq(CompanyInfo::getBuildId, req.getBuildId()));
//        Set<Integer> ids = new HashSet<>();
//        if (!InfoList.isEmpty()){
//            for (CompanyInfo companyInfo : InfoList) {
//                if (ObjectUtils.isNotEmpty(companyInfo.getCorporate()) && !companyInfo.getCorporate().isEmpty()){
//                    for (Integer i : companyInfo.getCorporate()) {
//                        PersonInfo personInfo = personMapper.selectById(i);
//                        if (ObjectUtils.isNotEmpty(personInfo)){
//                            ids.add(Math.toIntExact(personInfo.getId()));
//                        }
//                    }
//                }
//                if (ObjectUtils.isNotEmpty(companyInfo.getFinance()) && !companyInfo.getFinance().isEmpty()){
//                    for (Integer i : companyInfo.getFinance()) {
//                        PersonInfo personInfo = personMapper.selectById(i);
//                        if (ObjectUtils.isNotEmpty(personInfo)){
//                            ids.add(Math.toIntExact(personInfo.getId()));
//                        }
//                    }
//                }
//                if (ObjectUtils.isNotEmpty(companyInfo.getExecutives()) &&!companyInfo.getExecutives().isEmpty()){
//                    for (Integer i : companyInfo.getExecutives()) {
//                        PersonInfo personInfo = personMapper.selectById(i);
//                        if (ObjectUtils.isNotEmpty(personInfo)){
//                            ids.add(Math.toIntExact(personInfo.getId()));
//                        }
//                    }
//                }
//                if (ObjectUtils.isNotEmpty(companyInfo.getOtherPerson()) &&!companyInfo.getOtherPerson().isEmpty()){
//                    for (Integer i : companyInfo.getOtherPerson()) {
//                        PersonInfo personInfo = personMapper.selectById(i);
//                        if (ObjectUtils.isNotEmpty(personInfo)){
//                            ids.add(Math.toIntExact(personInfo.getId()));
//                        }
//                    }
//                }
//            }
//        }
//        if (!ids.isEmpty())
//            buildingInfo.setFocusPersonNum(personMapper.selectVoList(listPersonReq,ids).size());
//
//
//        houseDistributionVo.setBase_info(buildingInfo);
//        List<HouseInfo> list = houseMapper.house_distribution(req);
//        //户室内的公司信息
//        List<CompanyNatureVo> companyInfo = companyMapper.selectCompanyList();
//        for (HouseInfo houseInfoVo : list) {
//            List<CompanyNatureVo> collect = companyInfo.stream().filter(l -> {
//                List<Integer> houseId = l.getHouseId();
//                if (ObjectUtils.isNotEmpty(houseId) && !houseId.isEmpty()) {
//                    for (Integer i : houseId) {
//                        if (i == houseInfoVo.getId())
//                            return true;
//                    }
//                }
//                return false;
//            }).collect(Collectors.toList());
//            houseInfoVo.setCompanyInfoList(collect);
//            PersonInfo personInfo = personMapper.selectById(houseInfoVo.getPerson());
//            if (ObjectUtils.isNotEmpty(personInfo))
//                houseInfoVo.setPersonInfo(personInfo);
//        }
//        houseDistributionVo.setRooms(list);
//        return R.ok(houseDistributionVo);
//    }
//
//
//    @PostMapping("/list")
//    @ApiOperation(value = "户室列表")
//    public R<List<ListHouseVo>> list(@RequestBody ListHouseReq req) throws Exception {
//        List<String> ids = new ArrayList<>();
//        if (StringUtils.isNotBlank(req.getId())){
//            ids = Arrays.asList(req.getId().split(","));
//        }
//        List<ListHouseVo> list = houseMapper.selectVoList(req,ids);
//        return R.ok(ListUtil.page(req.getPage() - 1, req.getLimit(), list), list.size());
//    }
//
//}
