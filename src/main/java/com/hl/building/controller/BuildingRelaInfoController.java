package com.hl.building.controller;

import com.alibaba.fastjson2.JSONObject;
import com.hl.building.service.BuildingRealInfoService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/relaInfo")
@Api(tags = "关联信息")
@RequiredArgsConstructor
public class BuildingRelaInfoController {


    private final BuildingRealInfoService buildingRealInfoService;



    @PostMapping("/policeData")
    @ApiOperation(value = "获取关联警情信息")
    public R<?> getPoliceData(@RequestBody JSONObject param) {
        JSONObject policeData = buildingRealInfoService.getPoliceData(param);
        return R.ok(policeData);
    }


    @PostMapping("/faceRecord")
    @ApiOperation(value = "获取关联人脸抓拍信息")
    public R<?> getFaceRecord(@RequestBody JSONObject param) {
        JSONObject faceRecord = buildingRealInfoService.getFaceRecord(param);
        return R.ok(faceRecord);
    }


    @ApiOperation(value = "查询人员涉警记录")
    @PostMapping("/personPoliceRecord")
    public R<?> getPersonPoliceRecord(@RequestBody JSONObject param) {
        JSONObject personPoliceRecord = buildingRealInfoService.getPersonPoliceRecord(param);
        return R.ok(personPoliceRecord);
    }

    @ApiOperation(value = "查询人员涉案记录")
    @PostMapping("/personCaseRecord")
    public R<?> getPersonCaseRecord(@RequestBody JSONObject param) {
        JSONObject personCaseRecord = buildingRealInfoService.getPersonCaseRecord(param);
        return R.ok(personCaseRecord);
    }
}
