package com.hl.building.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjLyCaseInfo;
import com.hl.building.domain.dto.WjLyCaseInfoPageReq;
import com.hl.building.service.WjLyCaseInfoService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "楼宇关联案件")
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/wjLyCaseInfo")
public class WjLyCaseInfoController {


    private final WjLyCaseInfoService wjLyCaseInfoService;


    @ApiOperation(value = "分页查询")
    @PostMapping("/page")
    public R<List<WjLyCaseInfo>> page(@RequestBody WjLyCaseInfoPageReq req){
        Page<WjLyCaseInfo> page = wjLyCaseInfoService.pageList(req);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }
}
