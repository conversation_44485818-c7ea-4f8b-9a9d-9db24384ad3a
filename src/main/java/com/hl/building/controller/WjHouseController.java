package com.hl.building.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjHouseInfo;
import com.hl.building.domain.dto.DistributionReq;
import com.hl.building.domain.dto.ListHouseReq;
import com.hl.building.domain.vo.WjBuildingDetailRespVO;
import com.hl.building.service.WjBuildingInfoService;
import com.hl.building.service.WjHouseInfoService;
import com.hl.building.util.SsoUtil;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/wjHouse")
@Api(tags = "户室")
@AllArgsConstructor
public class WjHouseController {

    private final WjHouseInfoService wjHouseInfoService;

    private final WjBuildingInfoService wjBuildingInfoService;

    @PostMapping("/add")
    @ApiOperation(value = "添加户室")
    public R<Boolean> add(@RequestBody WjHouseInfo houseInfo) {

        houseInfo.setCreateUser(SsoUtil.getUserIdCard());
        houseInfo.setCreateTime(new Date());
        boolean save = wjHouseInfoService.save(houseInfo);

        return R.ok(save);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改户室")
    public R<Boolean> update(@RequestBody WjHouseInfo houseInfo) {
        houseInfo.setUpdateUser(SsoUtil.getUserIdCard());
        houseInfo.setUpdateTime(new Date());
        boolean update = wjHouseInfoService.updateById(houseInfo);
        return R.ok(update);
    }


    @PostMapping("/distribution")
    @ApiOperation(value = "根据楼栋ID获取所有户室数据")
    public R<WjBuildingDetailRespVO> houseDistribution(@RequestBody DistributionReq req) {
        WjBuildingDetailRespVO detailRespVO = wjHouseInfoService.houseDistribution(req);
        return R.ok(detailRespVO);
    }


    @PostMapping("/list")
    @ApiOperation(value = "户室列表")
    public R<List<WjHouseInfo>> list(@RequestBody ListHouseReq req) {

        List<String> ids = new ArrayList<>();
        if (StringUtils.isNotBlank(req.getId())) {
            ids = Arrays.asList(req.getId().split(","));
        }

        Page<WjHouseInfo> page = wjHouseInfoService.page(new Page<>(req.getPage(), req.getLimit()), Wrappers.<WjHouseInfo>lambdaQuery()
                .eq(StringUtils.isNotBlank(req.getBuildId()), WjHouseInfo::getBuildId, req.getBuildId())
                .like(StringUtils.isNotBlank(req.getAddress()), WjHouseInfo::getAddress, req.getAddress())
                .like(StringUtils.isNotBlank(req.getDzId()), WjHouseInfo::getDzId, req.getDzId())
                .in(!ids.isEmpty(), WjHouseInfo::getHouseId, ids)
                .and(StringUtils.isNotBlank(req.getQuery()), wrapper -> wrapper.like(WjHouseInfo::getHouseName, req.getQuery())
                        .or().like(WjHouseInfo::getAddress, req.getQuery())));
        return R.ok(page.getRecords(), (int) page.getTotal());
    }


    @PostMapping("/delete")
    @ApiOperation(value = "删除")
    public R<Boolean> delete(@RequestBody WjHouseInfo houseInfo) {
        boolean remove = wjHouseInfoService.removeById(houseInfo);
        return R.ok(remove);
    }
}
