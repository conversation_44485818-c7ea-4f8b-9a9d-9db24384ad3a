package com.hl.building.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjBuildingInfo;
import com.hl.building.domain.dto.AddOrUpdBuildReq;
import com.hl.building.domain.dto.ListBuildReq;
import com.hl.building.domain.vo.WjBuildingInfoRespVO;
import com.hl.building.service.WjBuildingInfoService;
import com.hl.building.util.SsoUtil;
import com.hl.common.domain.R;
import com.hl.es.domain.dto.JqCountDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/wjBuilding")
@Api(tags = "楼宇相关")
@RequiredArgsConstructor
public class WjBuildingController {


    private final WjBuildingInfoService wjBuildingInfoService;


    @PostMapping("/add")
    @ApiOperation("新增楼宇")
    public R<Boolean> add(@RequestBody AddOrUpdBuildReq req) {

        WjBuildingInfo wjBuildingInfo = new WjBuildingInfo();
        wjBuildingInfo.setBuildName(req.getBuildName());
        wjBuildingInfo.setAddress(req.getAddress());
        wjBuildingInfo.setLayerNum(req.getLayerNum());
        wjBuildingInfo.setGridX(req.getGridX());
        wjBuildingInfo.setGridY(req.getGridY());
        wjBuildingInfo.setCreateTime(new Date());
        wjBuildingInfo.setCreateUser(SsoUtil.getUserIdCard());
        boolean flag = wjBuildingInfoService.save(wjBuildingInfo);
        return R.ok(true);
    }

    @PostMapping("/update")
    @ApiOperation("修改楼宇")
    public R<Boolean> update(@RequestBody AddOrUpdBuildReq req) {
        if (StrUtil.isBlank(req.getBuildId())) {
            return R.fail("缺少id");
        }

        WjBuildingInfo buildingInfo = wjBuildingInfoService.getById(req.getBuildId());
        if (ObjectUtil.isNull(buildingInfo)) {
            return R.fail("楼宇不存在");
        }
        buildingInfo.setBuildName(req.getBuildName());
        buildingInfo.setAddress(req.getAddress());
        buildingInfo.setUpdateTime(new Date());
        buildingInfo.setUpdateUser(SsoUtil.getUserIdCard());
        boolean flag = wjBuildingInfoService.updateById(buildingInfo);
        return R.ok(flag);
    }


    @PostMapping("/delete")
    @ApiOperation("删除楼宇")
    public R<Boolean> delete(@RequestBody WjBuildingInfo req) {
        return R.ok(wjBuildingInfoService.removeById(req.getBuildId()));
    }


    @PostMapping("/list")
    @ApiOperation("楼宇列表")
    public R<List<WjBuildingInfoRespVO>> list(@RequestBody ListBuildReq req) {
        Page<WjBuildingInfoRespVO> page = wjBuildingInfoService.selectBuildingList(req);
        log.warn("buildingInfo:{}", page.getRecords());
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/pageCount")
    @ApiOperation("楼宇统计")
    public R<List<WjBuildingInfo>> pageCount(@RequestBody ListBuildReq req) {
        Page<WjBuildingInfo> page = wjBuildingInfoService.page(Page.of(req.getPage(), req.getLimit()),
                Wrappers.<WjBuildingInfo>lambdaQuery()
                        .like(StrUtil.isNotBlank(req.getQuery()), WjBuildingInfo::getBuildName, req.getQuery())
                        .isNotNull(WjBuildingInfo::getJqCount));
        List<WjBuildingInfo> records = page.getRecords();
        for (WjBuildingInfo record : records) {
            JqCountDTO jqCount = record.getJqCount();
            List<JqCountDTO.MainType> mainType = jqCount.getMainType();
            if (mainType != null && !mainType.isEmpty()) {
                mainType.sort((o1, o2) -> o2.getCount() - o1.getCount());
                jqCount.setMainType(mainType.subList(0, Math.min(mainType.size(), 5)));
            }
        }
        return R.ok(page.getRecords(), (int) page.getTotal());
    }





}
