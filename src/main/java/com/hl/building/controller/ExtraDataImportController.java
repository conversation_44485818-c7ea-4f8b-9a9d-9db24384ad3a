package com.hl.building.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjExcelImportConfig;
import com.hl.building.domain.dto.WjExcelDataQueryReq;
import com.hl.building.domain.dto.WjExcelImportConfigQueryRequest;
import com.hl.building.service.WjExcelDataService;
import com.hl.building.service.WjExcelImportConfigService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/extraData")
@Slf4j
@RequiredArgsConstructor
@Api(tags = "外部数据")
public class ExtraDataImportController {

    private final WjExcelDataService excelDataService;
    private final WjExcelImportConfigService excelImportConfigService;

    @PostMapping("/import")
    @ApiOperation(value = "上传文件并自动导入")
    public R<?> uploadAndAutoImport(@RequestParam("file") MultipartFile file,
                                    @RequestParam("name") String name,
                                    @RequestParam(value = "remark", required = false) String remark) throws IOException {
        Long configId = excelDataService.autoCreateConfigAndImport(file, name, remark);
        return R.ok(configId);
    }

    @PostMapping("/listConfig")
    @ApiOperation(value = "查询导入配置")
    public R<List<WjExcelImportConfig>> listConfig(@RequestBody WjExcelImportConfigQueryRequest request) {
        Page<WjExcelImportConfig> wjExcelImportConfigs = excelImportConfigService.listConfig(request);
        return R.ok(wjExcelImportConfigs.getRecords(), (int) wjExcelImportConfigs.getTotal());
    }

    @PostMapping("/queryData")
    @ApiOperation(value = "查询导入数据")
    public R<?> queryData(@RequestBody WjExcelDataQueryReq request) {
        JSONObject data = excelDataService.queryData(request);
        return R.ok(data);
    }
}
