package com.hl.building.controller;

import com.hl.building.domain.dto.PersonArchiveQueryDTO;
import com.hl.building.domain.dto.PersonArchiveReturnDTO;
import com.hl.building.service.WjPersonArchiveService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/personArchive")
@Slf4j
@Api(tags = "人员档案")
@RequiredArgsConstructor
public class WjPersonArchiveController {

    private final WjPersonArchiveService wjPersonArchiveService;

    @PostMapping("/queryPerson")
    @ApiOperation("查询人员档案")
    public R<PersonArchiveReturnDTO> queryPerson(@RequestBody PersonArchiveQueryDTO queryDTO){

        PersonArchiveReturnDTO personArchiveReturnDTO = wjPersonArchiveService.queryPerson(queryDTO);

        return R.ok(personArchiveReturnDTO);
    }
}
