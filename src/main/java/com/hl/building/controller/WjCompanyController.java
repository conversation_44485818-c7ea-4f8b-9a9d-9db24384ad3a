package com.hl.building.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjCompanyInfo;
import com.hl.building.domain.dto.AddOrUpdCompanyReq;
import com.hl.building.domain.dto.ListCompanyReq;
import com.hl.building.domain.vo.WjCompanyRespVO;
import com.hl.building.service.WjCompanyInfoService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/wjCompany")
@Api(tags = "企业")
@RequiredArgsConstructor
public class WjCompanyController {

    private final WjCompanyInfoService wjCompanyInfoService;

    @PostMapping("/delete")
    @ApiOperation(value = "删除企业")
    public R<Boolean> delete(@RequestBody WjCompanyInfo companyInfo) {
        boolean b = wjCompanyInfoService.removeById(companyInfo.getCompanyId());
        return R.ok(b);
    }


    @PostMapping("/list")
    @ApiOperation(value = "企业列表")
    public R<List<WjCompanyRespVO>> list(@RequestBody ListCompanyReq req) {
        Page<WjCompanyRespVO> page = wjCompanyInfoService.pageCompanyList(req);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改企业")
    public R<Boolean> update(@RequestBody WjCompanyInfo companyInfo) {
        boolean b = wjCompanyInfoService.updateById(companyInfo);
        return R.ok(b);
    }


    @PostMapping("/add")
    @ApiOperation(value = "新增企业")
    public R<Boolean> add(@RequestBody AddOrUpdCompanyReq req) {
        boolean b = wjCompanyInfoService.addCompany(req);
        return R.ok(b);
    }

    @PostMapping("/distribution")
    @ApiOperation(value = "分布")
    public R<List<WjCompanyRespVO>> distribution(@RequestBody Map<String, Object> req) {
        List<WjCompanyRespVO> list = wjCompanyInfoService.distribution(req);
        return R.ok(list);
    }



}
