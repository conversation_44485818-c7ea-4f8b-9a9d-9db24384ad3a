package com.hl.building.controller;

import cn.hutool.core.collection.ListUtil;
import com.hl.building.domain.dto.*;
import com.hl.building.domain.tables.CompanyInfo;
import com.hl.building.domain.vo.CompanyDetailVo;
import com.hl.building.domain.vo.CompanyDistributionVo;
import com.hl.building.domain.vo.CompanyInfoVo;
import com.hl.building.domain.vo.CountNatureVo;
import com.hl.building.mapper.CompanyMapper;
import com.hl.building.mapper.HouseMapper;
import com.hl.building.services.CompanyService;
import com.hl.building.services.HouseService;
import com.hl.common.domain.R;
import com.hl.common.utils.BeanUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

@RequestMapping("/company")
@RestController
@Api(tags = "公司信息")
@Slf4j
public class CompanyController {
    @Resource
    HouseMapper houseMapper;
    @Resource
    HouseService houseService;
    @Resource
    CompanyMapper companyMapper;
    @Resource
    CompanyService companyService;


    @PostMapping("/add")
    @ApiOperation(value = "添加公司")
    public R add(@RequestBody AddOrUpdCompanyReq req) throws Exception {
        CompanyInfo companyInfo = new CompanyInfo();
        BeanUtils.copyProperties(req, companyInfo);
        companyMapper.insert(companyInfo);
        return R.ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改公司")
    public R update(@RequestBody AddOrUpdCompanyReq req){
        companyService.update(req);
        return R.ok();
    }

    @PostMapping("/list")
    @ApiOperation(value = "公司列表")
    public R<List<CompanyInfoVo>> list(@RequestBody ListCompanyReq req) throws Exception {
        List<CompanyInfoVo> list = companyService.list(req);
        return R.ok(ListUtil.page(req.getPage() - 1, req.getLimit(), list), list.size());
    }

    @PostMapping("/delete")
    @ApiOperation(value = "逻辑删除公司")
    public R delete(@RequestBody CompanyDetailReq req)  {
        companyMapper.deleteById(req.getCompanyId());
        return R.ok();
    }
    @PostMapping("/distribution")
    @ApiOperation(value = "根据楼栋ID获取所有公司信息")
    public R<List<CompanyDistributionVo>> company_distribution(@RequestBody DistributionReq req) throws Exception {
        List<CompanyDistributionVo> list = companyService.company_distribution(req);
        return R.ok(list);
    }

    @PostMapping("/count_nature")
    @ApiOperation(value = "房屋性质统计")
    public R<List<CountNatureVo>> count_nature(@RequestBody BuildDetailReq req)  {
        List<CountNatureVo>  countNatureVos = companyService.count_nature(req);
        return R.ok(countNatureVos);
    }

    @PostMapping("/detail")
    @ApiOperation(value = "公司详情")
    public R<CompanyDetailVo> detail(@RequestBody CompanyDetailReq req)  {
        CompanyDetailVo companyDetailVo = companyService.detail(req);
        return R.ok(companyDetailVo);
    }

    @PostMapping("/history_company")
    @ApiOperation(value = "历史公司")
    public R<List<CompanyInfo>> history_company(@RequestBody HouseDetailReq req)  {
        List<CompanyInfo> companyInfoList = companyService.history_company(req);
        return R.ok(companyInfoList,companyInfoList.size());
    }




}
