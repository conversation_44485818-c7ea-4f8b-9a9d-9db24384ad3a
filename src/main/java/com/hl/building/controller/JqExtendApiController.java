package com.hl.building.controller;

import com.alibaba.fastjson2.JSONObject;
import com.hl.building.client.JqFeignClient;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "警情外部接口")
@RequestMapping("/extend/jq")
@RestController
@RequiredArgsConstructor
public class JqExtendApiController {

    private final JqFeignClient jqFeignClient;

    @Value("${spring.security.sso.projectToken}")
    private String token;

    @PostMapping("/cjlb")
    @ApiOperation("获取处警类别")
    public R<?> getJqCjlb() {
        JSONObject param = new JSONObject();
        param.put("type", "510");
        param.put("noLeave",false);

        R<?> dictTree = jqFeignClient.getDictTree(param, token);

        return dictTree;
    }

}
