package com.hl.building.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.dto.WjBuildingCameraReq;
import com.hl.building.domain.dto.WjFaceCameraInfoPageReq;
import com.hl.building.domain.dto.WjFaceCameraPageReq;
import com.hl.building.domain.vo.WjBuildingCameraRespVO;
import com.hl.common.domain.R;
import com.hl.face.domain.WjFaceCameraInfo;
import com.hl.face.service.WjFaceCameraInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/faceCameraInfo")
@Api(tags = "设备关联")
@RequiredArgsConstructor
public class WjFaceCameraController {

    private final WjFaceCameraInfoService wjFaceCameraInfoService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询")
    public R<List<WjBuildingCameraRespVO>> page(@RequestBody WjFaceCameraInfoPageReq req) {
        Page<WjBuildingCameraRespVO> page = wjFaceCameraInfoService.pageBuildingCameraInfo(req);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/updateDeviceRelation")
    @ApiOperation(value = "更新设备关联")
    public R<Boolean> updateDeviceRelation(@RequestBody WjBuildingCameraReq req) {
        return R.ok(wjFaceCameraInfoService.updateDeviceRelation(req));
    }

    @PostMapping("/cameraList")
    @ApiOperation(value = "获取设备列表")
    public R<List<WjFaceCameraInfo>> cameraList(@RequestBody WjFaceCameraPageReq req) {
        Page<WjFaceCameraInfo> page = wjFaceCameraInfoService.pageCameraList(req);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }
}
