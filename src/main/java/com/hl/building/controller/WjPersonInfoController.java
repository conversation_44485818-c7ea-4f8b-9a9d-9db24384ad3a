package com.hl.building.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.vo.WjPersonInfoPageReqVO;
import com.hl.building.domain.vo.WjPersonInfoRespVO;
import com.hl.building.service.WjPersonInfoService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@RestController
@RequestMapping("/wjPersonInfo")
@RequiredArgsConstructor
@Api(tags = "人员档案")
public class WjPersonInfoController {

    private final WjPersonInfoService wjPersonInfoService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<WjPersonInfoRespVO>> page(@RequestBody WjPersonInfoPageReqVO req) {
        Page<WjPersonInfoRespVO> list = wjPersonInfoService.pageList(req);
        return R.ok(list.getRecords(), (int) list.getTotal());
    }
}
