package com.hl.building.config;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * 翻译警情时间
 */
public class TimeStringSerializer extends JsonSerializer<String> {
    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        try {
            DateTime parse = DateUtil.parse(value);
            gen.writeString(parse.toString());

        }catch (Exception e){
            gen.writeString(value);
        }
    }
}
