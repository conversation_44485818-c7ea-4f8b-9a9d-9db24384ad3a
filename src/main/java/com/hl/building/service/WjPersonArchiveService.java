package com.hl.building.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.building.domain.CzJzCaseInfo;
import com.hl.building.domain.ViewCameraFaceRecord;
import com.hl.building.domain.ViewEsJqAll;
import com.hl.building.domain.WjscSyrk;
import com.hl.building.domain.dto.PersonArchiveQueryDTO;
import com.hl.building.domain.dto.PersonArchiveReturnDTO;
import com.hl.building.mapper.CzJzCaseInfoMapper;
import com.hl.building.mapper.ViewCameraFaceRecordMapper;
import com.hl.building.mapper.ViewEsJqAllMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class WjPersonArchiveService {

    private final WjscSyrkService wjscSyrkService;

    private final CzJzCaseInfoMapper czJzCaseInfoMapper;

    private final ViewCameraFaceRecordMapper viewCameraFaceRecordMapper;

    private final ViewEsJqAllMapper viewEsJqAllMapper;

    public PersonArchiveReturnDTO queryPerson(PersonArchiveQueryDTO queryDTO) {
        PersonArchiveReturnDTO returnDTO = new PersonArchiveReturnDTO();
        String idCard = queryDTO.getIdCard();

        // 获取人员
        WjscSyrk one = wjscSyrkService.getOne(Wrappers.<WjscSyrk>lambdaQuery()
                .eq(WjscSyrk::getGmsfhm, idCard)
                .last("limit 1"));
        returnDTO.setPersonBaseInfo(one);

        // 获取涉案信息
        List<CzJzCaseInfo> czJzCaseInfos = czJzCaseInfoMapper.selectCzJzCaseInfoList(idCard);
        returnDTO.setCaseInfoList(czJzCaseInfos);

        // 获取楼宇进出
        List<ViewCameraFaceRecord> viewCameraFaceRecords = viewCameraFaceRecordMapper.selectList(Wrappers.<ViewCameraFaceRecord>lambdaQuery()
                .eq(ViewCameraFaceRecord::getIdCard, idCard));
        returnDTO.setFaceRecordList(viewCameraFaceRecords);

        // 获取警情信息
        List<ViewEsJqAll> jqList = viewEsJqAllMapper.selectJqList(idCard);
        returnDTO.setJqList(jqList);
        return returnDTO;
    }
}
