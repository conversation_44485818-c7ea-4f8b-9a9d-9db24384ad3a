package com.hl.building.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.dto.ListBuildReq;
import com.hl.building.domain.vo.WjBuildingInfoRespVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.mapper.WjBuildingInfoMapper;
import com.hl.building.domain.WjBuildingInfo;
import com.hl.building.service.WjBuildingInfoService;
@Service
@RequiredArgsConstructor
public class WjBuildingInfoServiceImpl extends ServiceImpl<WjBuildingInfoMapper, WjBuildingInfo> implements WjBuildingInfoService{

    private final WjBuildingInfoMapper wjBuildingInfoMapper;

    @Override
    public Page<WjBuildingInfoRespVO> selectBuildingList(ListBuildReq req) {

        //  分页查询列表

        Page<WjBuildingInfoRespVO> pageList = wjBuildingInfoMapper.selectBuildingList(new Page<>(req.getPage(), req.getLimit()), req);

        return pageList ;
    }
}
