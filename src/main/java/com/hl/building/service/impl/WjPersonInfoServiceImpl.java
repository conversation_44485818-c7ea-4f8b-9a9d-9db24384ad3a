package com.hl.building.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.domain.WjPersonInfo;
import com.hl.building.domain.vo.WjPersonInfoPageReqVO;
import com.hl.building.domain.vo.WjPersonInfoRespVO;
import com.hl.building.mapper.WjPersonInfoMapper;
import com.hl.building.service.WjPersonInfoService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class WjPersonInfoServiceImpl extends ServiceImpl<WjPersonInfoMapper, WjPersonInfo>
        implements WjPersonInfoService {
    private final WjPersonInfoMapper wjPersonInfoMapper;
//    @Override
//    public Page<WjPersonInfoRespVO> pageList(WjPersonInfoPageReqVO req) {
//
//        Page<WjPersonInfo> page = page(Page.of(req.getPage(), req.getLimit()), Wrappers.<WjPersonInfo>lambdaQuery()
//                .like(StringUtils.isNotBlank(req.getName()), WjPersonInfo::getName, req.getName())
//                .like(StringUtils.isNotBlank(req.getIdCard()), WjPersonInfo::getIdCard, req.getIdCard()));
//
//        List<WjPersonInfoRespVO> list = new ArrayList<>();
//        if (!page.getRecords().isEmpty()) {
//            for (WjPersonInfo records : page.getRecords()) {
//                WjPersonInfoRespVO respVO = new WjPersonInfoRespVO();
//                BeanUtils.copyProperties(records, respVO);
//                list.add(respVO);
//            }
//        }
//        Page<WjPersonInfoRespVO> objectPage = new Page<>(page.getTotal(), page.getSize(), page.getCurrent());
//
//        objectPage.setRecords(list);
//        objectPage.setTotal(page.getTotal());
//
//        return objectPage;
//
//    }

    @Override
    public Page<WjPersonInfoRespVO> pageList(WjPersonInfoPageReqVO req) {
        Page<WjPersonInfoRespVO> page = wjPersonInfoMapper.selectPersonList(new Page<>(req.getPage(), req.getLimit()), req);
        List<WjPersonInfoRespVO> list = new ArrayList<>();
        if (!page.getRecords().isEmpty()) {
            for (WjPersonInfo records : page.getRecords()) {
                WjPersonInfoRespVO respVO = new WjPersonInfoRespVO();
                BeanUtils.copyProperties(records, respVO);
                list.add(respVO);
            }
        }
        Page<WjPersonInfoRespVO> objectPage = new Page<>(page.getTotal(), page.getSize(), page.getCurrent());

        objectPage.setRecords(list);
        objectPage.setTotal(page.getTotal());

        return objectPage;

    }
}
