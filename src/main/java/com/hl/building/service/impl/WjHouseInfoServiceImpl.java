package com.hl.building.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.building.domain.WjBuildingInfo;
import com.hl.building.domain.dto.DistributionReq;
import com.hl.building.domain.vo.WjBuildingDetailRespVO;
import com.hl.building.domain.vo.WjCompanyHouseVO;
import com.hl.building.mapper.WjBuildingInfoMapper;
import com.hl.building.mapper.WjCompanyHouseMapper;
import com.hl.building.mapper.WjCompanyInfoMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.domain.WjHouseInfo;
import com.hl.building.mapper.WjHouseInfoMapper;
import com.hl.building.service.WjHouseInfoService;

@Service
@RequiredArgsConstructor
public class WjHouseInfoServiceImpl extends ServiceImpl<WjHouseInfoMapper, WjHouseInfo> implements WjHouseInfoService {


    private final WjCompanyHouseMapper wjCompanyHouseMapper;

    private final WjBuildingInfoMapper wjBuildingInfoMapper;

    private final WjCompanyInfoMapper wjCompanyInfoMapper;


    /**
     * @param req 请求
     * @return 返回楼宇详情响应对象
     */
    @Override
    public WjBuildingDetailRespVO houseDistribution(DistributionReq req) {
        // 查询指定楼宇下的所有房屋信息
        List<WjHouseInfo> houseInfos = this.list(Wrappers.<WjHouseInfo>lambdaQuery()
                .eq(WjHouseInfo::getBuildId, req.getBuildId()));

        // 提取所有房屋的ID
        List<String> houseIdList = houseInfos.stream().map(WjHouseInfo::getHouseId).collect(Collectors.toList());

        // 根据房屋ID列表查询相关公司房屋信息
        List<WjCompanyHouseVO> wjCompanyHouseVOS = wjCompanyHouseMapper.selectCompanyHouseList(houseIdList);

        // 将公司房屋信息按房屋ID分组
        Map<String, List<WjCompanyHouseVO>> collect = wjCompanyHouseVOS.stream()
                .collect(Collectors.groupingBy(WjCompanyHouseVO::getHouseId));
        // 为每个房屋信息添加对应的公司列表
        for (WjHouseInfo houseInfo : houseInfos) {
            String id = houseInfo.getHouseId();
            if (collect.containsKey(id)) {
                houseInfo.setCompanyList(collect.get(id));
            }
        }

        // 创建楼宇详情响应对象
        WjBuildingDetailRespVO wjBuildingDetailRespVO = new WjBuildingDetailRespVO();
        wjBuildingDetailRespVO.setHouseList(houseInfos);

        // 查询并设置楼宇信息
        WjBuildingInfo buildingInfo = wjBuildingInfoMapper.selectById(req.getBuildId());
        // 统计楼宇房屋性质
        buildingInfo.setHouseNature(countHouseNature(req));

        // 统计企业相关信息
        buildingInfo.setStatistics(countCompanyInfo(req, houseInfos.size()));

        wjBuildingDetailRespVO.setBuildingInfo(buildingInfo);

        // 返回楼宇详情响应对象
        return wjBuildingDetailRespVO;
    }

    private List<JSONObject> countHouseNature(DistributionReq req) {
        // 统计
        List<JSONObject> countHouseNature = wjCompanyHouseMapper.countHouseNature(req.getBuildId());
        Map<String, Integer> houseNatureMap = countHouseNature.stream().collect(Collectors.toMap(k -> k.getString("house_nature"), v -> v.getInteger("count")));
        List<JSONObject> houseNatureCountList = new ArrayList<>();
        houseNatureCountList.add(new JSONObject()
                .fluentPut("name", "空闲")
                .fluentPut("value", houseNatureMap.getOrDefault("空闲", 0)));
        houseNatureCountList.add(new JSONObject()
                .fluentPut("name", "自住")
                .fluentPut("value", houseNatureMap.getOrDefault("自住", 0)));
        houseNatureCountList.add(new JSONObject()
                .fluentPut("name", "出租")
                .fluentPut("value", houseNatureMap.getOrDefault("出租", 0)));


        return houseNatureCountList;
    }


    private List<JSONObject> countCompanyInfo(DistributionReq req,
                                              Integer houseNum) {

        JSONObject countCompanyInfo = wjCompanyInfoMapper.countCompanyInfo(req.getBuildId());
        List<JSONObject> companyInfo = new ArrayList<>();

        companyInfo.add(new JSONObject()
                .fluentPut("name", "户室数量")
                .fluentPut("value", houseNum));

        companyInfo.add(new JSONObject()
                .fluentPut("name", "企业个数")
                .fluentPut("value", countCompanyInfo.getInteger("company_num") == null ? 0 : countCompanyInfo.getInteger("company_num")));

        companyInfo.add(new JSONObject()
                .fluentPut("name", "重点企业")
                .fluentPut("value", countCompanyInfo.getInteger("focus_num") == null ? 0 : countCompanyInfo.getInteger("focus_num")));

        companyInfo.add(new JSONObject()
                .fluentPut("name", "已注销企业")
                .fluentPut("value", countCompanyInfo.getInteger("logout_num") == null ? 0 : countCompanyInfo.getInteger("logout_num")));

        return companyInfo;
    }
}
