package com.hl.building.service.impl;

import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.analysis.util.SqlLiteUtil;
import com.hl.building.domain.WjCompanyInfo;
import com.hl.building.domain.dto.AddOrUpdCompanyReq;
import com.hl.building.domain.dto.ListCompanyReq;
import com.hl.building.domain.vo.WjCompanyHouseRespVO;
import com.hl.building.domain.vo.WjCompanyRespVO;
import com.hl.building.mapper.*;
import com.hl.building.service.WjCompanyInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WjCompanyInfoServiceImpl extends ServiceImpl<WjCompanyInfoMapper, WjCompanyInfo> implements WjCompanyInfoService {

    private final WjCompanyInfoMapper wjCompanyInfoMapper;

    private final WjCompanyHouseMapper wjCompanyHouseMapper;

    private final WjPersonInfoMapper wjPersonInfoMapper;

    private final WjLyJqxxMapper wjLyJqxxMapper;

    @Override
    public Page<WjCompanyRespVO> pageCompanyList(ListCompanyReq req) {

        Page<WjCompanyRespVO> page = wjCompanyInfoMapper.pageCompanyList(new Page<>(req.getPage(), req.getLimit()), req);

        List<WjCompanyRespVO> records = page.getRecords();
        getCompanyBaseInfo(records);
        return page;
    }


    private void getCompanyBaseInfo(List<WjCompanyRespVO> records) {
        List<String> companyIdList = records.stream()
                .map(WjCompanyRespVO::getCompanyId).collect(Collectors.toList());
        List<String> jgbhList = records.stream().map(WjCompanyInfo::getJgbh).collect(Collectors.toList());
        if (companyIdList.isEmpty()) {
            return;
        }

//        // 公司人员信息
//        Map<String, List<WjCompanyPersonRespVO>> companyPersonMap = getCompanyPersonMap(companyIdList);

        // 公司所在房屋信息
        Map<String, List<WjCompanyHouseRespVO>> houseMap = getCompanyHouseMap(companyIdList);
//        Map<String, Integer> personCount = getPersonCount(companyIdList);
//        Map<String, Integer> jqCount = getJqCount(jgbhList);
//        Map<String, Integer> caseCount = getCaseCount(companyIdList);

        for (WjCompanyRespVO record : records) {

//            record.setPeopleCount(personCount.getOrDefault(record.getCompanyId(), 0));
//            record.setJqCount(jqCount.getOrDefault(record.getCompanyId(), 0));
//            record.setCaseCount(caseCount.getOrDefault(record.getCompanyId(), 0));

            if (houseMap.containsKey(record.getCompanyId())) {
                record.setHouseList(houseMap.get(record.getCompanyId()));
            }
        }
    }

//    private Map<String, Integer> getCaseCount(List<String> companyIdList) {
//
////        TODO: 统计案件数量
//        return new HashMap<>();
//    }
//
//    private Map<String, Integer> getJqCount(List<String> companyIdList) {
//        if (companyIdList == null || companyIdList.isEmpty()) {
//            return new HashMap<>();
//        }
//
//        List<JSONObject> jqCount = wjLyJqxxMapper.countJqCount(companyIdList);
//        if (jqCount.isEmpty()) {
//            return new HashMap<>();
//        }
//        Map<String, Integer> collect = jqCount.stream().collect(Collectors.toMap(jsonObject -> jsonObject.getString("jgbh"),
//                jsonObject -> jsonObject.getInteger("count")));
//        return collect;
//
//    }

    private Map<String, List<WjCompanyHouseRespVO>> getCompanyHouseMap(List<String> companyIdList) {
        if (companyIdList.isEmpty()) {
            return new HashMap<>();
        }
        List<WjCompanyHouseRespVO> companyHouseRespVOS = wjCompanyHouseMapper.selectCompanyHouseInfoList(companyIdList);
        if (companyHouseRespVOS.isEmpty()) {
            return new HashMap<>();
        }
        return new HashMap<>();
//        return companyHouseRespVOS.stream().collect(Collectors.groupingBy(WjCompanyHouseRespVO::getCompanyId));
    }


//    private Map<String, Integer> getPersonCount(List<String> companyIdList) {
//        if (companyIdList.isEmpty()) {
//            return new HashMap<>();
//        }
//        List<JSONObject> personCount = wjPersonInfoMapper.countPersonCountByCompanyId(companyIdList);
//        if (personCount.isEmpty()) {
//            return new HashMap<>();
//        }
//
//        return personCount.stream().collect(Collectors.toMap(jsonObject -> jsonObject.getString("companyId"),
//                jsonObject -> jsonObject.getInteger("count")));
//    }


//    private Map<String, List<WjCompanyPersonRespVO>> getCompanyPersonMap(List<String> companyIdList) {
//        if (companyIdList.isEmpty()) {
//            return new HashMap<>();
//        }
//        List<WjCompanyPersonRespVO> companyPersonList = wjCompanyPersonMapper.selectCompanyPersonList(companyIdList);
//        return companyPersonList.stream().collect(Collectors.groupingBy(WjCompanyPersonRespVO::getCompanyId));
//    }


    @Override
    public boolean addCompany(AddOrUpdCompanyReq req) {
        // 新增企业
        return false;
    }


    @Override
    public List<WjCompanyRespVO> distribution(Map<String, Object> req) {
        List<WjCompanyRespVO> records = this.baseMapper.selectCompanyListByBuildId(req);
        getCompanyBaseInfo(records);
        return records;
    }


//    @EventListener(ApplicationReadyEvent.class)
    public void refreshCompany() throws SQLException {
        DataSource dataSource = SqlLiteUtil.getDataSource("conf/sql-data/zww_data.db");
        List<Entity> query = DbUtil.use(dataSource).query("select * from wj_corporate_base_info");

        for (Entity entity : query) {
            String tyshxydm = entity.getStr("tyshxydm");
            WjCompanyInfo wjCompanyInfo = new WjCompanyInfo();
            wjCompanyInfo.setCompanyName(entity.getStr("jgmc"));
            wjCompanyInfo.setCompanyAddress(entity.getStr("zcdz"));
            wjCompanyInfo.setZczb(entity.getStr("reg_capi"));
            wjCompanyInfo.setZcrq(entity.getStr("start_date"));
            wjCompanyInfo.setYxqx(entity.getStr("fare_term_end"));
            wjCompanyInfo.setJyfw(entity.getStr("fare_scope"));
            wjCompanyInfo.setZt(entity.getStr("corp_status"));
            wjCompanyInfo.setCompanyCode(tyshxydm);

            WjCompanyInfo one = this.getOne(Wrappers.<WjCompanyInfo>lambdaQuery()
                    .eq(WjCompanyInfo::getCompanyCode, tyshxydm)
                    .last(" limit 1"));
            if (one != null) {
                System.out.println("存在");
                wjCompanyInfo.setCompanyId(one.getCompanyId());
            }
            this.saveOrUpdate(wjCompanyInfo);
        }
    }



}
