package com.hl.building.service.impl;

import com.hl.building.domain.vo.WjLyPersonAttrScoreAddReqVO;
import com.hl.building.util.SsoUtil;
import org.springframework.stereotype.Service;

import java.util.Date;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.mapper.WjLyAttributeScoreMapper;
import com.hl.building.domain.WjLyAttributeScore;
import com.hl.building.service.WjLyPersonAttrScoreService;

@Service
public class WjLyAttributeScoreServiceImpl extends ServiceImpl<WjLyAttributeScoreMapper, WjLyAttributeScore> implements WjLyPersonAttrScoreService {

    @Override
    public Boolean addAttrScore(WjLyPersonAttrScoreAddReqVO req) {
        WjLyAttributeScore personAttrScore = new WjLyAttributeScore();
        personAttrScore.setAttributeName(req.getAttributeName());
        personAttrScore.setBaseScore(req.getBaseScore());
        personAttrScore.setRiskFactor(req.getRiskFactor());
        personAttrScore.setGroupId(req.getGroupId());
        personAttrScore.setStatus(req.getStatus());
        personAttrScore.setCreateUser(SsoUtil.getUserIdCard());
        personAttrScore.setCreateTime(new Date());
        personAttrScore.setUpdateUser(SsoUtil.getUserIdCard());
        personAttrScore.setUpdateTime(new Date());
        return save(personAttrScore);
    }
}
