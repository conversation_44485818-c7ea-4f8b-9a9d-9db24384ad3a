package com.hl.building.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.domain.vo.WjLyTaskResultPersonReqVO;
import com.hl.building.service.WjLyTaskResultPersonService;
import com.hl.warn.domain.WjLyTaskResultPerson;
import com.hl.warn.mapper.WjLyTaskResultPersonMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class WjLyTaskResultPersonServiceImpl extends ServiceImpl<WjLyTaskResultPersonMapper, WjLyTaskResultPerson> implements WjLyTaskResultPersonService {


    @Override
    public Page<WjLyTaskResultPerson> listResult(WjLyTaskResultPersonReqVO taskResultPersonReqVO) {

        Page<WjLyTaskResultPerson> page = page(new Page<>(taskResultPersonReqVO.getPage(), taskResultPersonReqVO.getLimit()), Wrappers.<WjLyTaskResultPerson>lambdaQuery()
                .eq(StringUtils.isNotBlank(taskResultPersonReqVO.getTaskId()), WjLyTaskResultPerson::getTaskId, taskResultPersonReqVO.getTaskId())
                .and(StringUtils.isNotBlank(taskResultPersonReqVO.getQuery()),
                        wjLyTaskResultPersonLambdaQueryWrapper -> wjLyTaskResultPersonLambdaQueryWrapper
                                .like(WjLyTaskResultPerson::getGmsfhm, taskResultPersonReqVO.getQuery())
                                .or()
                                .like(WjLyTaskResultPerson::getXm, taskResultPersonReqVO.getQuery()))
                .orderByDesc(WjLyTaskResultPerson::getId));
        return page;
    }
}
