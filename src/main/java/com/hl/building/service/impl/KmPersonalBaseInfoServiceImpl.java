package com.hl.building.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.service.KmPersonalBaseInfoService;
import com.hl.warn.domain.KmPersonalBaseInfo;
import com.hl.warn.mapper.KmPersonalBaseInfoMapper;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class KmPersonalBaseInfoServiceImpl extends ServiceImpl<KmPersonalBaseInfoMapper, KmPersonalBaseInfo> implements KmPersonalBaseInfoService {
    public List<KmPersonalBaseInfo> getPersonalBaseInfoByGroupId(List<String> groupId) {
        List<KmPersonalBaseInfo> list = baseMapper.getPersonalBaseInfoByGroupId(groupId);
        return list;
    }
}
