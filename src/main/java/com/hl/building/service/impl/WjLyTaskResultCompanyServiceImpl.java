package com.hl.building.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.domain.vo.WjLyTaskResultCompanyReqVO;
import com.hl.building.service.WjLyTaskResultCompanyService;
import com.hl.warn.domain.WjLyTaskResultCompany;
import com.hl.warn.mapper.WjLyTaskResultCompanyMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
@Service
public class WjLyTaskResultCompanyServiceImpl extends ServiceImpl<WjLyTaskResultCompanyMapper, WjLyTaskResultCompany> implements WjLyTaskResultCompanyService{


    @Override
    public Page<WjLyTaskResultCompany> listResult(WjLyTaskResultCompanyReqVO taskResultCompanyReqVO) {

        Page<WjLyTaskResultCompany> page = page(new Page<>(taskResultCompanyReqVO.getPage(), taskResultCompanyReqVO.getLimit()), Wrappers.<WjLyTaskResultCompany>lambdaQuery()
                .eq(StringUtils.isNotBlank(taskResultCompanyReqVO.getTaskId()), WjLyTaskResultCompany::getTaskId, taskResultCompanyReqVO.getTaskId())
                .and(StringUtils.isNotBlank(taskResultCompanyReqVO.getQuery()),
                        wjLyTaskResultCompanyLambdaQueryWrapper -> wjLyTaskResultCompanyLambdaQueryWrapper
                                .like(WjLyTaskResultCompany::getCompanyName, taskResultCompanyReqVO.getQuery())
                                .or()
                                .like(WjLyTaskResultCompany::getCompanyCode, taskResultCompanyReqVO.getQuery()))
        );
        return page;
    }
}
