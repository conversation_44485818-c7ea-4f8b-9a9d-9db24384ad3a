package com.hl.building.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.domain.WjLyJqxx;
import com.hl.building.domain.dto.WjLyJqxxPageReq;
import com.hl.building.domain.vo.WjLyJqxxPageRespVO;
import com.hl.building.mapper.WjLyJqxxMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WjLyJqxxService extends ServiceImpl<WjLyJqxxMapper, WjLyJqxx> {

    private final WjLyJqxxMapper wjLyJqxxMapper;

    public Page<WjLyJqxxPageRespVO> pageList(WjLyJqxxPageReq req) {

        Page<WjLyJqxxPageRespVO> page = wjLyJqxxMapper.pageList(Page.of(req.getPage(), req.getLimit()),req);
        return page;
    }

//    public List<WjLyJqxxPageRespVO> selectList(WjLyJqxxPageReq req) {
//        LambdaQueryWrapper<WjLyJqxx> queryWrapper = new LambdaQueryWrapper<>();
//        if(StringUtils.isNotEmpty(req.getJjbh())) queryWrapper.like(WjLyJqxx::getJjbh, req.getJjbh());
//        if(StringUtils.isNotEmpty(req.getBjlx())) queryWrapper.like(WjLyJqxx::getBjlx, req.getBjlx());
//        if(StringUtils.isNotEmpty(req.getJjdw())) queryWrapper.like(WjLyJqxx::getJjdw, req.getJjdw());
//        if(StringUtils.isNotEmpty(req.getCjlb())) queryWrapper.like(WjLyJqxx::getCjlb, req.getCjlb());
//        if(StringUtils.isNotEmpty(req.getCjdw())) queryWrapper.like(WjLyJqxx::getCjdw, req.getCjdw());
//        List<WjLyJqxx> wjLyJqxxes = wjLyJqxxMapper.selectList(queryWrapper);
//        List<WjLyJqxxPageRespVO> filterList = new ArrayList<>();
//        BeanUtils.copyProperties(wjLyJqxxes, filterList);
//        List<WjLyJqxxPageRespVO> resultList = filterList.stream().filter(item -> isExistArray(item.getCompanyInfo(), req.getCompanyName())
//                        && isExistArray(item.getBuildingInfo(), req.getBuildingName()))
//                .collect(Collectors.toList());
//
//        int start = (req.getPage()-1) * req.getLimit();
//        int end = Math.min(start + req.getLimit(), filterList.size());
//        if(!filterList.isEmpty()){
//            for (int i = start; i < end; i++) {
//                resultList.add(filterList.get(i));
//            }
//        }
//
//        return resultList;
//    }
//
//    private boolean isExistArray(List<String> list, String key){
//        if(list==null || list.isEmpty()) return false;
//        if(StringUtils.isEmpty(key)) return true;
//        return list.contains(key);
//    }


}
