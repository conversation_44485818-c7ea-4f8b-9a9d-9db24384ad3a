package com.hl.building.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.domain.WjExcelData;
import com.hl.building.domain.WjExcelFieldConfig;
import com.hl.building.domain.WjExcelImportConfig;
import com.hl.building.domain.dto.WjExcelDataQueryReq;
import com.hl.building.mapper.WjExcelDataMapper;
import com.hl.building.mapper.WjExcelFieldConfigMapper;
import com.hl.building.mapper.WjExcelImportConfigMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@RequiredArgsConstructor
public class WjExcelDataService extends ServiceImpl<WjExcelDataMapper, WjExcelData> {

    private final WjExcelImportConfigMapper configMapper;
    private final WjExcelFieldConfigMapper fieldMapper;
    private final WjExcelDataMapper dataMapper;


    public Long autoCreateConfigAndImport(MultipartFile file, String templateName, String remark) throws IOException {
        // 1. 读取 Excel 首行作为标题
        List<Map<Integer, String>> rows = new ArrayList<>();
        List<String> headers = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                headers.addAll(headMap.values());
            }

            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                rows.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet().doRead();

        if (headers.isEmpty()) {
            throw new IllegalArgumentException("Excel 表头为空！");
        }

        // 2. 生成 ImportConfig
        WjExcelImportConfig config = new WjExcelImportConfig();
        config.setName(templateName);
        config.setRemark(remark);
        config.setCreateTime(new Date());
        config.setTableName("excel_data_" + System.currentTimeMillis());

        configMapper.insert(config);

        AtomicInteger order = new AtomicInteger(0);
        List<WjExcelFieldConfig> fieldConfigs = new ArrayList<>();
        Map<Integer, String> colIndexToFieldName = new HashMap<>();

        for (int i = 0; i < headers.size(); i++) {
            String header = headers.get(i);
            String fieldName = toFieldName(header, i);

            WjExcelFieldConfig field = new WjExcelFieldConfig();
            field.setImportConfigId(config.getId());
            field.setColumnName(header);
            field.setFieldName(fieldName);
            field.setDataType("varchar");
            field.setIsRequired(false);
            field.setFieldOrder(order.getAndIncrement());
            fieldConfigs.add(field);

            colIndexToFieldName.put(i, fieldName);
        }
        fieldConfigs.forEach(fieldMapper::insert);
        // 4. 导入数据到 JSON
        for (Map<Integer, String> row : rows) {
            Map<String, Object> dataMap = new HashMap<>();
            for (Map.Entry<Integer, String> cell : row.entrySet()) {
                String fieldName = colIndexToFieldName.get(cell.getKey());
                if (fieldName != null) {
                    dataMap.put(fieldName, cell.getValue());
                }
            }

            WjExcelData excelData = new WjExcelData();
            excelData.setImportConfigId(config.getId());
            excelData.setData(new JSONObject(dataMap));
            excelData.setCreateTime(new Date());
            dataMapper.insert(excelData);
        }

        return config.getId();
    }


    private String toFieldName(String columnName, int index) {
        return PinyinUtil.getPinyin(columnName, "") + "_" + index;
    }

    private List<Map<String, String>> getHeader(Long importConfigId) {
        LambdaQueryWrapper<WjExcelFieldConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WjExcelFieldConfig::getImportConfigId, importConfigId)
                .orderByAsc(WjExcelFieldConfig::getFieldOrder);
        List<WjExcelFieldConfig> fields = fieldMapper.selectList(wrapper);

        List<Map<String, String>> headers = new ArrayList<>();
        for (WjExcelFieldConfig field : fields) {
            Map<String, String> header = new HashMap<>();
            header.put("item", field.getFieldName());
            header.put("name", field.getColumnName());
            headers.add(header);
        }
        return headers;
    }

//    public JSONObject queryData(WjExcelDataQueryReq request) {
//        // 构建查询条件
//        LambdaQueryWrapper<WjExcelData> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(WjExcelData::getImportConfigId, request.getImportConfigId());
//
//        // 添加JSONB查询条件
//        if (request.getData() != null && !request.getData().isEmpty()) {
//            for (WjExcelDataQueryReq.ColumnQuery query : request.getData()) {
//                String sql = "data::jsonb->> '"+ query.getItem()+"' LIKE CONCAT('%','"+query.getValue()+"','%')";
//
// //                wrapper.apply("data::jsonb->>'{0}' LIKE CONCAT('%',{1},'%')", query.getItem(), query.getValue());
//                wrapper.apply(sql);
//            }
//        }
//
//        Page<WjExcelData> page = page(Page.of(request.getPage(), request.getLimit()), wrapper);
//        List<JSONObject> list = page.getRecords().stream().map(WjExcelData::getData).collect(Collectors.toList());
//        // 构建返回结果
//        JSONObject result = new JSONObject();
//        result.put("header", getHeader(request.getImportConfigId()));
//        result.put("list", list);
//        result.put("total", page.getTotal());
//        return result;
//    }

    public JSONObject queryData(WjExcelDataQueryReq request) {
        // 构建查询条件
        LambdaQueryWrapper<WjExcelData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WjExcelData::getImportConfigId, request.getImportConfigId());
        List<JSONObject> filterList = new ArrayList<>();
        List<JSONObject> resultList = new ArrayList<>();
        //获取全部数据
        List<WjExcelData> wjExcelDataList = dataMapper.selectList(wrapper);

        if (request.getData() != null && !request.getData().isEmpty()) {

            List<WjExcelDataQueryReq.ColumnQuery> queries = request.getData();

            for (WjExcelData wjExcelData : wjExcelDataList) {
                JSONObject data = wjExcelData.getData();
                boolean match = true;
                for (WjExcelDataQueryReq.ColumnQuery query : queries) {
                    String item = query.getItem();
                    String value = query.getValue();
                    if (data.containsKey(item)) {
                        if (StrUtil.isNotBlank(data.getString(item)) && !data.getString(item).contains(value)) {
                            match = false;
                            break;
                        }
                    }
                }
                if (match) {
                    filterList.add(data);
                }
            }


//            for (WjExcelDataQueryReq.ColumnQuery query : request.getData()) {
//
//                for (WjExcelData wjExcelData : wjExcelDataList) {
//                    JSONObject data = wjExcelData.getData();
//                    if (data.containsKey(query.getItem())) {
//                        String expected = query.getValue();
//                        String actual = data.getString(query.getItem());
//                        if (StringUtils.isNotEmpty(actual) && actual.contains(expected)) {
//                            filterList.add(data);
//                        }
//                    }
//                }
//            }
        }

        //分页
        int start = (request.getPage()-1) * request.getLimit();
        int end = Math.min(start + request.getLimit(), filterList.size());
        if(!filterList.isEmpty()){
            for (int i = start; i < end; i++) {
                resultList.add(filterList.get(i));
            }
        }


//        Page<WjExcelData> page = page(Page.of(request.getPage(), request.getLimit()), wrapper);
        // 构建返回结果
        JSONObject result = new JSONObject();
        result.put("header", getHeader(request.getImportConfigId()));
        result.put("list", resultList);
        result.put("total", resultList.size());
        return result;
    }


}
