package com.hl.building.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.domain.WjLyCaseInfo;
import com.hl.building.domain.dto.WjLyCaseInfoPageReq;
import com.hl.building.mapper.WjLyCaseInfoMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
@Service
@RequiredArgsConstructor
public class WjLyCaseInfoService extends ServiceImpl<WjLyCaseInfoMapper, WjLyCaseInfo> {

    private final WjLyCaseInfoMapper wjLyCaseInfoMapper;

    public Page<WjLyCaseInfo> pageList(WjLyCaseInfoPageReq req) {

        Page<WjLyCaseInfo> page = wjLyCaseInfoMapper.pageList(Page.of(req.getPage(), req.getLimit()),req);

        return page;

    }
}
