package com.hl.building.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.*;
import com.hl.building.mapper.WjJqDataSourceMapper;
import com.hl.building.mapper.WjLyCaseInfoMapper;
import com.hl.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class WjLyJqCaseSyncService {

    private final WjJqDataSourceMapper wjJqDataSourceMapper;
    private final WjLyJqxxService wjLyJqxxService;
    private final WjCompanyInfoService wjCompanyInfoService;
    private final WjBuildingInfoService wjBuildingInfoService;


    private final WjLyCaseInfoService wjLyCaseInfoService;

    private final WjLyCaseInfoMapper wjLyCaseInfoMapper;


    private final WjPersonInfoService wjPersonInfoService;


    private static final int PAGE_SIZE = 100;




    /**
     * 同步警情数据
     * 从WjCompanyInfo和WjBuildingInfo中获取jq_label字段，查询警情信息，并保存到WjLyJqxx中
     */
//    @Transactional(rollbackFor = Exception.class)
//    @EventListener(ApplicationReadyEvent.class)
    public void syncJqCaseData() {
        log.info("开始同步警情数据");

        // 1. 同步公司警情数据
       syncCompanyJqData();

        // 2. 同步楼宇警情数据
        syncBuildingJqData();

        // 3. 同步人员警情数据
       syncPersonJqData();

        // 4. 同步公司案件数据
//        syncCompanyCaseData();

        // 5. 同步楼宇案件数据
//        syncBuildingCaseData();

        // 6. 同步人员案件数据
        syncPersonCaseData();

        log.info("警情数据同步完成");
    }

    /**
     * 同步公司警情数据
     */
    private void syncCompanyJqData() {
        log.info("开始同步公司警情数据");
        List<WjCompanyInfo> companyInfoList = wjCompanyInfoService.list();

        // 按每1000个公司分组处理
        int batchSize = 1000;
        for (int i = 0; i < companyInfoList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, companyInfoList.size());
            List<WjCompanyInfo> batch = companyInfoList.subList(i, endIndex);

            // 收集该批次中所有有效的jgbh
            List<String> jgbhList = new ArrayList<>();
            Map<String, WjCompanyInfo> jgbhToCompanyMap = new HashMap<>();

            for (WjCompanyInfo companyInfo : batch) {
                if (companyInfo.getJgbh() != null && !companyInfo.getJgbh().isEmpty()) {
                    jgbhList.add(companyInfo.getJgbh());
                    jgbhToCompanyMap.put(companyInfo.getJgbh(), companyInfo);
                }
            }

            if (!jgbhList.isEmpty()) {
                long currentPage = 1;
                boolean hasMore = true;

                while (hasMore) {
                    Page<WjLyJqxx> page = new Page<>(currentPage, PAGE_SIZE);
                    Page<WjLyJqxx> jqInfoPage = wjJqDataSourceMapper.queryJqInfoByJgbhList(page, jgbhList);

                    if (jqInfoPage == null || jqInfoPage.getRecords().isEmpty()) {
                        hasMore = false;
                        continue;
                    }

                    // 处理查询结果
                    for (WjLyJqxx jqxx : jqInfoPage.getRecords()) {
                        String jgbh = jqxx.getJgbh();
                        WjCompanyInfo companyInfo = jgbhToCompanyMap.get(jgbh);

                        if (companyInfo != null) {
                            jqxx.setCompany(Collections.singletonList(companyInfo.getCompanyId()));
                            jqxx.setCompanyInfo(Collections.singletonList(companyInfo.getCompanyName()));
                            if (companyInfo.getBuildId() != null) {
                                jqxx.setBuilding(Collections.singletonList(companyInfo.getBuildId()));
                                jqxx.setBuildingInfo(Collections.singletonList(companyInfo.getBuildName()));
                            }
                            saveOrUpdateJqxx(jqxx);
                        }
                    }

                    hasMore = currentPage * PAGE_SIZE < jqInfoPage.getTotal();
                    currentPage++;
                }
            }

            log.info("完成处理第{}到{}个公司的警情数据", i + 1, endIndex);
        }

        log.info("公司警情数据同步完成");
    }

    /**
     * 同步楼宇警情数据
     */
    private void syncBuildingJqData() {
        log.info("开始同步楼宇警情数据");
        List<WjBuildingInfo> buildingInfoList = wjBuildingInfoService.list();

        for (WjBuildingInfo buildingInfo : buildingInfoList) {
            if (buildingInfo.getJqLabel() == null || buildingInfo.getJqLabel().isEmpty()) {
                continue;
            }

            processJqDataByLabel(buildingInfo.getJqLabel(),
                    (jqxx) -> {
                        jqxx.setBuilding(Collections.singletonList(buildingInfo.getBuildId()));
                        jqxx.setBuildingInfo(Collections.singletonList(buildingInfo.getBuildName()));
                    },
                    "楼宇[" + buildingInfo.getBuildName() + "]"
            );
        }
        log.info("楼宇警情数据同步完成");
    }

    /**
     * 同步人员警情数据
     */
    private void syncPersonJqData() {
        log.info("开始同步人员警情数据");
        List<WjPersonInfo> personList = wjPersonInfoService.list();
        for (WjPersonInfo person : personList) {
            if (person.getIdCard() != null && !person.getIdCard().isEmpty()) {
                processPersonJqData(person);
            }
        }
        log.info("人员警情数据同步完成");
    }

    /**
     * 处理单个人员的警情数据
     */
    private void processPersonJqData(WjPersonInfo person) {
        int pageNum = 1;
        while (true) {
            Page<WjLyJqxx> page = new Page<>(pageNum, PAGE_SIZE);
            Page<WjLyJqxx> result = wjJqDataSourceMapper.queryJqInfoByIdCard(page, person.getIdCard());

            if (result.getRecords().isEmpty()) {
                break;
            }

            for (WjLyJqxx jqxx : result.getRecords()) {
                // 根据警情编号查询是否存在
                LambdaQueryWrapper<WjLyJqxx> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(WjLyJqxx::getJjbh, jqxx.getJjbh());
                WjLyJqxx existingJqxx = wjLyJqxxService.getOne(queryWrapper);

                if (existingJqxx != null) {
                    // 如果存在，保留原有的关联关系，添加新的人员关联
                    jqxx.setCompany(mergeLists(existingJqxx.getCompany(), jqxx.getCompany()));
                    jqxx.setCompanyInfo(mergeLists(existingJqxx.getCompanyInfo(), jqxx.getCompanyInfo()));
                    jqxx.setBuilding(mergeLists(existingJqxx.getBuilding(), jqxx.getBuilding()));
                    jqxx.setBuildingInfo(mergeLists(existingJqxx.getBuildingInfo(), jqxx.getBuildingInfo()));
                    jqxx.setPerson(mergeLists(existingJqxx.getPerson(), Collections.singletonList(person.getPersonId())));
                    jqxx.setPersonInfo(mergeLists(existingJqxx.getPersonInfo(), Collections.singletonList(person.getName())));

                    // 更新数据
                    wjLyJqxxService.updateById(jqxx);
                    log.info("更新警情数据，警情编号：{}，关联人员：{}", jqxx.getJjbh(), person.getName());
                } else {
                    log.info("新增警情数据，机构编号：{}", person.getJgbh());
                    LambdaQueryWrapper<WjCompanyInfo> companyQueryWrapper = new LambdaQueryWrapper<>();
                    companyQueryWrapper.eq(WjCompanyInfo::getJgbh, person.getJgbh());
                    WjCompanyInfo wjCompanyInfo = wjCompanyInfoService.getOne(companyQueryWrapper);
                    if (wjCompanyInfo != null) {
                        jqxx.setCompany(Collections.singletonList(wjCompanyInfo.getCompanyId()));
                        jqxx.setCompanyInfo(Collections.singletonList(wjCompanyInfo.getCompanyName()));
                        jqxx.setBuilding(Collections.singletonList(wjCompanyInfo.getBuildId()));
                        jqxx.setBuildingInfo(Collections.singletonList(wjCompanyInfo.getBuildName()));
                        log.info("新增警情数据，company：{}，companyInfo：{}", wjCompanyInfo.getCompanyId(), wjCompanyInfo.getCompanyName());
                        log.info("新增警情数据，building：{}，buildingInfo：{}", wjCompanyInfo.getBuildId(), wjCompanyInfo.getBuildName());
                    }

                    // 不存在则新增
                    jqxx.setPerson(Collections.singletonList(person.getPersonId()));
                    jqxx.setPersonInfo(Collections.singletonList(person.getName()));
                    wjLyJqxxService.save(jqxx);
                    log.info("新增警情数据，警情编号：{}，关联人员：{}", jqxx.getJjbh(), person.getName());
                }
            }

            if (pageNum >= result.getPages()) {
                break;
            }
            pageNum++;
        }
    }

    /**
     * 同步公司案件数据
     */
    private void syncCompanyCaseData() {
        log.info("开始同步公司案件数据");
        List<WjCompanyInfo> companyInfoList = wjCompanyInfoService.list();

        for (WjCompanyInfo companyInfo : companyInfoList) {
            if (companyInfo.getJqLabel() == null || companyInfo.getJqLabel().isEmpty()) {
                continue;
            }

            processCaseDataByLabel(companyInfo.getJqLabel(),
                    (caseInfo) -> {
                        caseInfo.setCompany(Collections.singletonList(companyInfo.getCompanyId()));
                        caseInfo.setCompanyInfo(Collections.singletonList(companyInfo.getCompanyName()));
                        if (companyInfo.getBuildId() != null) {
                            caseInfo.setBuilding(Collections.singletonList(companyInfo.getBuildId()));
                            caseInfo.setBuildingInfo(Collections.singletonList(companyInfo.getCompanyName()));
                        }
                    },
                    "公司[" + companyInfo.getCompanyName() + "]"
            );
        }
        log.info("公司案件数据同步完成");
    }

    /**
     * 同步楼宇案件数据
     */
    private void syncBuildingCaseData() {
        log.info("开始同步楼宇案件数据");
        List<WjBuildingInfo> buildingInfoList = wjBuildingInfoService.list();

        for (WjBuildingInfo buildingInfo : buildingInfoList) {
            if (buildingInfo.getJqLabel() == null || buildingInfo.getJqLabel().isEmpty()) {
                continue;
            }

            processCaseDataByLabel(buildingInfo.getJqLabel(),
                    (caseInfo) -> {
                        caseInfo.setBuilding(Collections.singletonList(buildingInfo.getBuildId()));
                        caseInfo.setBuildingInfo(Collections.singletonList(buildingInfo.getBuildName()));
                    },
                    "楼宇[" + buildingInfo.getBuildName() + "]"
            );
        }
        log.info("楼宇案件数据同步完成");
    }

    /**
     * 同步人员案件数据
     */
    private void syncPersonCaseData() {
        log.info("开始同步人员案件数据");
        List<WjPersonInfo> personList = wjPersonInfoService.list();
        for (WjPersonInfo person : personList) {
            if (person.getIdCard() != null && !person.getIdCard().isEmpty()) {
                processPersonCaseData(person);
            }
        }
        log.info("人员案件数据同步完成");
    }

    /**
     * 处理单个人员的案件数据
     */
    private void processPersonCaseData(WjPersonInfo person) {
        int pageNum = 1;
        while (true) {
            Page<WjLyCaseInfo> page = new Page<>(pageNum, PAGE_SIZE);
            Page<WjLyCaseInfo> result =   wjLyCaseInfoMapper.queryCaseInfoByIdCard(page, person.getIdCard());
//            Page<WjLyCaseInfo> result = wjJqDataSourceMapper.queryCaseInfoByIdCard(page, person.getIdCard());

            if (result.getRecords().isEmpty()) {
                break;
            }

            for (WjLyCaseInfo caseInfo : result.getRecords()) {
                // 根据案件编号查询是否存在
                LambdaQueryWrapper<WjLyCaseInfo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(WjLyCaseInfo::getCaseNo, caseInfo.getCaseNo());
                WjLyCaseInfo existingCaseInfo = wjLyCaseInfoService.getOne(queryWrapper);

                if (existingCaseInfo != null) {
                    // 如果存在，保留原有的关联关系，添加新的人员关联
                    caseInfo.setCompany(mergeLists(existingCaseInfo.getCompany(), caseInfo.getCompany()));
                    caseInfo.setCompanyInfo(mergeLists(existingCaseInfo.getCompanyInfo(), caseInfo.getCompanyInfo()));
                    caseInfo.setBuilding(mergeLists(existingCaseInfo.getBuilding(), caseInfo.getBuilding()));
                    caseInfo.setBuildingInfo(mergeLists(existingCaseInfo.getBuildingInfo(), caseInfo.getBuildingInfo()));
                    caseInfo.setPerson(mergeLists(existingCaseInfo.getPerson(), Collections.singletonList(person.getPersonId())));
                    caseInfo.setPersonInfo(mergeLists(existingCaseInfo.getPersonInfo(), Collections.singletonList(person.getName())));

                    // 更新数据
                    wjLyCaseInfoService.updateById(caseInfo);
                    log.info("更新案件数据，案件编号：{}，关联人员：{}", caseInfo.getCaseNo(), person.getName());
                } else {
                    LambdaQueryWrapper<WjCompanyInfo> companyQueryWrapper = new LambdaQueryWrapper<>();
                    companyQueryWrapper.eq(WjCompanyInfo::getJgbh, person.getJgbh());
                    WjCompanyInfo wjCompanyInfo = wjCompanyInfoService.getOne(companyQueryWrapper);
                    if (wjCompanyInfo != null) {
                        caseInfo.setCompany(Collections.singletonList(wjCompanyInfo.getCompanyId()));
                        caseInfo.setCompanyInfo(Collections.singletonList(wjCompanyInfo.getCompanyName()));
                        caseInfo.setBuilding(Collections.singletonList(wjCompanyInfo.getBuildId()));
                        caseInfo.setBuildingInfo(Collections.singletonList(wjCompanyInfo.getBuildName()));
                        log.info("新增案件数据，company：{}，companyInfo：{}", wjCompanyInfo.getCompanyId(), wjCompanyInfo.getCompanyName());
                        log.info("新增案件数据，building：{}，buildingInfo：{}", wjCompanyInfo.getBuildId(), wjCompanyInfo.getBuildName());
                    }

                    // 不存在则新增
                    caseInfo.setPerson(Collections.singletonList(person.getPersonId()));
                    caseInfo.setPersonInfo(Collections.singletonList(person.getName()));
                    wjLyCaseInfoService.save(caseInfo);
                    log.info("新增案件数据，案件编号：{}，关联人员：{}", caseInfo.getCaseNo(), person.getName());
                }
            }

            if (pageNum >= result.getPages()) {
                break;
            }
            pageNum++;
        }
    }

    /**
     * 根据标签处理警情数据
     *
     * @param jqLabel        警情标签
     * @param relationSetter 关联关系设置器
     * @param entityName     实体名称（用于日志）
     */
    private void processJqDataByLabel(List<String> jqLabel,
                                      JqxxRelationSetter relationSetter,
                                      String entityName) {
        log.info("开始处理{}的警情数据", entityName);

        Map<String, Object> param = new HashMap<>();
        param.put("jqLabel", jqLabel);

        long currentPage = 1;
        boolean hasMore = true;

        while (hasMore) {
            Page<WjLyJqxx> page = new Page<>(currentPage, PAGE_SIZE);
            Page<WjLyJqxx> jqInfoPage = wjJqDataSourceMapper.queryJqInfoByJqLabel(page, param);

            if (jqInfoPage == null || jqInfoPage.getRecords().isEmpty()) {
                hasMore = false;
                continue;
            }

            processJqDataPage(jqInfoPage.getRecords(), relationSetter, entityName);

            hasMore = currentPage * PAGE_SIZE < jqInfoPage.getTotal();
            currentPage++;
        }

        log.info("{}的警情数据处理完成", entityName);
    }

    /**
     * 处理一页警情数据
     *
     * @param jqDataList     警情数据列表
     * @param relationSetter 关联关系设置器
     * @param entityName     实体名称（用于日志）
     */
    private void processJqDataPage(List<WjLyJqxx> jqDataList,
                                   JqxxRelationSetter relationSetter,
                                   String entityName) {
        for (WjLyJqxx jqxx : jqDataList) {
            try {
                // 设置关联关系
                relationSetter.setRelations(jqxx);
                // 保存或更新警情数据
                saveOrUpdateJqxx(jqxx);
            } catch (Exception e) {
                log.error("处理警情数据失败，警情编号：{}，{}，错误：{}",
                        jqxx.getJjbh(), entityName, e.getMessage());
            }
        }
    }

    /**
     * 保存或更新警情数据
     *
     * @param jqxx 警情信息
     */
    private void saveOrUpdateJqxx(WjLyJqxx jqxx) {
        // 根据警情编号查询是否存在
        LambdaQueryWrapper<WjLyJqxx> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WjLyJqxx::getJjbh, jqxx.getJjbh());
        WjLyJqxx existingJqxx = wjLyJqxxService.getOne(queryWrapper);

        if (existingJqxx != null) {
            // 如果存在，保留原有的关联关系
            jqxx.setCompany(mergeLists(existingJqxx.getCompany(), jqxx.getCompany()));
            jqxx.setCompanyInfo(mergeLists(existingJqxx.getCompanyInfo(), jqxx.getCompanyInfo()));
            jqxx.setBuilding(mergeLists(existingJqxx.getBuilding(), jqxx.getBuilding()));
            jqxx.setBuildingInfo(mergeLists(existingJqxx.getBuildingInfo(), jqxx.getBuildingInfo()));
            jqxx.setPerson(mergeLists(existingJqxx.getPerson(), jqxx.getPerson()));
            jqxx.setPersonInfo(mergeLists(existingJqxx.getPersonInfo(), jqxx.getPersonInfo()));

            // 更新数据
            wjLyJqxxService.updateById(jqxx);
            log.info("更新警情数据，警情编号：{}", jqxx.getJjbh());
        } else {
            // 不存在则新增
            wjLyJqxxService.save(jqxx);
            log.info("新增警情数据，警情编号：{}", jqxx.getJjbh());
        }
    }

    /**
     * 合并两个列表，去重
     *
     * @param list1 列表1
     * @param list2 列表2
     * @return 合并后的列表
     */
    private List<String> mergeLists(List<String> list1, List<String> list2) {
        if (list1 == null) {
            return list2;
        }
        if (list2 == null) {
            return list1;
        }
        Set<String> mergedSet = new HashSet<>(list1);
        mergedSet.addAll(list2);
        return new ArrayList<>(mergedSet);
    }

    /**
     * 根据标签处理案件数据
     *
     * @param caseLabel     案件标签
     * @param relationSetter 关联关系设置器
     * @param entityName    实体名称（用于日志）
     */
    private void processCaseDataByLabel(List<String> caseLabel,
                                      CaseInfoRelationSetter relationSetter,
                                      String entityName) {
        log.info("开始处理{}的案件数据", entityName);

        Map<String, Object> param = new HashMap<>();
        param.put("caseLabel", caseLabel);

        long currentPage = 1;
        boolean hasMore = true;

        while (hasMore) {
            Page<WjLyCaseInfo> page = new Page<>(currentPage, PAGE_SIZE);
            Page<WjLyCaseInfo> caseInfoPage = wjJqDataSourceMapper.queryCaseInfoByLabel(page, param);

            if (caseInfoPage == null || caseInfoPage.getRecords().isEmpty()) {
                hasMore = false;
                continue;
            }

            processCaseDataPage(caseInfoPage.getRecords(), relationSetter, entityName);

            hasMore = currentPage * PAGE_SIZE < caseInfoPage.getTotal();
            currentPage++;
        }

        log.info("{}的案件数据处理完成", entityName);
    }

    /**
     * 处理一页案件数据
     *
     * @param caseDataList   案件数据列表
     * @param relationSetter 关联关系设置器
     * @param entityName     实体名称（用于日志）
     */
    private void processCaseDataPage(List<WjLyCaseInfo> caseDataList,
                                   CaseInfoRelationSetter relationSetter,
                                   String entityName) {
        for (WjLyCaseInfo caseInfo : caseDataList) {
            try {
                // 设置关联关系
                relationSetter.setRelations(caseInfo);
                // 保存或更新案件数据
                saveOrUpdateCaseInfo(caseInfo);
            } catch (Exception e) {
                log.error("处理案件数据失败，案件编号：{}，{}，错误：{}",
                        caseInfo.getCaseNo(), entityName, e.getMessage());
            }
        }
    }

    /**
     * 保存或更新案件数据
     *
     * @param caseInfo 案件信息
     */
    private void saveOrUpdateCaseInfo(WjLyCaseInfo caseInfo) {
        // 根据案件编号查询是否存在
        LambdaQueryWrapper<WjLyCaseInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WjLyCaseInfo::getCaseNo, caseInfo.getCaseNo());
        WjLyCaseInfo existingCaseInfo = wjLyCaseInfoService.getOne(queryWrapper);

        if (existingCaseInfo != null) {
            // 如果存在，保留原有的关联关系
            caseInfo.setCompany(mergeLists(existingCaseInfo.getCompany(), caseInfo.getCompany()));
            caseInfo.setCompanyInfo(mergeLists(existingCaseInfo.getCompanyInfo(), caseInfo.getCompanyInfo()));
            caseInfo.setBuilding(mergeLists(existingCaseInfo.getBuilding(), caseInfo.getBuilding()));
            caseInfo.setBuildingInfo(mergeLists(existingCaseInfo.getBuildingInfo(), caseInfo.getBuildingInfo()));
            caseInfo.setPerson(mergeLists(existingCaseInfo.getPerson(), caseInfo.getPerson()));
            caseInfo.setPersonInfo(mergeLists(existingCaseInfo.getPersonInfo(), caseInfo.getPersonInfo()));

            // 更新数据
            wjLyCaseInfoService.updateById(caseInfo);
            log.info("更新案件数据，案件编号：{}", caseInfo.getCaseNo());
        } else {
            // 不存在则新增
            wjLyCaseInfoService.save(caseInfo);
            log.info("新增案件数据，案件编号：{}", caseInfo.getCaseNo());
        }
    }

    /**
     * 警情信息关联关系设置器接口
     */
    @FunctionalInterface
    private interface JqxxRelationSetter {
        void setRelations(WjLyJqxx jqxx);
    }

    /**
     * 案件信息关联关系设置器接口
     */
    @FunctionalInterface
    private interface CaseInfoRelationSetter {
        void setRelations(WjLyCaseInfo caseInfo);
    }

}
