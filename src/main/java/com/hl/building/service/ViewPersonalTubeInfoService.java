package com.hl.building.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.domain.ViewPersonalTubeInfo;
import com.hl.building.domain.dto.ListPersonalTubeReq;
import com.hl.building.mapper.ViewPersonalTubeInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ViewPersonalTubeInfoService extends ServiceImpl<ViewPersonalTubeInfoMapper, ViewPersonalTubeInfo> {

    private final ViewPersonalTubeInfoMapper viewPersonalTubeInfoMapper;

    public Page<ViewPersonalTubeInfo> pageList(ListPersonalTubeReq req) {

        Page<ViewPersonalTubeInfo> page = viewPersonalTubeInfoMapper.pageList(Page.of(req.getPage(), req.getLimit()), req);
        return page;
    }
}
