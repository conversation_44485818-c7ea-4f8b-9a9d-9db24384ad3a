package com.hl.building.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjCompanyInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hl.building.domain.dto.AddOrUpdCompanyReq;
import com.hl.building.domain.dto.ListCompanyReq;
import com.hl.building.domain.vo.WjCompanyRespVO;

import java.util.List;
import java.util.Map;

public interface WjCompanyInfoService extends IService<WjCompanyInfo>{


    Page<WjCompanyRespVO> pageCompanyList(ListCompanyReq req);

    boolean addCompany(AddOrUpdCompanyReq req);

    List<WjCompanyRespVO> distribution(Map<String, Object> req);

}
