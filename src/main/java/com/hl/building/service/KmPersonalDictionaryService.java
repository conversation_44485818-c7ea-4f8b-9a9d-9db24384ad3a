package com.hl.building.service;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.domain.KmPersonalDictionary;
import com.hl.building.mapper.KmPersonalDictionaryMapper;
import com.hl.building.trans.TransConstants;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class KmPersonalDictionaryService extends ServiceImpl<KmPersonalDictionaryMapper, KmPersonalDictionary> {


    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @EventListener(ApplicationReadyEvent.class)
    public void loadDictionaryOnStartup() {
        List<KmPersonalDictionary> allDictionaries = list();
        Map<String, List<KmPersonalDictionary>> groupedByZdlbdm = allDictionaries.stream()
                .filter(dictionary -> dictionary.getZdlbdm() != null)
                .collect(Collectors.groupingBy(KmPersonalDictionary::getZdlbdm));
        groupedByZdlbdm.forEach((zdlbdm, dictionaries) ->
                redisTemplate.opsForHash().put(TransConstants.KM_PERSON_DICT_TRANS, zdlbdm, dictionaries)
        );
    }

    public List<Tree<String>> dictTree(JSONObject param) {

        String type = param.getString("type");
        Integer isTree = param.getInteger("isTree");
        List<KmPersonalDictionary> dictionaryList = (List<KmPersonalDictionary>) redisTemplate.opsForHash()
                .get(TransConstants.KM_PERSON_DICT_TRANS, type);
        if (isTree != null && isTree == 0) {
            List<TreeNode<String>> collect = null;
            if (dictionaryList != null) {
                collect = dictionaryList.stream()
                        .map(dict -> new TreeNode<>(dict.getZddm(), dict.getFzddm(), dict.getZdmc(), 0))
                        .collect(Collectors.toList());
            }
            return TreeUtil.build(collect, null);
        }
        if (dictionaryList != null) {
            List<TreeNode<String>> collect = dictionaryList.stream()
                    .map(dict -> new TreeNode<>(dict.getZddm(), dict.getFzddm(), dict.getZdmc(), 0))
                    .collect(Collectors.toList());
            return TreeUtil.build(collect, "0");
        }

        return null;

    }
}
