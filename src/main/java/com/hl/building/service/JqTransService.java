package com.hl.building.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;

import com.hl.building.mapper.WjJqDataSourceMapper;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class JqTransService {

    private final WjJqDataSourceMapper  jqMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private static final String DICT_CACHE_KEY = "jq:dict:";
    private static final String DICT_INDEX_KEY = "jq:dict:index:";

    public enum DictType {
        JJCJN("jjcjn", "警情类型");

        @Getter
        private final String permission;
        private final String type;

        DictType(String permission, String type) {
            this.permission = permission;
            this.type = type;
        }

    }

    /**
     * 初始化字典数据到Redis缓存
     */
    @EventListener(value = ApplicationReadyEvent.class)
    private void initDictCache() {
        log.info("开始初始化警情字典数据");

        List<JSONObject> dictList = jqMapper.getDict();

        // 按permission分组
        Map<String, List<JSONObject>> dictMap = dictList.stream()
                .collect(Collectors.groupingBy(dict -> dict.getString("permission")));

        // 存入Redis原始数据
        dictMap.forEach((permission, list) -> {
            redisTemplate.opsForValue().set(DICT_CACHE_KEY + permission, list);

            // 建立索引
            Map<String, String> indexMap = list.stream()
                    .collect(Collectors.toMap(
                            dict -> dict.getString("id").substring(3),
                            dict -> dict.getString("dict_name"),
                            (existing, replacement) -> existing
                    ));
            redisTemplate.opsForValue().set(DICT_INDEX_KEY + permission, indexMap);
        });
        log.info("初始化警情字典数据完成");
    }

    /**
     * 根据字典类型和值获取字典名称
     *
     * @param dictType  字典类型
     * @param dictValue 字典值
     * @return 字典名称
     */
    public String getDictName(DictType dictType, String dictValue) {
        Object cacheValue = redisTemplate.opsForValue().get(DICT_INDEX_KEY + dictType.getPermission());
        if (cacheValue == null) {
            return "";
        }

        Map<String, String> indexMap = (Map<String, String>) cacheValue;
        return indexMap.getOrDefault(dictValue, "");
    }

    public void replaceJqData(List<JSONObject> jqData) {
        if (CollUtil.isEmpty(jqData)) {
            return;
        }
        jqData.forEach(jq -> {
            jq.put("bjlx_mc", getDictName(DictType.JJCJN, jq.getString("bjlx")));
            jq.put("cjlb_mc", getDictName(DictType.JJCJN, jq.getString("cjlb")));
        });
    }
}
