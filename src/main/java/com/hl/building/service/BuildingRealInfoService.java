package com.hl.building.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.client.JqFeignClient;
import com.hl.building.domain.WjBuildingInfo;
import com.hl.building.domain.header.HeaderCache;
import com.hl.building.domain.header.HeaderKey;
import com.hl.building.domain.vo.PoliceIncidentInfo;
import com.hl.building.mapper.FaceRecordMapper;
import com.hl.building.mapper.WjJqDataSourceMapper;
import com.hl.common.domain.R;
import com.hl.face.domain.WjFaceCameraInfo;
import com.hl.face.service.WjFaceCameraInfoService;
import com.hl.security.UserUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class BuildingRealInfoService {

    private final WjJqDataSourceMapper wjJqDataSourceMapper;

    private final FaceRecordMapper faceRecordMapper;

    private final JqFeignClient jqFeignClient;

    private final WjBuildingInfoService wjBuildingInfoService;

    private final WjFaceCameraInfoService wjFaceCameraInfoService;

    public JSONObject getPoliceData(JSONObject param) {
        JSONObject result = new JSONObject();

//        Integer page = param.getInteger("page");
//        Integer limit = param.getInteger("limit");

//        Page<JSONObject> policeData = wjJqDataSourceMapper.getPoliceData(Page.of(page, limit), param);
        result.put("header", HeaderCache.get(HeaderKey.JQ_BASE_HEADER.getKey()));

        param.put("jjdw", new JSONArray().fluentAdd("320412000000"));

        JSONArray bzbq = new JSONArray();
        bzbq.add("JG320412572306300001181105");
        bzbq.add("JG320412572306300001194299");
        bzbq.add("JG320412572403061548490001");
        param.put("bzbq", bzbq);
        String token = UserUtils.getUser().getToken();
        log.info("请求参数:{}", param);
        R<?> policeData = jqFeignClient.getPoliceData(param, token);

//            result.put("policeData", policeData.getData());
        result.put("list", policeData.getData());
        result.put("total", policeData.getCount());
        return result;
    }

    public JSONObject getFaceRecord(JSONObject param) {
        JSONObject result = new JSONObject();
        Integer page = param.getInteger("page");
        Integer limit = param.getInteger("limit");

        String buildId = param.getString("buildId");
        WjBuildingInfo buildingInfo = wjBuildingInfoService.getById(buildId);
        List<String> deviceInfo = buildingInfo.getDeviceInfo();
        if (deviceInfo == null || deviceInfo.isEmpty()) {
            result.put("header", HeaderCache.get(HeaderKey.CAMERA_FACE_RECORD.getKey()));
            result.put("list", new ArrayList<>());
            result.put("total", 0);
            return result;
        }

        List<WjFaceCameraInfo> list = wjFaceCameraInfoService.list(Wrappers.<WjFaceCameraInfo>lambdaQuery()
                .in(WjFaceCameraInfo::getId, deviceInfo));
        deviceInfo = list.stream().map(WjFaceCameraInfo::getCameraId).collect(Collectors.toList());
        param.put("deviceInfo", deviceInfo);

        Page<JSONObject> faceRecord = faceRecordMapper.getFaceRecord(Page.of(page, limit), param);

        List<String> idCard = faceRecord.getRecords().stream().map(k -> k.getString("id_card")).collect(Collectors.toList());
        if (!idCard.isEmpty()){
            List<JSONObject> jsonObjects = faceRecordMapper.queryPoliceData(idCard);
            Map<String, Integer> policeMap = jsonObjects.stream()
                    .collect(Collectors.toMap(k -> k.getString("gmsfhm"), v -> v.getInteger("count")));
            List<JSONObject> caseInfo =  faceRecordMapper.queryCaseData(idCard);
            Map<String, Integer> caseMap = caseInfo.stream()
                    .collect(Collectors.toMap(k -> k.getString("id_number"), v -> v.getInteger("count")));
            for (JSONObject record : faceRecord.getRecords()) {
                String gmsfhm = record.getString("id_card");
                record.put("police_count", policeMap.getOrDefault(gmsfhm, 0));
                record.put("case_count", caseMap.getOrDefault(gmsfhm, 0));
            }
        }

        result.put("header", HeaderCache.get(HeaderKey.CAMERA_FACE_RECORD.getKey()));
        result.put("list", faceRecord.getRecords());
        result.put("total", faceRecord.getTotal());
        return result;
    }

    public JSONObject getPersonPoliceRecord(JSONObject param) {
        JSONObject result = new JSONObject();
        result.put("header", HeaderCache.get(HeaderKey.PERSON_POLICE_RECORD.getKey()));
        List<PoliceIncidentInfo> objects = wjJqDataSourceMapper.queryPersonPoliceData(param);
        result.put("list", objects);
        return result;
    }

    public JSONObject getPersonCaseRecord(JSONObject param) {
        JSONObject result = new JSONObject();
        result.put("header", HeaderCache.get(HeaderKey.PERSON_CASE_INFO.getKey()));
        List<JSONObject> objects = faceRecordMapper.queryPersonCaseRecord(param);
        result.put("list", objects);
        return result;
    }
}
