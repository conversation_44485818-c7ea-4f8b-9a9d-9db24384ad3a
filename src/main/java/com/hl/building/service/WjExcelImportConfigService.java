package com.hl.building.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.domain.WjExcelImportConfig;
import com.hl.building.domain.dto.WjExcelImportConfigQueryRequest;
import com.hl.building.mapper.WjExcelImportConfigMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WjExcelImportConfigService extends ServiceImpl<WjExcelImportConfigMapper, WjExcelImportConfig> {

    public Page<WjExcelImportConfig> listConfig(WjExcelImportConfigQueryRequest request) {
        LambdaQueryWrapper<WjExcelImportConfig> wrapper = new LambdaQueryWrapper<>();
        
        if (StrUtil.isNotBlank(request.getName())) {
            wrapper.like(WjExcelImportConfig::getName, request.getName());
        }
        
        if (StrUtil.isNotBlank(request.getRemark())) {
            wrapper.like(WjExcelImportConfig::getRemark, request.getRemark());
        }
        if (request.getId() != null){
            wrapper.eq(WjExcelImportConfig::getId, request.getId());
        }
        
        wrapper.orderByDesc(WjExcelImportConfig::getCreateTime);

        Page<WjExcelImportConfig> page = page(new Page<>(request.getPage(), request.getLimit()), wrapper);
        return page ;
    }
}
