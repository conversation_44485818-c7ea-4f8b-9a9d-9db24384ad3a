package com.hl.building.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.building.domain.dto.BuildDetailReq;
import com.hl.building.domain.dto.CompanyDetailReq;
import com.hl.building.domain.dto.DistributionReq;
import com.hl.building.domain.dto.ListCompanyReq;
import com.hl.building.domain.tables.CompanyInfo;
import com.hl.building.domain.vo.CompanyDistributionVo;
import com.hl.building.domain.vo.CompanyInfoVo;
import com.hl.building.domain.vo.CompanyNatureVo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CompanyMapper extends BaseMapper<CompanyInfo> {
    List<CompanyDistributionVo> company_distribution(@Param("req") DistributionReq req);

    List<CompanyInfoVo> selectVoList(@Param("req") ListCompanyReq req);

    CompanyDistributionVo detail(@Param("req")CompanyDetailReq req);

    List<CompanyNatureVo> selectCompanyList();

    @MapKey(value = "house_nature")
    List<CompanyInfo>  selectCountNature(@Param("req") BuildDetailReq req);

    List<CompanyInfo> selectDelList();
}
