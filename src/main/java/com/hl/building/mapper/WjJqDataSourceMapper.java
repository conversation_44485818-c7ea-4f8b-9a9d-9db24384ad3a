package com.hl.building.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjLyJqxx;
import com.hl.building.domain.WjLyCaseInfo;
import com.hl.building.domain.vo.PoliceIncidentInfo;
import com.hl.common.config.datasource.DataSource;
import com.hl.common.config.datasource.DataSourceType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
@DataSource(value = DataSourceType.DATASOURCE1)
public interface WjJqDataSourceMapper {
    Page<JSONObject> getPoliceData(@Param("page")  Page<JSONObject> page, @Param("param") JSONObject param);

    List<PoliceIncidentInfo> queryPersonPoliceData(JSONObject param);

    Page<WjLyJqxx> queryJqInfoByJqLabel(@Param("page") Page<WjLyJqxx> page, @Param("param") Map<String, Object> param);

    Page<WjLyJqxx> queryJqInfoByJgbhList(@Param("page") Page<WjLyJqxx> page, @Param("jgbhList") List<String> jgbhList);

    List<String> queryJjbhByJqLabel(@Param("jqLabel") List<String> jqLabel);

    /**
     * 根据标签查询案件信息
     *
     * @param page 分页参数
     * @param param 查询参数
     * @return 案件信息分页结果
     */
    Page<WjLyCaseInfo> queryCaseInfoByLabel(@Param("page") Page<WjLyCaseInfo> page, @Param("param") Map<String, Object> param);

    /**
     * 根据身份证号查询案件信息
     *
     * @param page 分页参数
     * @param idCard 身份证号
     * @return 案件信息分页结果
     */
    Page<WjLyCaseInfo> queryCaseInfoByIdCard(@Param("page") Page<WjLyCaseInfo> page, @Param("idCard") String idCard);

    /**
     * 根据身份证号查询警情信息
     *
     * @param page 分页参数
     * @param idCard 身份证号
     * @return 警情信息分页结果
     */
    Page<WjLyJqxx> queryJqInfoByIdCard(@Param("page") Page<WjLyJqxx> page, @Param("idCard") String idCard);

    @Select("select * from dict where permission in ('jjcjn')")
    List<JSONObject> getDict();
}
