package com.hl.building.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjBuildingInfo;
import com.hl.building.domain.dto.ListBuildReq;
import com.hl.building.domain.vo.WjBuildingInfoRespVO;
import org.apache.ibatis.annotations.Param;

public interface WjBuildingInfoMapper extends BaseMapper<WjBuildingInfo> {


    Page<WjBuildingInfoRespVO> selectBuildingList(@Param("page") Page<WjBuildingInfoRespVO> pageParam,@Param("req") ListBuildReq req);
}