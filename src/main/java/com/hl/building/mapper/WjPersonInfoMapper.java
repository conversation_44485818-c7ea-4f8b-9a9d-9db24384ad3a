package com.hl.building.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjPersonInfo;
import com.hl.building.domain.vo.WjPersonInfoPageReqVO;
import com.hl.building.domain.vo.WjPersonInfoRespVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WjPersonInfoMapper extends BaseMapper<WjPersonInfo> {

    List<JSONObject> countPersonCountByCompanyId(@Param("companyIdList") List<String> companyIdList);

    Page<WjPersonInfoRespVO> selectPersonList(@Param("page") Page<WjPersonInfoRespVO> objectPage, @Param("req") WjPersonInfoPageReqVO req);
}