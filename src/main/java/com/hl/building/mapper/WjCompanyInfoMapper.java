package com.hl.building.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjCompanyInfo;
import com.hl.building.domain.dto.ListCompanyReq;
import com.hl.building.domain.vo.WjCompanyRespVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface WjCompanyInfoMapper extends BaseMapper<WjCompanyInfo> {
    Page<WjCompanyRespVO> pageCompanyList(@Param("page") Page<WjCompanyRespVO> objectPage, @Param("req") ListCompanyReq req);

    JSONObject countCompanyInfo(String buildId);

    List<WjCompanyRespVO> selectCompanyListByBuildId(@Param("req") Map<String, Object> req);

}