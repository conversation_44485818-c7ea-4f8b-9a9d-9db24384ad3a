package com.hl.building.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.building.domain.WjCompanyHouse;
import com.hl.building.domain.vo.WjCompanyHouseRespVO;
import com.hl.building.domain.vo.WjCompanyHouseVO;

import java.util.List;

public interface WjCompanyHouseMapper extends BaseMapper<WjCompanyHouse> {
    List<WjCompanyHouseVO> selectCompanyHouseList(List<String> req);

    List<WjCompanyHouseRespVO> selectCompanyHouseInfoList(List<String> companyIdList);


    List<JSONObject> countHouseNature(String buildId);
}