package com.hl.building.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjLyCaseInfo;
import com.hl.building.domain.dto.WjLyCaseInfoPageReq;
import org.apache.ibatis.annotations.Param;

public interface WjLyCaseInfoMapper extends BaseMapper<WjLyCaseInfo> {

    Page<WjLyCaseInfo> queryCaseInfoByIdCard(@Param("page") Page<WjLyCaseInfo> page, @Param("idCard") String idCard);

    Page<WjLyCaseInfo> pageList(@Param("page") Page<WjLyCaseInfo> page, @Param("req") WjLyCaseInfoPageReq req);

}