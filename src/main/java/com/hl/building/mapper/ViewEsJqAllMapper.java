package com.hl.building.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.building.domain.ViewEsJqAll;
import com.hl.common.config.datasource.DataSource;
import com.hl.common.config.datasource.DataSourceType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@DataSource(value = DataSourceType.DATASOURCE1)
@Mapper
public interface ViewEsJqAllMapper extends BaseMapper<ViewEsJqAll> {


    List<ViewEsJqAll> selectJqList(String idCard);
}