package com.hl.building.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.building.domain.dto.DistributionReq;
import com.hl.building.domain.dto.HouseDetailReq;
import com.hl.building.domain.dto.ListHouseReq;
import com.hl.building.domain.tables.HouseInfo;
import com.hl.building.domain.vo.HouseInfoVo;
import com.hl.building.domain.vo.ListHouseVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface HouseMapper extends BaseMapper<HouseInfo> {
    List<ListHouseVo> selectVoList(@Param("req") ListHouseReq req,@Param("ids") List<String> ids);

    List<HouseInfo> house_distribution(@Param("req") DistributionReq req);

    void deleteUpdHouse(@Param("houseNum") int houseNum, @Param("buildId") long buildId);

//    List<HouseInfoVo> company_distribution(@Param("req")DistributionReq req);

    HouseInfoVo detail(@Param("req")HouseDetailReq req);
}
