package com.hl.building.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.ViewPersonalTubeInfo;
import com.hl.building.domain.dto.ListPersonalTubeReq;
import org.apache.ibatis.annotations.Param;

public interface ViewPersonalTubeInfoMapper extends BaseMapper<ViewPersonalTubeInfo> {

    Page<ViewPersonalTubeInfo> pageList(@Param("page") Page<Object> of, @Param("req") ListPersonalTubeReq req);
}