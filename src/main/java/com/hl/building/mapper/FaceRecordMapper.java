package com.hl.building.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FaceRecordMapper {


    Page<JSONObject> getFaceRecord(@Param("page") Page<JSONObject> page, @Param("param") JSONObject param);


    /**
     * 查询涉警数量
     * @param idCardList
     * @return
     */
    List<JSONObject> queryPoliceData(@Param("idCardList") List<String> idCardList);


    /**
     * 查询涉案数量
     * @param idCard
     * @return
     */
    List<JSONObject> queryCaseData(@Param("idCardList") List<String> idCard);


    List<JSONObject> queryPersonPoliceRecord(JSONObject param);

    List<JSONObject> queryPersonCaseRecord(JSONObject param);
}
