package com.hl.building.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.building.domain.dto.ListPersonReq;
import com.hl.building.domain.tables.PersonInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface PersonMapper extends BaseMapper<PersonInfo> {

    List<PersonInfo> selectVoList(@Param("req") ListPersonReq req, @Param("ids") Set<Integer> ids);
}
