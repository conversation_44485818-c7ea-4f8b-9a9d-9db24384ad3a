package com.hl.building.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.WjLyJqxx;
import com.hl.building.domain.dto.WjLyJqxxPageReq;
import com.hl.building.domain.vo.WjLyJqxxPageRespVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WjLyJqxxMapper extends BaseMapper<WjLyJqxx> {
    Page<WjLyJqxxPageRespVO> pageList(@Param("page") Page<WjLyJqxxPageRespVO> of, @Param("req") WjLyJqxxPageReq req);

    List<JSONObject> countJqCount(@Param("jgbhList") List<String> companyIdList);
}