package com.hl.building.trans;


import com.hl.building.service.JqTransService;
import com.hl.translation.annotation.TranslationType;
import com.hl.translation.core.TranslationInterface;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@AllArgsConstructor
@Configuration
@TranslationType(type = TransConstants.JQ_TRANS)
public class JqTransTransImpl implements TranslationInterface<String> {

    @Resource
    private JqTransService jqTransService;

    @Override
    public String translation(Object o, String s) {
        String dictValue = o.toString();
        return jqTransService.getDictName(JqTransService.DictType.JJCJN, dictValue);
    }
}
