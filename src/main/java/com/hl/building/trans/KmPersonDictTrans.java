package com.hl.building.trans;

import cn.hutool.core.util.StrUtil;
import com.hl.building.domain.KmPersonalDictionary;
import com.hl.translation.annotation.TranslationType;
import com.hl.translation.core.TranslationInterface;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@TranslationType(type = TransConstants.KM_PERSON_DICT_TRANS)
@Configuration
public class KmPersonDictTrans implements TranslationInterface<String> {


    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public String translation(Object o, String s) {
        if (o instanceof String) {
            List<KmPersonalDictionary> personalDictionaries = (List<KmPersonalDictionary>) redisTemplate.opsForHash().get(TransConstants.KM_PERSON_DICT_TRANS, s.toUpperCase());
            if (personalDictionaries != null) {
                if ("st_zdryjzfl".equals(s)) {
                    // 特殊处理一下
                    List<String> jzsxList = new ArrayList<>();
                    String[] split = o.toString().split(",");
                    for (String string : split) {
                        String value = personalDictionaries.stream()
                                .filter(dictionary -> dictionary.getZddm().equals(string))
                                .findFirst()
                                .map(KmPersonalDictionary::getZdmc)
                                .orElse(null);
                        if (StrUtil.isNotBlank(value)) {
                            jzsxList.add(value);
                        }
                    }
                    return String.join(",", jzsxList);
                }
                return personalDictionaries.stream()
                        .filter(dictionary -> dictionary.getZddm().equals(o))
                        .findFirst()
                        .map(KmPersonalDictionary::getZdmc)
                        .orElse(null);
            }
        }
        return null;
    }
}
