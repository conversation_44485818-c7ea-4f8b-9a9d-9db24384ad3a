package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 公司信息
 */
@ApiModel(description = "公司信息")
@Data
@TableName(value = "wjhl.wj_company_info", autoResultMap = true)
public class WjCompanyInfo {
    /**
     * 公司编号
     */
    @TableId(value = "company_id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "公司编号")
    private String companyId;

    /**
     * 公司名称
     */
    @TableField(value = "company_name")
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 统一社会信用代码
     */
    @TableField(value = "company_code")
    @ApiModelProperty(value = "统一社会信用代码")
    private String companyCode;

    /**
     * 公司地址
     */
    @TableField(value = "company_address")
    @ApiModelProperty(value = "公司地址")
    private String companyAddress;

    /**
     * 0未注销，1注销
     */
    @TableField(value = "is_logout")
    @ApiModelProperty(value = "0在业，1停业 2 注销 3 吊销后未注销 4 企业直接申请注销 5 吊销后注销")
    private Integer isLogout;

    /**
     * 0非重点企业，1重点企业
     */
    @TableField(value = "is_focus")
    @ApiModelProperty(value = "0非重点企业，1重点企业")
    private Integer isFocus;

    /**
     * 0未删除，1已删除
     */
    @TableField(value = "is_delete")
    @ApiModelProperty(value = "0未删除，1已删除")
    @TableLogic
    private Integer isDelete;

    /**
     * 创建用户
     */
    @TableField(value = "create_user")
    @ApiModelProperty(value = "创建用户")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新用户
     */
    @TableField(value = "update_user")
    @ApiModelProperty(value = "更新用户")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(value = "build_id")
    @ApiModelProperty(value = "楼宇id")
    private String buildId;

    @TableField(value = "jq_label", typeHandler = Fastjson2TypeHandler.class)
    private List<String> jqLabel;


    /**
     * 全景机构编号
     */
    @TableField(value = "jgbh")
    @ApiModelProperty(value = "全景机构编号")
    private String jgbh;

    /**
     * 全景标准地址
     */
    @TableField(value = "dz_id")
    @ApiModelProperty(value = "全景标准地址")
    private String dzId;

    /**
     * 全景地址名称
     */
    @TableField(value = "dzmc")
    @ApiModelProperty(value = "全景地址名称")
    private String dzmc;

    /**
     * 工商注册名称
     */
    @TableField(value = "gszcmc")
    @ApiModelProperty(value = "工商注册名称")
    private String gszcmc;

    /**
     * 注册资本
     */
    @TableField(value = "zczb")
    @ApiModelProperty(value = "注册资本")
    private String zczb;

    /**
     * 注册日期
     */
    @TableField(value = "zcrq")
    @ApiModelProperty(value = "注册日期")
    private String zcrq;

    /**
     * 有效期限
     */
    @TableField(value = "yxqx")
    @ApiModelProperty(value = "有效期限")
    private String yxqx;

    /**
     * 工商登记时间
     */
    @TableField(value = "gsdjsj")
    @ApiModelProperty(value = "工商登记时间")
    private String gsdjsj;

    /**
     * 发证时间
     */
    @TableField(value = "fzsj")
    @ApiModelProperty(value = "发证时间")
    private String fzsj;

    /**
     * 注册地址
     */
    @TableField(value = "zcdz")
    @ApiModelProperty(value = "注册地址")
    private String zcdz;

    /**
     * 经营范围
     */
    @TableField(value = "jyfw")
    @ApiModelProperty(value = "经营范围")
    private String jyfw;

    /**
     * 税务登记号
     */
    @TableField(value = "swdjh")
    @ApiModelProperty(value = "税务登记号")
    private String swdjh;

    /**
     * 状态
     */
    @TableField(value = "zt")
    @ApiModelProperty(value = "状态")
    private String zt;

    /**
     * 楼宇名称
     */
    @TableField(value = "build_name")
    @ApiModelProperty(value = "楼宇名称")
    private String buildName;
}