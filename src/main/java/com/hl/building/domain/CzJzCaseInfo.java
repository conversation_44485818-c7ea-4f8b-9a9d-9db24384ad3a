package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "harzon.cz_jz_case_info")
public class CzJzCaseInfo {
    @TableId(value = "uuid", type = IdType.INPUT)
    private String uuid;

    /**
     * 案件编号
     */
    @TableField(value = "case_no")
    private String caseNo;

    /**
     * 案件名称
     */
    @TableField(value = "case_name")
    private String caseName;

    /**
     * 简要案情
     */
    @TableField(value = "case_summary")
    private String caseSummary;

    /**
     * 案发地点
     */
    @TableField(value = "case_address")
    private String caseAddress;

    /**
     * 案发开始时间
     */
    @TableField(value = "case_start_time")
    private Date caseStartTime;

    /**
     * 案发结束时间
     */
    @TableField(value = "case_end_time")
    private Date caseEndTime;

    /**
     * 报案人手机号码
     */
    @TableField(value = "case_report_phone")
    private String caseReportPhone;

    /**
     * 报案人姓名
     */
    @TableField(value = "case_report_name")
    private String caseReportName;

    /**
     * 报案时间
     */
    @TableField(value = "case_report_time")
    private Date caseReportTime;

    /**
     * 受理人用户名
     */
    @TableField(value = "case_accept_uid")
    private String caseAcceptUid;

    /**
     * 受理人名称
     */
    @TableField(value = "case_accept_name")
    private String caseAcceptName;

    /**
     * 受理单位代码
     */
    @TableField(value = "case_accept_dep_code")
    private String caseAcceptDepCode;

    /**
     * 受理单位名称
     */
    @TableField(value = "case_accept_dep_name")
    private String caseAcceptDepName;

    /**
     * 受理时间
     */
    @TableField(value = "case_accept_time")
    private Date caseAcceptTime;

    /**
     * 立案审批用户名
     */
    @TableField(value = "register_approval_uid")
    private String registerApprovalUid;

    /**
     * 立案审批人名称
     */
    @TableField(value = "register_approval_name")
    private String registerApprovalName;

    /**
     * 立案审批单位代码
     */
    @TableField(value = "register_approval_dep_code")
    private String registerApprovalDepCode;

    /**
     * 立案审批单位名称
     */
    @TableField(value = "register_approval_dep_name")
    private String registerApprovalDepName;

    /**
     * 立案审批名时间
     */
    @TableField(value = "register_approval_time")
    private Date registerApprovalTime;

    /**
     * 破案审批人用户名
     */
    @TableField(value = "solve_approval_uid")
    private String solveApprovalUid;

    /**
     * 破案审批人名称
     */
    @TableField(value = "solve_approval_name")
    private String solveApprovalName;

    /**
     * 破案审批人单位代码
     */
    @TableField(value = "solve_approval_dep_code")
    private String solveApprovalDepCode;

    /**
     * 破案审批人单位名称
     */
    @TableField(value = "solve_approval_dep_name")
    private String solveApprovalDepName;

    /**
     * 破案单位代码
     */
    @TableField(value = "solve_dep_code")
    private String solveDepCode;

    /**
     * 破案单位名称
     */
    @TableField(value = "solve_dep_name")
    private String solveDepName;

    /**
     * 破案时间
     */
    @TableField(value = "solve_time")
    private Date solveTime;

    /**
     * 案件大类
     */
    @TableField(value = "case_main_class")
    private String caseMainClass;

    /**
     * 案件类别
     */
    @TableField(value = "case_type")
    private String caseType;

    /**
     * 案件副案别
     */
    @TableField(value = "case_sub_type")
    private String caseSubType;

    /**
     * 案件状态
     */
    @TableField(value = "case_state")
    private String caseState;

    /**
     * 案件性质
     */
    @TableField(value = "case_nature")
    private String caseNature;

    /**
     * 创建用户id
     */
    @TableField(value = "create_uid")
    private String createUid;

    /**
     * 创建用户
     */
    @TableField(value = "create_name")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 初次入库时间
     */
    @TableField(value = "scrk_time")
    private Date scrkTime;

    /**
     * 涉案总价值
     */
    @TableField(value = "value_involved")
    private String valueInvolved;

    /**
     * 报警人性别
     */
    @TableField(value = "case_report_gender")
    private String caseReportGender;

    /**
     * 报警人出生年月
     */
    @TableField(value = "case_report_birthday")
    private String caseReportBirthday;

    /**
     * 报警人证件号码
     */
    @TableField(value = "case_report_id_card")
    private String caseReportIdCard;

    /**
     * 报警人单位名称
     */
    @TableField(value = "case_report_unit")
    private String caseReportUnit;

    /**
     * 报警人地址信息
     */
    @TableField(value = "case_report_address")
    private String caseReportAddress;

    /**
     * 指派状态
     */
    @TableField(value = "assign_state")
    private Short assignState;

    /**
     * 破案方式
     */
    @TableField(value = "solve_way")
    private String solveWay;

    /**
     * 破案类型
     */
    @TableField(value = "solve_type")
    private String solveType;

    /**
     * 破案简况
     */
    @TableField(value = "solve_brief")
    private String solveBrief;

    /**
     * 案情综述
     */
    @TableField(value = "case_review")
    private String caseReview;

    /**
     * 犯罪主体类型
     */
    @TableField(value = "crime_sub_type")
    private String crimeSubType;

    /**
     * 经济损失
     */
    @TableField(value = "economic_loss")
    private Long economicLoss;

    /**
     * 挽回总价值
     */
    @TableField(value = "save_involved")
    private Long saveInvolved;

    /**
     * 缴获总价值
     */
    @TableField(value = "capture_involved")
    private Long captureInvolved;

    /**
     * 案件主办人标识
     */
    @TableField(value = "major_user_uid")
    private String majorUserUid;

    /**
     * 案件主办单位编码
     */
    @TableField(value = "major_unit_code")
    private String majorUnitCode;

    /**
     * 主办电话
     */
    @TableField(value = "major_phone")
    private String majorPhone;

    /**
     * 案件主办人名称
     */
    @TableField(value = "major_user_name")
    private String majorUserName;

    /**
     * 案件主办单位名称
     */
    @TableField(value = "major_unit_name")
    private String majorUnitName;

    /**
     * 案件主办人警号
     */
    @TableField(value = "major_police_number")
    private String majorPoliceNumber;

    /**
     * 主办人姓名
     */
    @TableField(value = "sponsor_name")
    private String sponsorName;

    /**
     * 协办人姓名
     */
    @TableField(value = "assistant_name")
    private String assistantName;

    /**
     * 受理人
     */
    @TableField(value = "acceptor_name")
    private String acceptorName;

    /**
     * 变更时间
     */
    @TableField(value = "case_bg_time")
    private String caseBgTime;
}