package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 云管控重点人员字典表
 */
@ApiModel(description="云管控重点人员字典表")
@Data
@TableName(value = "harzon.km_personal_dictionary")
public class KmPersonalDictionary {
    @TableField(value = "id")
    @ApiModelProperty(value="")
    private String id;

    @TableField(value = "glid")
    @ApiModelProperty(value="")
    private String glid;

    @TableField(value = "nbbsf")
    @ApiModelProperty(value="")
    private String nbbsf;

    @TableField(value = "zdlbdm")
    @ApiModelProperty(value="")
    private String zdlbdm;

    @TableField(value = "zdlbmc")
    @ApiModelProperty(value="")
    private String zdlbmc;

    @TableField(value = "zddm")
    @ApiModelProperty(value="")
    private String zddm;

    @TableField(value = "zdmc")
    @ApiModelProperty(value="")
    private String zdmc;

    @TableField(value = "fzddm")
    @ApiModelProperty(value="")
    private String fzddm;

    @TableField(value = "fzdmc")
    @ApiModelProperty(value="")
    private String fzdmc;

    @TableField(value = "gflylb")
    @ApiModelProperty(value="")
    private String gflylb;

    @TableField(value = "sm")
    @ApiModelProperty(value="")
    private String sm;

    @TableField(value = "zt")
    @ApiModelProperty(value="")
    private String zt;

    @TableField(value = "xh")
    @ApiModelProperty(value="")
    private String xh;

    @TableField(value = "qyrq")
    @ApiModelProperty(value="")
    private Date qyrq;

    @TableField(value = "zxrq")
    @ApiModelProperty(value="")
    private Date zxrq;

    @TableField(value = "djdw_gajgjgdm")
    @ApiModelProperty(value="")
    private String djdwGajgjgdm;

    @TableField(value = "djdw_gajgmc")
    @ApiModelProperty(value="")
    private String djdwGajgmc;

    @TableField(value = "djr_yhm")
    @ApiModelProperty(value="")
    private String djrYhm;

    @TableField(value = "djsj")
    @ApiModelProperty(value="")
    private Date djsj;

    @TableField(value = "sjgsdwmc")
    @ApiModelProperty(value="")
    private String sjgsdwmc;

    @TableField(value = "djbgsj")
    @ApiModelProperty(value="")
    private Date djbgsj;

    @TableField(value = "djbgbs")
    @ApiModelProperty(value="")
    private String djbgbs;

    @TableField(value = "shcjbs")
    @ApiModelProperty(value="")
    private String shcjbs;

    @TableField(value = "sjjzbs")
    @ApiModelProperty(value="")
    private String sjjzbs;

    @TableField(value = "jlmj")
    @ApiModelProperty(value="")
    private String jlmj;

    @TableField(value = "sjgsry")
    @ApiModelProperty(value="")
    private String sjgsry;

    @TableField(value = "sjgsdwdm")
    @ApiModelProperty(value="")
    private String sjgsdwdm;

    @TableField(value = "jc")
    @ApiModelProperty(value="")
    private String jc;
}