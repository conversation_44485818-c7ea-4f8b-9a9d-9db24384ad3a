package com.hl.building.domain;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 楼宇数据源信息
 */
@ApiModel(description="楼宇数据源信息")
@Data
@TableName(value = "wjhl.wj_ly_datasource_info",autoResultMap = true)
public class WjLyDatasourceInfo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 表名
     */
    @TableField(value = "\"table_name\"")
    @ApiModelProperty(value="表名")
    private String tableName;

    /**
     * 数据源名称
     */
    @TableField(value = "datasource_name")
    @ApiModelProperty(value="数据源名称")
    private String datasourceName;

    /**
     * 字段信息
     */
    @TableField(value = "\"columns\"",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value="字段信息")
    private JSONArray columns;
}