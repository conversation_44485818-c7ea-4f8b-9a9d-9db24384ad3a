package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "wjhl.view_camera_face_record")
public class ViewCameraFaceRecord {
    @TableField(value = "id")
    private String id;

    @TableField(value = "face_image_id")
    private String faceImageId;

    @TableField(value = "pic_id")
    private String picId;

    @TableField(value = "\"name\"")
    private String name;

    @TableField(value = "id_card")
    private String idCard;

    @TableField(value = "appear_time")
    private Date appearTime;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "similarity")
    private String similarity;

    @TableField(value = "global_face_image_uri")
    private String globalFaceImageUri;

    @TableField(value = "camera_name")
    private String cameraName;

    @TableField(value = "camera_id")
    private String cameraId;

    @TableField(value = "inter_code")
    private String interCode;
}