package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "wj_ly_jqxx")
@Data
@TableName(value = "wjhl.wj_ly_jqxx",autoResultMap = true)
public class WjLyJqxx {
    @TableId(value = "jjbh", type = IdType.AUTO)
    @ApiModelProperty(value = "")
    private String jjbh;

    @TableField(value = "cjbs")
    @ApiModelProperty(value = "")
    private String cjbs;

    @TableField(value = "bjr")
    @ApiModelProperty(value = "")
    private String bjr;

    @TableField(value = "bjlx")
    @ApiModelProperty(value = "")
    private String bjlx;

    @TableField(value = "jjdwmc")
    @ApiModelProperty(value = "")
    private String jjdwmc;

    @TableField(value = "bjdhsj_time")
    @ApiModelProperty(value = "")
    private String bjdhsjTime;

    @TableField(value = "jjdw")
    @ApiModelProperty(value = "")
    private String jjdw;

    @TableField(value = "lxdh")
    @ApiModelProperty(value = "")
    private String lxdh;

    @TableField(value = "bjnr")
    @ApiModelProperty(value = "")
    private String bjnr;

    @TableField(value = "djdwmc")
    @ApiModelProperty(value = "")
    private String djdwmc;

    @TableField(value = "sfdd")
    @ApiModelProperty(value = "")
    private String sfdd;

    @TableField(value = "bjxs")
    @ApiModelProperty(value = "")
    private String bjxs;

    @TableField(value = "jjdbh")
    @ApiModelProperty(value = "")
    private String jjdbh;

    @TableField(value = "bjdhsj")
    @ApiModelProperty(value = "")
    private Date bjdhsj;

    @TableField(value = "jjrqsj")
    @ApiModelProperty(value = "")
    private String jjrqsj;

    @TableField(value = "jqdj")
    @ApiModelProperty(value = "")
    private String jqdj;

    @TableField(value = "gis_x")
    @ApiModelProperty(value = "")
    private String gisX;

    @TableField(value = "gis_y")
    @ApiModelProperty(value = "")
    private String gisY;

    @TableField(value = "sfxq")
    @ApiModelProperty(value = "")
    private String sfxq;

    @TableField(value = "cljgnr")
    @ApiModelProperty(value = "")
    private String cljgnr;

    @TableField(value = "djr")
    @ApiModelProperty(value = "")
    private String djr;

    @TableField(value = "jqsx")
    @ApiModelProperty(value = "")
    private String jqsx;

    @TableField(value = "tqqk")
    @ApiModelProperty(value = "")
    private String tqqk;

    @TableField(value = "zrq")
    @ApiModelProperty(value = "")
    private String zrq;

    @TableField(value = "cjbh")
    @ApiModelProperty(value = "")
    private String cjbh;

    @TableField(value = "cjsj_time")
    @ApiModelProperty(value = "")
    private String cjsjTime;

    @TableField(value = "cjlb")
    @ApiModelProperty(value = "")
    private String cjlb;

    @TableField(value = "cjdw")
    @ApiModelProperty(value = "")
    private String cjdw;

    @TableField(value = "sfcs")
    @ApiModelProperty(value = "")
    private String sfcs;

    @TableField(value = "ssxxqk")
    @ApiModelProperty(value = "")
    private String ssxxqk;

    @TableField(value = "bccljg")
    @ApiModelProperty(value = "")
    private String bccljg;

    @TableField(value = "cjr")
    @ApiModelProperty(value = "")
    private String cjr;

    @TableField(value = "cjxz")
    @ApiModelProperty(value = "")
    private String cjxz;

    @TableField(value = "cjdwmc")
    @ApiModelProperty(value = "")
    private String cjdwmc;

    @TableField(value = "cjjg")
    @ApiModelProperty(value = "")
    private String cjjg;

    @TableField(value = "cjsj")
    @ApiModelProperty(value = "")
    private Date cjsj;

    @TableField(value = "company",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "")
    private List<String> company;

    @TableField(value = "building",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "")
    private List<String> building;

    @TableField(value = "person",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "")
    private List<String> person;

    @TableField(value = "company_info",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "")
    private List<String> companyInfo;

    @TableField(value = "building_info",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "")
    private List<String> buildingInfo;

    @TableField(value = "person_info",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "")
    private List<String> personInfo;

    @TableField(value = "jgbh")
    private String jgbh;
}