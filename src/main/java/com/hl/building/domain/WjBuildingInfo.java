package com.hl.building.domain;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.hl.es.domain.dto.JqCountDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description="wj_building_info")
@Data
@TableName(value = "wjhl.wj_building_info",autoResultMap = true)
public class WjBuildingInfo {
    /**
     * 主键id
     */
    @TableId(value = "build_id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value="主键id")
    private String buildId;

    /**
     * 楼宇名称
     */
    @TableField(value = "build_name")
    @ApiModelProperty(value="楼宇名称")
    private String buildName;

    /**
     * 地址
     */
    @TableField(value = "address")
    @ApiModelProperty(value="地址")
    private String address;

    /**
     * 楼宇层数
     */
    @TableField(value = "layer_num")
    @ApiModelProperty(value="楼宇层数")
    private Integer layerNum;

    /**
     * 每层网格数
     */
    @TableField(value = "grid_x")
    @ApiModelProperty(value="每层网格数")
    private Integer gridX;

    /**
     * 每层网格列数
     */
    @TableField(value = "grid_y")
    @ApiModelProperty(value="每层网格列数")
    private Integer gridY;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 创建用户
     */
    @TableField(value = "create_user")
    @ApiModelProperty(value="创建用户")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 更新用户
     */
    @TableField(value = "update_user")
    @ApiModelProperty(value="更新用户")
    private String updateUser;

    /**
     * 是否删除
     */
    @TableField(value = "is_delete")
    @ApiModelProperty(value="是否删除")
    @TableLogic
    private Integer isDelete;


    @TableField(exist = false)
    private List<JSONObject> houseNature;


    @TableField(exist = false)
    private List<JSONObject> statistics;



    @TableField(value = "device_info",typeHandler = Fastjson2TypeHandler.class)
    private List<String> deviceInfo;


    @TableField(value = "jq_label",typeHandler = Fastjson2TypeHandler.class)
    private List<String> jqLabel;

    /**
     * 楼宇类型
     */
    @TableField(value = "build_type")
    @ApiModelProperty(value="楼宇类型")
    private String buildType;

    /**
     * 所属辖区
     */
    @TableField(value = "organization_id")
    @ApiModelProperty(value="所属辖区")
    private String organizationId;

    /**
     * 所属辖区名称
     */
    @TableField(value = "organization_name")
    @ApiModelProperty(value="所属辖区名称")
    private String organizationName;


    @TableField(value = "jq_count",typeHandler = Fastjson2TypeHandler.class)
    private JqCountDTO jqCount;

}