package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hl.building.trans.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description = "view_personal_tube_info")
@Data
@TableName(value = "harzon.view_personal_tube_info")
public class ViewPersonalTubeInfo {
    @TableField(value = "\"row_number\"")
    @ApiModelProperty(value = "")
    private Long rowNumber;

    @TableField(value = "xxzjbh")
    @ApiModelProperty(value = "")
    private String xxzjbh;

    @TableField(value = "rkbm")
    @ApiModelProperty(value = "")
    private String rkbm;

    @TableField(value = "bj_xxzjbh")
    @ApiModelProperty(value = "")
    private String bjXxzjbh;

    @TableField(value = "gmsfhm")
    @ApiModelProperty(value = "公民身份号码")
    private String gmsfhm;

    @TableField(value = "zdryglztdm")
    @ApiModelProperty(value = "")
    private String zdryglztdm;

    @TableField(value = "zdrylbdm")
    @ApiModelProperty(value = "")
    private String zdrylbdm;

    @TableField(value = "zdryjzlbdm")
    @ApiModelProperty(value = "")
    private String zdryjzlbdm;

    @TableField(value = "gljbdm")
    @ApiModelProperty(value = "")
    private String gljbdm;

    @TableField(value = "lglydm")
    @ApiModelProperty(value = "")
    private String lglydm;

    @TableField(value = "lgr_yhm")
    @ApiModelProperty(value = "")
    private String lgrYhm;

    @TableField(value = "lgsj_rqsj")
    @ApiModelProperty(value = "")
    private Date lgsjRqsj;

    @TableField(value = "lgr_xm")
    @ApiModelProperty(value = "")
    private String lgrXm;

    @TableField(value = "lgdw_gajgmc")
    @ApiModelProperty(value = "列管单位")
    private String lgdwGajgmc;

    @TableField(value = "lgyj_yjsm")
    @ApiModelProperty(value = "列管依据")
    private String lgyjYjsm;

    @TableField(value = "cgr_yhm")
    @ApiModelProperty(value = "")
    private String cgrYhm;

    @TableField(value = "cgsj_rqsj")
    @ApiModelProperty(value = "")
    private Date cgsjRqsj;

    @TableField(value = "cgr_xm")
    @ApiModelProperty(value = "")
    private String cgrXm;

    @TableField(value = "cgdw_gajgmc")
    @ApiModelProperty(value = "")
    private String cgdwGajgmc;

    @TableField(value = "cgyj_yjsm")
    @ApiModelProperty(value = "")
    private String cgyjYjsm;

    @TableField(value = "cgyydm")
    @ApiModelProperty(value = "")
    private String cgyydm;

    @TableField(value = "bz")
    @ApiModelProperty(value = "")
    private String bz;

    @TableField(value = "zdrylglydm")
    @ApiModelProperty(value = "")
    private String zdrylglydm;

    @TableField(value = "ajlbdm")
    @ApiModelProperty(value = "")
    private String ajlbdm;

    @TableField(value = "zrmj_yhm")
    @ApiModelProperty(value = "")
    private String zrmjYhm;

    @TableField(value = "zrmj_lxdh")
    @ApiModelProperty(value = "")
    private String zrmjLxdh;

    @TableField(value = "zdryzkztdm")
    @ApiModelProperty(value = "")
    private String zdryzkztdm;

    @TableField(value = "zdrysjlydm")
    @ApiModelProperty(value = "")
    private String zdrysjlydm;

    @TableField(value = "xgldw_gajgjgdm")
    @ApiModelProperty(value = "")
    private String xgldwGajgjgdm;

    @TableField(value = "xgldw_gajgmc")
    @ApiModelProperty(value = "")
    private String xgldwGajgmc;

    @TableField(value = "bc_lgr_yhm")
    @ApiModelProperty(value = "")
    private String bcLgrYhm;

    @TableField(value = "bc_lgsj_rqsj")
    @ApiModelProperty(value = "")
    private Date bcLgsjRqsj;

    @TableField(value = "bc_lgr_xm")
    @ApiModelProperty(value = "")
    private String bcLgrXm;

    @TableField(value = "bc_lgdw_gajgmc")
    @ApiModelProperty(value = "")
    private String bcLgdwGajgmc;

    @TableField(value = "bc_lgyj_yjsm")
    @ApiModelProperty(value = "")
    private String bcLgyjYjsm;

    @TableField(value = "zdryztdm")
    @ApiModelProperty(value = "")
    private String zdryztdm;

    @TableField(value = "glbh_xxzjbh")
    @ApiModelProperty(value = "")
    private String glbhXxzjbh;

    @TableField(value = "zdrycgyydm")
    @ApiModelProperty(value = "")
    private String zdrycgyydm;

    @TableField(value = "bc_lgdw_gajgjgdm")
    @ApiModelProperty(value = "")
    private String bcLgdwGajgjgdm;

    @TableField(value = "sfyx_pdbz")
    @ApiModelProperty(value = "")
    private String sfyxPdbz;

    @TableField(value = "sflylbdm")
    @ApiModelProperty(value = "")
    private String sflylbdm;

    @TableField(value = "wtztdm")
    @ApiModelProperty(value = "")
    private String wtztdm;

    @TableField(value = "xgldwlbdm")
    @ApiModelProperty(value = "")
    private String xgldwlbdm;

    @TableField(value = "bdtssj_rqsj")
    @ApiModelProperty(value = "")
    private Date bdtssjRqsj;

    @TableField(value = "djdw_gajgjgdm")
    @ApiModelProperty(value = "")
    private String djdwGajgjgdm;

    @TableField(value = "djbgbs")
    @ApiModelProperty(value = "")
    private String djbgbs;

    @TableField(value = "djr_yhm")
    @ApiModelProperty(value = "")
    private String djrYhm;

    @TableField(value = "djsj")
    @ApiModelProperty(value = "")
    private Date djsj;

    @TableField(value = "djr_xm")
    @ApiModelProperty(value = "")
    private String djrXm;

    @TableField(value = "djdw_gajgmc")
    @ApiModelProperty(value = "")
    private String djdwGajgmc;

    @TableField(value = "djbgsj")
    @ApiModelProperty(value = "")
    private Date djbgsj;

    @TableField(value = "sjgsry")
    @ApiModelProperty(value = "")
    private String sjgsry;

    @TableField(value = "sjgsdwdm")
    @ApiModelProperty(value = "")
    private String sjgsdwdm;

    @TableField(value = "sgr_xm")
    @ApiModelProperty(value = "")
    private String sgrXm;

    @TableField(value = "xgr_xm")
    @ApiModelProperty(value = "")
    private String xgrXm;

    @TableField(value = "sjgsdwmc")
    @ApiModelProperty(value = "")
    private String sjgsdwmc;

    @TableField(value = "sjly_jyqk")
    @ApiModelProperty(value = "")
    private String sjlyJyqk;

    @TableField(value = "xm")
    @ApiModelProperty(value = "姓名")
    private String xm;

    @TableField(value = "xszt_jyqk")
    @ApiModelProperty(value = "")
    private String xsztJyqk;

    @TableField(value = "gkdjdm")
    @ApiModelProperty(value = "")
    private String gkdjdm;

    @TableField(value = "sflydm")
    @ApiModelProperty(value = "")
    private String sflydm;

    @TableField(value = "swry_sfswrydm")
    @ApiModelProperty(value = "")
    private String swrySfswrydm;

    @TableField(value = "lgdw_gajgjgdm")
    @ApiModelProperty(value = "")
    private String lgdwGajgjgdm;

    @TableField(value = "swry_jkjbdm")
    @ApiModelProperty(value = "")
    private String swryJkjbdm;

    @TableField(value = "swry_swlbdm")
    @ApiModelProperty(value = "")
    private String swrySwlbdm;

    @TableField(value = "ccxd_xdrq")
    @ApiModelProperty(value = "")
    private Date ccxdXdrq;

    @TableField(value = "lydpzldm")
    @ApiModelProperty(value = "")
    private String lydpzldm;

    @TableField(value = "gzbx_jyqk")
    @ApiModelProperty(value = "")
    private String gzbxJyqk;

    @TableField(value = "saqk_jyqk")
    @ApiModelProperty(value = "")
    private String saqkJyqk;

    @TableField(value = "sfcg_pdbz")
    @ApiModelProperty(value = "")
    private String sfcgPdbz;

    @TableField(value = "cgdw_gajgjgdm")
    @ApiModelProperty(value = "")
    private String cgdwGajgjgdm;

    @TableField(value = "cgxx_jyqk")
    @ApiModelProperty(value = "")
    private String cgxxJyqk;

    @TableField(value = "sfjd_pdbz")
    @ApiModelProperty(value = "")
    private String sfjdPdbz;

    @TableField(value = "sflybdm")
    @ApiModelProperty(value = "")
    private String sflybdm;

    @TableField(value = "sfjbdm")
    @ApiModelProperty(value = "")
    private String sfjbdm;

    @TableField(value = "sfzd_pdbz")
    @ApiModelProperty(value = "")
    private String sfzdPdbz;

    @TableField(value = "sfbq_pdbz")
    @ApiModelProperty(value = "")
    private String sfbqPdbz;

    @TableField(value = "sfslg_pdbz")
    @ApiModelProperty(value = "")
    private String sfslgPdbz;

    @TableField(value = "xzzdrylydm")
    @ApiModelProperty(value = "")
    private String xzzdrylydm;

    @TableField(value = "wgy_xm")
    @ApiModelProperty(value = "")
    private String wgyXm;

    @TableField(value = "wgy_lxdh")
    @ApiModelProperty(value = "")
    private String wgyLxdh;

    @TableField(value = "zdryhjsxdm")
    @ApiModelProperty(value = "")
    private String zdryhjsxdm;

    @TableField(value = "zdrysfsxdm")
    @ApiModelProperty(value = "")
    private String zdrysfsxdm;

    @TableField(value = "hjdz_qhnxxdz")
    @ApiModelProperty(value = "")
    private String hjdzQhnxxdz;

    @TableField(value = "xzz_qhnxxdz")
    @ApiModelProperty(value = "")
    private String xzzQhnxxdz;

    @TableField(value = "image_url")
    @ApiModelProperty(value = "")
    private String imageUrl;

    @TableField(value = "sjly")
    @ApiModelProperty(value = "")
    private Long sjly;


    @TableField(exist = false)
    @Translation(type = TransConstants.KM_PERSON_DICT_TRANS, mapper = "gljbdm", other = "st_zdrygljb")
    @ApiModelProperty(value = "管理级别")
    private String lgjbms;

    @TableField(exist = false)
    @ApiModelProperty(value = "管理状态")
    @Translation(type = TransConstants.KM_PERSON_DICT_TRANS, mapper = "zdryglztdm", other = "st_zdrygkzt")
    private String glztms;

    @TableField(exist = false)
    @ApiModelProperty(value = "警种属性")
    @Translation(type = TransConstants.KM_PERSON_DICT_TRANS, mapper = "zdryjzlbdm", other = "st_zdryjzfl")
    private String jzsxms;
}