package com.hl.building.domain.tables;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@TableName(value = "company_info",autoResultMap = true)
public class CompanyInfo {

  @ApiModelProperty("公司编号")
  @TableId
  private long companyId;
  @ApiModelProperty("楼宇编号")
  private long buildId;
  @ApiModelProperty("户室性质")
  private String houseNature;
  @ApiModelProperty("公司名称")
  private String companyName;
  @ApiModelProperty("机构编号")
  private String organization;
  @ApiModelProperty("其他人员")
  @TableField(value="other_person",typeHandler = JacksonTypeHandler.class)
  private List<Integer> otherPerson;
  @ApiModelProperty("企业法人")
  @TableField(value="corporate",typeHandler = JacksonTypeHandler.class)
  private List<Integer> corporate;
  @ApiModelProperty("企业高管")
  @TableField(value="executives",typeHandler = JacksonTypeHandler.class)
  private List<Integer> executives;
  @ApiModelProperty("财务人员")
  @TableField(value="finance",typeHandler = JacksonTypeHandler.class)
  private List<Integer> finance;

  @ApiModelProperty("其他人员详情")
  @TableField(exist = false)
  private List<PersonInfo> otherPersonInfo;
  @ApiModelProperty("企业法人详情")
  @TableField(exist = false)
  private List<PersonInfo> corporateInfo;
  @ApiModelProperty("企业高管详情")
  @TableField(exist = false)
  private List<PersonInfo> executivesInfo;
  @ApiModelProperty("财务人员详情")
  @TableField(exist = false)
  private List<PersonInfo> financeInfo;

  @ApiModelProperty("0未删除，1已删除")
  @TableLogic
  private int isDelete = 0;
//  @ApiModelProperty("户室详情")
//  @TableField(exist = false)
//  private List<HouseInfo> houseInfoList;
  @ApiModelProperty("户室id组成的数组")
  @TableField(value="house_id",typeHandler = JacksonTypeHandler.class)
  private List<Integer> houseId;
  @ApiModelProperty("涉警信息")
  private String sjInfo;
  @ApiModelProperty("涉案信息")
  private String saInfo;
  @ApiModelProperty("统一社会信用代码")
  private String companyCode;
  @ApiModelProperty("0未注销，1注销")
  private int isLogout = 0;
  @ApiModelProperty("0非重点企业，1重点企业")
  private int isFocus = 0;

}
