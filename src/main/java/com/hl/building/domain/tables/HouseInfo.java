package com.hl.building.domain.tables;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.hl.building.domain.vo.CompanyNatureVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@TableName(value = "house_info",autoResultMap = true)
public class HouseInfo {

  @ApiModelProperty("主键")
  @TableId(type = IdType.AUTO)
  private long id;
  @ApiModelProperty("所属楼宇")
  private int buildId;
  @ApiModelProperty("户室编号")
  private String houseId;
//  @ApiModelProperty("公司编号")
//  private long companyId;
//  @ApiModelProperty("公司信息")
//  @TableField(exist = false)
//  @JsonInclude(JsonInclude.Include.NON_NULL)
//  private CompanyInfo companyInfo;
//  @ApiModelProperty("机构编号")
//  private String organization;
  @ApiModelProperty("所在楼层")
  private int floor;
  @ApiModelProperty("0未删除，1已删除")
  @TableLogic
  private int isDelete = 0;
  @ApiModelProperty("x开始位置")
  private int xLayer ;
  @ApiModelProperty("y开始位置")
  private int yLayer ;
  @ApiModelProperty("所占行数")
  private int rowNum = 1;
  @ApiModelProperty("所占列数")
  private int columnNum = 1;
  @ApiModelProperty("户主Id")
  private Integer person;
  @ApiModelProperty("户主信息详情")
  @TableField(exist = false)
  private PersonInfo personInfo;
  @ApiModelProperty("标准地址")
  private String address;
  @ApiModelProperty("户室内的公司信息")
  @TableField(exist = false)
  private List<CompanyNatureVo> companyInfoList;

  @ApiModelProperty("历史公司数量")
  @TableField(exist = false)
  private int historyCompanyNum;

}
