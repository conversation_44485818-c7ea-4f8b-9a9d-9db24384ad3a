package com.hl.building.domain.tables;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@TableName(value = "building_info")
public class BuildingInfo {

  @ApiModelProperty("主键")
  @TableId(type = IdType.AUTO)
  private long buildId;
  @ApiModelProperty("楼宇名称")
  private String buildName;
  @ApiModelProperty("标准地址")
  private String address;
  @ApiModelProperty("楼宇层数")
  private int layerNum;
  @ApiModelProperty("户室数量")
  @TableField(exist = false)
  private int houseNum;
  @ApiModelProperty("公司数量")
  @TableField(exist = false)
  private int companyNum;
  @ApiModelProperty("每层网格行数")
  private int gridX;
  @ApiModelProperty("每层网格列数")
  private int gridY;
  @ApiModelProperty("创建时间")
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createTime;
  @ApiModelProperty("机构编号")
  private String organization;

  @ApiModelProperty("法人数量")
  @TableField(exist = false)
  private int corporateNum = 0;
  @ApiModelProperty("企业高管数量")
  @TableField(exist = false)
  private int executivesNum = 0;
  @ApiModelProperty("财务人员数量")
  @TableField(exist = false)
  private int financeNum = 0;
  @ApiModelProperty("其他人员数量")
  @TableField(exist = false)
  private int otherPersonNum = 0;
  @ApiModelProperty("重点企业数量")
  @TableField(exist = false)
  private Long focusCompanyNum = 0L;
  @ApiModelProperty("重点人员数量")
  @TableField(exist = false)
  private int focusPersonNum = 0;

}
