package com.hl.building.domain.tables;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@TableName(value = "person_info",autoResultMap = true)
public class PersonInfo {

  @ApiModelProperty("主键")
  @TableId(type = IdType.AUTO)
  private Long id;
  @ApiModelProperty("姓名")
  private String name;
  @ApiModelProperty("身份证")
  private String idCard;
  @ApiModelProperty("手机号")
  private String phone;
  @ApiModelProperty("户籍地")
  private String domicile;
  @ApiModelProperty("现住地")
  private String current;
  @ApiModelProperty("是否财务人员，0否1是")
  private int isFinance = 0;
  @ApiModelProperty("是否企业高管，0否1是")
  private int isExecutives = 0;
  @ApiModelProperty("是否企业法人，0否1是")
  private int isCorporate = 0;
  @ApiModelProperty("是否其他人员，0否1是")
  private int isOtherPerson = 0;

  @ApiModelProperty("人员类型")
  @TableField(exist = false)
  private List<String> personType = new ArrayList<>();

}
