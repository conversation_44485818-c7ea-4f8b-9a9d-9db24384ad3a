package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "wjjq.view_es_jq_all")
public class ViewEsJqAll {
    /**
     * 接警编号
     */
    @TableField(value = "jjbh")
    private String jjbh;

    /**
     * 处警标识
     */
    @TableField(value = "cjbs")
    private String cjbs;

    /**
     * 接警报警人
     */
    @TableField(value = "bjr")
    private String bjr;

    /**
     * 报警类型
     */
    @TableField(value = "bjlx")
    private String bjlx;

    /**
     * 接警单位名称
     */
    @TableField(value = "jjdwmc")
    private String jjdwmc;

    /**
     * 接警报警时间
     */
    @TableField(value = "bjdhsj_time")
    private String bjdhsjTime;

    /**
     * 接警单位
     */
    @TableField(value = "jjdw")
    private String jjdw;

    /**
     * 接警报警人联系电话
     */
    @TableField(value = "lxdh")
    private String lxdh;

    /**
     * 报警内容
     */
    @TableField(value = "bjnr")
    private String bjnr;

    /**
     * 接警登记单位名称
     */
    @TableField(value = "djdwmc")
    private String djdwmc;

    /**
     * 发生地点
     */
    @TableField(value = "sfdd")
    private String sfdd;

    /**
     * 报警方式
     */
    @TableField(value = "bjxs")
    private String bjxs;

    /**
     * 接警登记编号
     */
    @TableField(value = "jjdbh")
    private String jjdbh;

    /**
     * 警情等级
     */
    @TableField(value = "bjdhsj")
    private Date bjdhsj;

    /**
     * 接警日期时间
     */
    @TableField(value = "jjrqsj")
    private String jjrqsj;

    /**
     * 警情等级
     */
    @TableField(value = "jqdj")
    private String jqdj;

    /**
     * 警情坐标-X
     */
    @TableField(value = "gis_x")
    private String gisX;

    /**
     * 警情坐标-y
     */
    @TableField(value = "gis_y")
    private String gisY;

    /**
     * 处警处警时间
     */
    @TableField(value = "cjsj_time")
    private String cjsjTime;

    /**
     * 处警类别
     */
    @TableField(value = "cjlb")
    private String cjlb;

    /**
     * 处警单位
     */
    @TableField(value = "cjdw")
    private String cjdw;

    /**
     * 事发场所
     */
    @TableField(value = "sfcs")
    private String sfcs;

    /**
     * 损失详细情况
     */
    @TableField(value = "ssxxqk")
    private String ssxxqk;

    /**
     * 补充处理结果
     */
    @TableField(value = "bccljg")
    private String bccljg;

    /**
     * 事发时间下限
     */
    @TableField(value = "sfsjxx_time")
    private String sfsjxxTime;

    /**
     * 事发星期
     */
    @TableField(value = "sfxq")
    private String sfxq;

    /**
     * 处理结果内容
     */
    @TableField(value = "cljgnr")
    private String cljgnr;

    /**
     * 事发时间上限
     */
    @TableField(value = "sfsjsx_time")
    private String sfsjsxTime;

    /**
     * 登记人
     */
    @TableField(value = "djr")
    private String djr;

    /**
     * 警情属性
     */
    @TableField(value = "jqsx")
    private String jqsx;

    /**
     * 天气情况
     */
    @TableField(value = "tqqk")
    private String tqqk;

    /**
     * 处警编号
     */
    @TableField(value = "cjbh")
    private String cjbh;

    /**
     * 处警详址
     */
    @TableField(value = "cjxz")
    private String cjxz;

    /**
     * 处警处警单位名称
     */
    @TableField(value = "cjdwmc")
    private String cjdwmc;

    /**
     * 处警结果
     */
    @TableField(value = "cjjg")
    private String cjjg;

    /**
     * 处警处警时间
     */
    @TableField(value = "cjsj")
    private Date cjsj;

    /**
     * 事发时间下限
     */
    @TableField(value = "sfsjxx")
    private Date sfsjxx;

    /**
     * 事发时间上限
     */
    @TableField(value = "sfsjsx")
    private Date sfsjsx;

    /**
     * 所属辖区
     */
    @TableField(value = "ssxq")
    private String ssxq;

    /**
     * 处警人
     */
    @TableField(value = "cjr")
    private String cjr;

    /**
     * 警情标签
     */
    @TableField(value = "cjjqbq")
    private String cjjqbq;

    /**
     * 地址补充
     */
    @TableField(value = "bzdzmc")
    private String bzdzmc;

    /**
     * 区域类别
     */
    @TableField(value = "qylb")
    private String qylb;

    /**
     * 处警信息地点
     */
    @TableField(value = "cjxxdd")
    private String cjxxdd;

    /**
     * 警情坐标-X
     */
    @TableField(value = "xzb")
    private String xzb;

    /**
     * 警情坐标-Y'
     */
    @TableField(value = "yzb")
    private String yzb;

    /**
     * 警情标注标签
     */
    @TableField(value = "jqbz")
    private String jqbz;

    /**
     * 分局标签
     */
    @TableField(value = "fjbq")
    private String fjbq;

    /**
     * 标注状态  - 未标注 0
  - 已标注审核通过 1
  - 已标注待审核 2
  - 已标注审核未通过 3
     */
    @TableField(value = "bzzt")
    private Integer bzzt;

    /**
     * 地址标签
     */
    @TableField(value = "addressM")
    private String addressm;

    /**
     * 人员标签
     */
    @TableField(value = "personM")
    private String personm;

    /**
     * 结果标签
     */
    @TableField(value = "resultM")
    private String resultm;

    /**
     * 时间标签
     */
    @TableField(value = "timeM")
    private String timem;

    /**
     * 手段标签
     */
    @TableField(value = "toolM")
    private String toolm;

    /**
     * 原因标签
     */
    @TableField(value = "reasonM")
    private String reasonm;

    /**
     * 标注单位
     */
    @TableField(value = "unit")
    private String unit;

    /**
     * 审批结果:-1未标注 0已标注待审批 1同意 2退回
     */
    @TableField(value = "spjg")
    private Integer spjg;

    /**
     * 审批人
     */
    @TableField(value = "spr")
    private String spr;

    /**
     * 审批时间
     */
    @TableField(value = "sp_time")
    private String spTime;

    /**
     * 审批内容
     */
    @TableField(value = "spnr")
    private String spnr;

    /**
     * 标注人
     */
    @TableField(value = "bzr")
    private String bzr;

    /**
     * 标注时间
     */
    @TableField(value = "bz_time")
    private String bzTime;

    /**
     * 0未标注1已标注
     */
    @TableField(value = "mark")
    private Integer mark;

    @TableField(value = "bzrxm")
    private String bzrxm;

    @TableField(value = "sprxm")
    private String sprxm;

    /**
     * 事发原因 dict 77
     */
    @TableField(value = "fsyy")
    private String fsyy;

    /**
     * 百度提示
     */
    @TableField(value = "baidu")
    private String baidu;

    @TableField(value = "dz_type")
    private String dzType;

    @TableField(value = "dsdz")
    private String dsdz;

    @TableField(value = "jgbh")
    private String jgbh;

    @TableField(value = "dzid")
    private String dzid;

    @TableField(value = "dwmc")
    private String dwmc;

    @TableField(value = "dwdz")
    private String dwdz;

    @TableField(value = "xq")
    private String xq;

    @TableField(value = "build_no")
    private String buildNo;

    @TableField(value = "zrq")
    private String zrq;

    /**
     * 事件标签Id
     */
    @TableField(value = "eventId")
    private String eventid;

    /**
     * 所属责任区
     */
    @TableField(value = "sszrq")
    private String sszrq;
}