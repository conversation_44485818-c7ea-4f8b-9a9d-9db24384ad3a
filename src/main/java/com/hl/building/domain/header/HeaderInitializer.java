package com.hl.building.domain.header;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class HeaderInitializer {

    @EventListener(value = ApplicationReadyEvent.class)
    public void init() {
        HeaderCache.put(HeaderKey.IMPORTANT_AREA_HEADER.getKey(), getImportantAreaHeaderList());
        HeaderCache.put(HeaderKey.IMPORTANT_AREA_DETAIL.getKey(), getImportantAreaDetailHeaderList());
        HeaderCache.put(HeaderKey.CAMERA_FACE_RECORD.getKey(), getCameraFaceRecord());
        HeaderCache.put(HeaderKey.JQ_BASE_HEADER.getKey(), getJqBaseHeader());
        HeaderCache.put(HeaderKey.PERSON_POLICE_RECORD.getKey(), getPersonPoliceRecord());
        HeaderCache.put(HeaderKey.PERSON_CASE_INFO.getKey(), getPersonCaseInfoHeaderList());
    }

    private List<JSONObject> getPersonCaseInfoHeaderList() {
        List<JSONObject> headerList = new ArrayList<>();
        headerList.add(createHeader("case_no", "案件编号", false, false));
        headerList.add(createHeader("case_main_class", "案件类型", false, false));
        headerList.add(createHeader("case_name", "案件名称", false, false));
        headerList.add(createHeader("case_accept_dep_name", "受案单位", false, false));
        headerList.add(createHeader("case_report_time", "报案时间", false, false));
        return headerList;
    }

    private List<JSONObject> getPersonPoliceRecord() {
        List<JSONObject> headerList = new ArrayList<>();
        headerList.add(createHeader("jjbh", "接警编号", false, false));
        headerList.add(createHeader("bjdhsjTime", "接警时间", false, false));
        headerList.add(createHeader("jjdwmc", "接警单位", false, false));
        headerList.add(createHeader("bjlx", "报警类型", false, false));
        headerList.add(createHeader("cjlb", "处警类别", false, false));
        headerList.add(createHeader("bjnr", "报警内容", false, false));
        headerList.add(createHeader("cljgnr", "处警内容", false, false));
        headerList.add(createHeader("cjdwmc", "处警单位名称", false, false));
        return headerList;
    }


    private List<JSONObject> getImportantAreaHeaderList() {
        List<JSONObject> headerList = new ArrayList<>();
        headerList.add(createHeader("organization_name", "单位", false, false));
        headerList.add(createHeader("dict_name", "重点单位名称", false, false));
        JSONObject countHeader = createHeader("count", "涉警数量", false, false);
        countHeader.put("next", new JSONObject()
                .fluentPut("type", "list")
                .fluentPut("title", new JSONArray().fluentAdd("dict_name"))
                .fluentPut("param", new JSONArray().fluentAdd("address"))
                .fluentPut("url", "/indexV3/queryImportantAreaDetail"));
        headerList.add(countHeader);
        return headerList;
    }

    private List<JSONObject> getImportantAreaDetailHeaderList() {

        List<JSONObject> headerList = new ArrayList<>();

        headerList.add(createHeader("jjbh", "接警编号", false, false));
        headerList.add(createHeader("bjdhsj_time", "接警时间", false, false));
        headerList.add(createHeader("jjdwmc", "接警单位", false, false));
        // 报警类型需要翻译
        headerList.add(createHeader("bjlx_name", "报警类型", false, false));
        // 处警类别需要翻译
        headerList.add(createHeader("cjlb_name", "处警类别", false, false));

        headerList.add(createHeader("cjjg_name", "处警结果", false, false));

        headerList.add(createHeader("address_label", "地址标签", false, false)
                .fluentPut("type", "tag"));

//        headerList.add(createHeader("person_label", "人员标签", false, false)
//                .fluentPut("type","tag"));
        return headerList;
    }

    private List<JSONObject> getCameraFaceRecord() {
        List<JSONObject> headerList = new ArrayList<>();
        headerList.add(createHeader("camera_name", "点位名称", false, false));
        headerList.add(createHeader("name", "姓名", false, false));
        headerList.add(createHeader("id_card", "身份证", false, false));
        headerList.add(createHeader("appear_time", "出现时间", false, false));
        headerList.add(createHeader("similarity", "相似度", false, false));
        JSONObject policeInfo = createHeader("police_count", "涉警数量", false, false);
        policeInfo.put("next",new JSONObject()
                .fluentPut("type","list")
                .fluentPut("url","/relaInfo/personPoliceRecord")
                .fluentPut("param",new JSONArray().fluentAdd("id_card"))
                .fluentPut("title",new JSONArray().fluentAdd("name")));
        headerList.add(policeInfo);
        JSONObject caseInfo = createHeader("case_count", "涉案数量", false, false);
        caseInfo.put("next",new JSONObject()
                .fluentPut("type","list")
                .fluentPut("url","/relaInfo/personCaseRecord")
                .fluentPut("param",new JSONArray().fluentAdd("id_card"))
                .fluentPut("title",new JSONArray().fluentAdd("name")));
        headerList.add(caseInfo);
        return headerList;
    }


    private List<JSONObject> getJqBaseHeader() {
        List<JSONObject> headerList = new ArrayList<>();
        headerList.add(createHeader("jjbh", "接警编号", false, false));
        headerList.add(createHeader("bjdhsj_time", "接警时间", false, false));
        headerList.add(createHeader("jjdwmc", "接警单位", false, false));
        headerList.add(createHeader("bjlx", "报警类型", false, false));
        headerList.add(createHeader("cjlb", "处警类别", false, false));
        headerList.add(createHeader("cjjg", "处警结果", false, false));
        headerList.add(createHeader("sfcs", "事发场所", false, false));
        headerList.add(createHeader("cljgnr", "处理结果内容", false, false));
        return headerList;
    }


    // 创建简单字段
    private JSONObject createHeader(String item, String name, boolean sum, boolean sort) {
        return new JSONObject().fluentPut("item", item).fluentPut("name", name)
                .fluentPut("sum", sum)
                .fluentPut("sort", sort);
    }

    // 创建带单位的简单字段
    private JSONObject createHeader(String item, String name, String unit) {
        return new JSONObject()
                .fluentPut("item", item)
                .fluentPut("name", name)
                .fluentPut("unit", unit);

    }

    // 创建带子节点的字段
    private JSONObject createHeaderWithChildren(String name, JSONObject... children) {
        return new JSONObject()
                .fluentPut("name", name)
                .fluentPut("children", Arrays.asList(children));
    }

}
