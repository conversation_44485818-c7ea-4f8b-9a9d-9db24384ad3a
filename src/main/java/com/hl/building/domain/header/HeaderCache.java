package com.hl.building.domain.header;

import com.alibaba.fastjson2.JSONObject;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class HeaderCache {
    private static final Map<String, List<JSONObject>> HEADER_CACHE = new ConcurrentHashMap<>();

    public static void put(String key, List<JSONObject> value) {
        HEADER_CACHE.put(key, value);
    }

    public static List<JSONObject> get(String key) {
        return HEADER_CACHE.get(key);
    }

    public static boolean contains(String key) {
        return HEADER_CACHE.containsKey(key);
    }
}
