package com.hl.building.domain.header;

import lombok.Getter;

@Getter
public enum HeaderKey {

    IMPORTANT_AREA_HEADER("important-area", "重点部位表头"),

    IMPORTANT_AREA_DETAIL("important-area-detail", "重点部位详情表头"),

    CAMERA_FACE_RECORD("camera-face-record", "人脸抓拍记录表头"),
    JQ_BASE_HEADER("jq-base-header", "警情基础表头"),

    PERSON_CASE_INFO("person-case-info", "涉案人员信息表头"),
    PERSON_POLICE_RECORD("person-police-record", "涉警人员表头");

    private final String key;
    private final String description;

    HeaderKey(String key, String description) {
        this.key = key;
        this.description = description;
    }
}
