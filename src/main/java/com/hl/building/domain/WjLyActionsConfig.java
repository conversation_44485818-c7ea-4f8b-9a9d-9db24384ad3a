package com.hl.building.domain;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hl.building.trans.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description="wj_ly_actions_config")
@Data
@TableName(value = "wjhl.wj_ly_actions_config",autoResultMap = true)
public class WjLyActionsConfig {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 行为名称
     */
    @TableField(value = "actions_name")
    @ApiModelProperty(value="行为名称")
    private String actionsName;

    /**
     * 行为类别
     */
    @TableField(value = "actions_type")
    @ApiModelProperty(value="行为类别")
    private Integer actionsType;

    /**
     * 行为说明
     */
    @TableField(value = "actions_remark")
    @ApiModelProperty(value="行为说明")
    private String actionsRemark;

    /**
     * 配置
     */
    @TableField(value = "config",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value="配置")
    private JSONArray config;

    /**
     * 创建时间
     */
    @TableField(value = "create_user")
    @ApiModelProperty(value="创建时间")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新用户
     */
    @TableField(value = "update_user")
    @ApiModelProperty(value="更新用户")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    @ApiModelProperty(value="是否删除，0否1是")
    @TableField(value = "is_delete")
    @TableLogic
    private Integer isDelete;

    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ,mapper = "createUser")
    private JSONObject createUserInfo;
}