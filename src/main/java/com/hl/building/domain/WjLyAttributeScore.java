package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description="wj_ly_attribute_score")
@Data
@TableName(value = "wjhl.wj_ly_attribute_score")
public class WjLyAttributeScore {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 属性名称
     */
    @TableField(value = "attribute_name")
    @ApiModelProperty(value="属性名称")
    private String attributeName;

    /**
     * 关联群体id
     */
    @TableField(value = "group_id")
    @ApiModelProperty(value="关联群体id")
    private String groupId;

    /**
     * 基础分值
     */
    @TableField(value = "base_score")
    @ApiModelProperty(value="基础分值")
    private Double baseScore;

    /**
     * 风险系数
     */
    @TableField(value = "risk_factor")
    @ApiModelProperty(value="风险系数")
    private Double riskFactor;

    /**
     * 状态
     */
    @TableField(value = "\"status\"")
    @ApiModelProperty(value="状态")
    private Integer status;

    /**
     * 创建用户
     */
    @TableField(value = "create_user")
    @ApiModelProperty(value="创建用户")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新用户
     */
    @TableField(value = "update_user")
    @ApiModelProperty(value="更新用户")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}