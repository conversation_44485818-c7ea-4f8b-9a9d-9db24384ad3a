package com.hl.building.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AddOrUpdHouseReq {
    @ApiModelProperty("主键")
    private long id;
    @ApiModelProperty("所属楼宇")
    private int buildId;
    @ApiModelProperty("户室编号")
    private String houseId;
    @ApiModelProperty("户室性质")
    private String houseNature;
//    @ApiModelProperty("公司名称")
//    private String CompanyName;
//    @ApiModelProperty("机构编号")
//    private String organization;
    @ApiModelProperty("所在楼层")
    private int floor;
    @ApiModelProperty("x开始位置")
    private int xLayer ;
    @ApiModelProperty("y开始位置")
    private int yLayer ;
    @ApiModelProperty("所占行数")
    private int rowNum = 1;
    @ApiModelProperty("所占列数")
    private int columnNum = 1;
    @ApiModelProperty("人员id")
    private Integer person;
    @ApiModelProperty("标准地址")
    private String address;
}
