package com.hl.building.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ListHouseReq {
    private int page;
    private int limit;
    @ApiModelProperty("多个id逗号连接")
    private String id;
    //户室编号、户室标准地址
    @ApiModelProperty("关键字")
    private String query;
    @ApiModelProperty("是否删除，0否1是")
    private int isDelete = 0;
    @ApiModelProperty("楼宇编号")
    private String buildId;


    @ApiModelProperty("标准地址id")
    private String dzId;

    @ApiModelProperty("标准地址名称")
    private String address;

}
