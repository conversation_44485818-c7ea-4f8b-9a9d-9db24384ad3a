package com.hl.building.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AddOrUpdBuildReq {
    @ApiModelProperty("楼宇编号")
    private String buildId;
    @ApiModelProperty("楼宇名称")
    private String buildName;
    @ApiModelProperty("标准地址")
    private String address;
    @ApiModelProperty("楼宇层数")
    private int layerNum;
    @ApiModelProperty("每层网格行数")
    private int gridX;
    @ApiModelProperty("每层网格列数")
    private int gridY;
}
