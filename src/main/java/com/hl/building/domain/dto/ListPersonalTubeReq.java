package com.hl.building.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("重点人员列表请求参数")
public class ListPersonalTubeReq {
    
    @ApiModelProperty("页码")
    private Integer page = 1;
    
    @ApiModelProperty("每页条数")
    private Integer limit = 10;

    @ApiModelProperty("公民身份号码")
    private String gmsfhm;

    @ApiModelProperty("姓名")
    private String xm;

    @ApiModelProperty("管理状态")
    private List<String> zdryglztdm;

    @ApiModelProperty("现管理单位")
    private String xgldwGajgmc;

    @ApiModelProperty("列管时间-开始")
    private String controlTimeStart;

    @ApiModelProperty("列管时间-结束")
    private String controlTimeEnd;

    @ApiModelProperty("管理级别")
    private List<String> gljbdm;

    @ApiModelProperty("警种属性")
    private List<String> zdryjzlbdmList;

    @ApiModelProperty("身份类别")
    private List<String> zdrysflbdmList;

    @ApiModelProperty("户籍属性")
    private String zdryhjsxdm;

    @ApiModelProperty("撤管时间-开始")
    private String cancelTimeStart;

    @ApiModelProperty("撤管时间-结束")
    private String cancelTimeEnd;
} 