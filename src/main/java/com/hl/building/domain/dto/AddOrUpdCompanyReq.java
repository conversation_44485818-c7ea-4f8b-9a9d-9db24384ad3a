package com.hl.building.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AddOrUpdCompanyReq {
    @ApiModelProperty("公司编号")
    private long companyId;
    @ApiModelProperty("楼宇编号")
    private long buildId;
    @ApiModelProperty("户室性质")
    private String houseNature = "空闲";
    @ApiModelProperty("公司名称")
    private String companyName;
    @ApiModelProperty("机构编号")
    private String organization;
    @ApiModelProperty("其他人员")
    @TableField(value="other_person",typeHandler = JacksonTypeHandler.class)
    private List<Integer> otherPerson;
    @ApiModelProperty("企业法人")
    @TableField(value="corporate",typeHandler = JacksonTypeHandler.class)
    private List<Integer> corporate;
    @ApiModelProperty("企业高管")
    @TableField(value="executives",typeHandler = JacksonTypeHandler.class)
    private List<Integer> executives;
    @ApiModelProperty("财务人员")
    @TableField(value="finance",typeHandler = JacksonTypeHandler.class)
    private List<Integer> finance;
    @ApiModelProperty("0未删除，1已删除")
    @TableLogic
    private int isDelete = 0;
    @ApiModelProperty("户室id组成的数组")
    private List<Integer> houseId;
    @ApiModelProperty("涉警信息")
    private String sjInfo;
    @ApiModelProperty("涉案信息")
    private String saInfo;
    @ApiModelProperty("0未注销，1注销")
    private int isLogout = 0;
}
