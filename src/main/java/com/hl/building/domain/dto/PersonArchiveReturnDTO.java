package com.hl.building.domain.dto;

import com.hl.building.domain.CzJzCaseInfo;
import com.hl.building.domain.ViewCameraFaceRecord;
import com.hl.building.domain.ViewEsJqAll;
import com.hl.building.domain.WjscSyrk;
import lombok.Data;

import java.util.List;

@Data
public class PersonArchiveReturnDTO {

    private WjscSyrk personBaseInfo;

    private List<CzJzCaseInfo> caseInfoList;

    private List<ViewCameraFaceRecord> faceRecordList;

    private List<ViewEsJqAll> jqList;
}
