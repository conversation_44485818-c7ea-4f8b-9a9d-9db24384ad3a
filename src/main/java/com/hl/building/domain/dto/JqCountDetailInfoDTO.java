package com.hl.building.domain.dto;

import com.hl.building.domain.WjBuildingInfo;
import com.hl.es.domain.dto.JqCountDTO;
import lombok.Data;

import java.util.List;

@Data
public class JqCountDetailInfoDTO {

    private WjBuildingInfo buildingInfo;


    private List<HouseDetail> houseDetailList;


    private List<CompanyDetail> companyDetailList;


    @Data
    public static class CompanyDetail{
        private String companyName;

        private String companyCode;

        private String jgbh;

        private Integer jqCount;

        private List<MainType> mainTypeList;
    }



    @Data
    public static class HouseDetail{
        private String houseName;

        private String dzId;
        private Integer jqCount;

        private List<MainType> mainTypeList;

    }

    @Data
    public static class MainType {
        private String typeName;

        private String typeCode;

        private Integer count;
    }
}
