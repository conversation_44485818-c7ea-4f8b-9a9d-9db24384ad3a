package com.hl.building.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ListCompanyReq {
    private int page;
    private int limit;
    //楼宇名称、公司名称、企业人员
    @ApiModelProperty("关键字")
    private String query;
    @ApiModelProperty("是否删除，0否1是")
    private int isDelete = 0;
    @ApiModelProperty("楼宇编号")
    private String buildId;
    @ApiModelProperty("0非重点企业，1重点企业")
    private Integer isFocus = 0;
    @ApiModelProperty("0未注销，1注销")
    private Integer isLogout ;

    @ApiModelProperty("企业ID")
    private String companyId;
}
