package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hl.building.domain.vo.WjCompanyHouseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 户室信息
 */
@ApiModel(description="户室信息")
@Data
@TableName(value = "wjhl.wj_house_info")
public class WjHouseInfo {
    /**
     * 户室编号
     */
    @TableId(value = "house_id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value="户室编号")
    private String houseId;

    /**
     * 所属楼宇
     */
    @TableField(value = "build_id")
    @ApiModelProperty(value="所属楼宇")
    private String buildId;

    /**
     * 户室名称
     */
    @TableField(value = "house_name")
    @ApiModelProperty(value="户室名称")
    private String houseName;

    /**
     * 标准地址
     */
    @TableField(value = "address")
    @ApiModelProperty(value="标准地址")
    private String address;

    /**
     * 所在楼层
     */
    @TableField(value = "floor")
    @ApiModelProperty(value="所在楼层")
    private Integer floor;

    /**
     * x开始位置
     */
    @TableField(value = "x_layer")
    @ApiModelProperty(value="x开始位置")
    private Integer xLayer;

    /**
     * y开始位置
     */
    @TableField(value = "y_layer")
    @ApiModelProperty(value="y开始位置")
    private Integer yLayer;

    /**
     * 所占行数
     */
    @TableField(value = "row_num")
    @ApiModelProperty(value="所占行数")
    private Integer rowNum;

    /**
     * 所占列数
     */
    @TableField(value = "column_num")
    @ApiModelProperty(value="所占列数")
    private Integer columnNum;

    /**
     * 0未删除，1已删除
     */
    @TableField(value = "is_delete")
    @ApiModelProperty(value="0未删除，1已删除")
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 创建用户
     */
    @TableField(value = "create_user")
    @ApiModelProperty(value="创建用户")
    private String createUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_user")
    @ApiModelProperty(value="更新时间")
    private String updateUser;


    @TableField(value = "house_nature")
    @ApiModelProperty(value="户室性质")
    private String houseNature;


    @TableField(exist = false)
    private List<WjCompanyHouseVO> companyList;

    /**
     * 出租标识 0 居住 1 出租
     */
    @TableField(value = "czbs")
    @ApiModelProperty(value="出租标识 0 居住 1 出租")
    private String czbs;

    /**
     * 房主姓名
     */
    @TableField(value = "fz_xm")
    @ApiModelProperty(value="房主姓名")
    private String fzXm;

    /**
     * 房主证件号码
     */
    @TableField(value = "fz_zjhm")
    @ApiModelProperty(value="房主证件号码")
    private String fzZjhm;

    /**
     * 房主电话
     */
    @TableField(value = "fz_dh")
    @ApiModelProperty(value="房主电话")
    private String fzDh;

    /**
     * 实际出租人姓名
     */
    @TableField(value = "sjczr_xm")
    @ApiModelProperty(value="实际出租人姓名")
    private String sjczrXm;

    /**
     * 实际出租人证件号码
     */
    @TableField(value = "sjczr_zjhm")
    @ApiModelProperty(value="实际出租人证件号码")
    private String sjczrZjhm;

    /**
     * 实际出租人电话
     */
    @TableField(value = "sjczr_dh")
    @ApiModelProperty(value="实际出租人电话")
    private String sjczrDh;

    /**
     * 房主现住地址
     */
    @TableField(value = "fz_xzdz")
    @ApiModelProperty(value="房主现住地址")
    private String fzXzdz;

    /**
     * 实际出租人现住地址
     */
    @TableField(value = "sjczr_xzdz")
    @ApiModelProperty(value="实际出租人现住地址")
    private String sjczrXzdz;

    /**
     * 标准地址id
     */
    @TableField(value = "dz_id")
    @ApiModelProperty(value="标准地址id")
    private String dzId;

    /**
     * 标准地址名称
     */
    @TableField(value = "dzmc")
    @ApiModelProperty(value="标准地址名称")
    private String dzmc;

    @TableField(value = "hjxz")
    @ApiModelProperty(value="户籍详址")
    private String hjxz;

}