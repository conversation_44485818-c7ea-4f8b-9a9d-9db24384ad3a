package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 楼宇关联案件信息
 */
@ApiModel(description = "楼宇关联案件信息")
@Data
@TableName(value = "wjhl.wj_ly_case_info", autoResultMap = true)
public class WjLyCaseInfo {
    /**
     * 案件编号
     */
    @TableId(value = "case_no", type = IdType.AUTO)
    @ApiModelProperty(value = "案件编号")
    private String caseNo;

    /**
     * 案件名称
     */
    @TableField(value = "case_name")
    @ApiModelProperty(value = "案件名称")
    private String caseName;

    /**
     * 简要案情
     */
    @TableField(value = "case_summary")
    @ApiModelProperty(value = "简要案情")
    private String caseSummary;

    /**
     * 案发地点
     */
    @TableField(value = "case_address")
    @ApiModelProperty(value = "案发地点")
    private String caseAddress;

    /**
     * 案发开始时间
     */
    @TableField(value = "case_start_time")
    @ApiModelProperty(value = "案发开始时间")
    private Date caseStartTime;

    /**
     * 案发结束时间
     */
    @TableField(value = "case_end_time")
    @ApiModelProperty(value = "案发结束时间")
    private Date caseEndTime;

    /**
     * 报案人手机号码
     */
    @TableField(value = "case_report_phone")
    @ApiModelProperty(value = "报案人手机号码")
    private String caseReportPhone;

    /**
     * 报案人姓名
     */
    @TableField(value = "case_report_name")
    @ApiModelProperty(value = "报案人姓名")
    private String caseReportName;

    /**
     * 报案时间
     */
    @TableField(value = "case_report_time")
    @ApiModelProperty(value = "报案时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date caseReportTime;

    /**
     * 受理人用户名
     */
    @TableField(value = "case_accept_uid")
    @ApiModelProperty(value = "受理人用户名")
    private String caseAcceptUid;

    /**
     * 受理人名称
     */
    @TableField(value = "case_accept_name")
    @ApiModelProperty(value = "受理人名称")
    private String caseAcceptName;

    /**
     * 受理单位代码
     */
    @TableField(value = "case_accept_dep_code")
    @ApiModelProperty(value = "受理单位代码")
    private String caseAcceptDepCode;

    /**
     * 受理单位名称
     */
    @TableField(value = "case_accept_dep_name")
    @ApiModelProperty(value = "受理单位名称")
    private String caseAcceptDepName;

    /**
     * 受理时间
     */
    @TableField(value = "case_accept_time")
    @ApiModelProperty(value = "受理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date caseAcceptTime;

    /**
     * 立案审批用户名
     */
    @TableField(value = "register_approval_uid")
    @ApiModelProperty(value = "立案审批用户名")
    private String registerApprovalUid;

    /**
     * 立案审批人名称
     */
    @TableField(value = "register_approval_name")
    @ApiModelProperty(value = "立案审批人名称")
    private String registerApprovalName;

    /**
     * 立案审批单位代码
     */
    @TableField(value = "register_approval_dep_code")
    @ApiModelProperty(value = "立案审批单位代码")
    private String registerApprovalDepCode;

    /**
     * 立案审批单位名称
     */
    @TableField(value = "register_approval_dep_name")
    @ApiModelProperty(value = "立案审批单位名称")
    private String registerApprovalDepName;

    /**
     * 立案审批名时间
     */
    @TableField(value = "register_approval_time")
    @ApiModelProperty(value = "立案审批名时间")
    private Date registerApprovalTime;

    /**
     * 破案审批人用户名
     */
    @TableField(value = "solve_approval_uid")
    @ApiModelProperty(value = "破案审批人用户名")
    private String solveApprovalUid;

    /**
     * 破案审批人名称
     */
    @TableField(value = "solve_approval_name")
    @ApiModelProperty(value = "破案审批人名称")
    private String solveApprovalName;

    /**
     * 破案审批人单位代码
     */
    @TableField(value = "solve_approval_dep_code")
    @ApiModelProperty(value = "破案审批人单位代码")
    private String solveApprovalDepCode;

    /**
     * 破案审批人单位名称
     */
    @TableField(value = "solve_approval_dep_name")
    @ApiModelProperty(value = "破案审批人单位名称")
    private String solveApprovalDepName;

    /**
     * 破案单位代码
     */
    @TableField(value = "solve_dep_code")
    @ApiModelProperty(value = "破案单位代码")
    private String solveDepCode;

    /**
     * 破案单位名称
     */
    @TableField(value = "solve_dep_name")
    @ApiModelProperty(value = "破案单位名称")
    private String solveDepName;

    /**
     * 破案时间
     */
    @TableField(value = "solve_time")
    @ApiModelProperty(value = "破案时间")
    private Date solveTime;

    /**
     * 案件大类
     */
    @TableField(value = "case_main_class")
    @ApiModelProperty(value = "案件大类")
    private String caseMainClass;

    /**
     * 案件类别
     */
    @TableField(value = "case_type")
    @ApiModelProperty(value = "案件类别")
    private String caseType;

    /**
     * 案件副案别
     */
    @TableField(value = "case_sub_type")
    @ApiModelProperty(value = "案件副案别")
    private String caseSubType;

    /**
     * 案件状态
     */
    @TableField(value = "case_state")
    @ApiModelProperty(value = "案件状态")
    private String caseState;

    /**
     * 案件性质
     */
    @TableField(value = "case_nature")
    @ApiModelProperty(value = "案件性质")
    private String caseNature;

    /**
     * 创建用户id
     */
    @TableField(value = "create_uid")
    @ApiModelProperty(value = "创建用户id")
    private String createUid;

    /**
     * 创建用户
     */
    @TableField(value = "create_name")
    @ApiModelProperty(value = "创建用户")
    private String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 初次入库时间
     */
    @TableField(value = "scrk_time")
    @ApiModelProperty(value = "初次入库时间")
    private Date scrkTime;

    /**
     * 涉案总价值
     */
    @TableField(value = "value_involved")
    @ApiModelProperty(value = "涉案总价值")
    private String valueInvolved;

    /**
     * 报警人性别
     */
    @TableField(value = "case_report_gender")
    @ApiModelProperty(value = "报警人性别")
    private String caseReportGender;

    /**
     * 报警人出生年月
     */
    @TableField(value = "case_report_birthday")
    @ApiModelProperty(value = "报警人出生年月")
    private String caseReportBirthday;

    /**
     * 报警人证件号码
     */
    @TableField(value = "case_report_id_card")
    @ApiModelProperty(value = "报警人证件号码")
    private String caseReportIdCard;

    /**
     * 报警人单位名称
     */
    @TableField(value = "case_report_unit")
    @ApiModelProperty(value = "报警人单位名称")
    private String caseReportUnit;

    /**
     * 报警人地址信息
     */
    @TableField(value = "case_report_address")
    @ApiModelProperty(value = "报警人地址信息")
    private String caseReportAddress;

    /**
     * 指派状态
     */
    @TableField(value = "assign_state")
    @ApiModelProperty(value = "指派状态")
    private Short assignState;

    /**
     * 破案方式
     */
    @TableField(value = "solve_way")
    @ApiModelProperty(value = "破案方式")
    private String solveWay;

    /**
     * 破案类型
     */
    @TableField(value = "solve_type")
    @ApiModelProperty(value = "破案类型")
    private String solveType;

    /**
     * 破案简况
     */
    @TableField(value = "solve_brief")
    @ApiModelProperty(value = "破案简况")
    private String solveBrief;

    /**
     * 案情综述
     */
    @TableField(value = "case_review")
    @ApiModelProperty(value = "案情综述")
    private String caseReview;

    /**
     * 犯罪主体类型
     */
    @TableField(value = "crime_sub_type")
    @ApiModelProperty(value = "犯罪主体类型")
    private String crimeSubType;

    /**
     * 经济损失
     */
    @TableField(value = "economic_loss")
    @ApiModelProperty(value = "经济损失")
    private Long economicLoss;

    /**
     * 挽回总价值
     */
    @TableField(value = "save_involved")
    @ApiModelProperty(value = "挽回总价值")
    private Long saveInvolved;

    /**
     * 缴获总价值
     */
    @TableField(value = "capture_involved")
    @ApiModelProperty(value = "缴获总价值")
    private Long captureInvolved;

    /**
     * 案件主办人标识
     */
    @TableField(value = "major_user_uid")
    @ApiModelProperty(value = "案件主办人标识")
    private String majorUserUid;

    /**
     * 案件主办单位编码
     */
    @TableField(value = "major_unit_code")
    @ApiModelProperty(value = "案件主办单位编码")
    private String majorUnitCode;

    /**
     * 主办电话
     */
    @TableField(value = "major_phone")
    @ApiModelProperty(value = "主办电话")
    private String majorPhone;

    /**
     * 案件主办人名称
     */
    @TableField(value = "major_user_name")
    @ApiModelProperty(value = "案件主办人名称")
    private String majorUserName;

    /**
     * 案件主办单位名称
     */
    @TableField(value = "major_unit_name")
    @ApiModelProperty(value = "案件主办单位名称")
    private String majorUnitName;

    /**
     * 案件主办人警号
     */
    @TableField(value = "major_police_number")
    @ApiModelProperty(value = "案件主办人警号")
    private String majorPoliceNumber;

    /**
     * 主办人姓名
     */
    @TableField(value = "sponsor_name")
    @ApiModelProperty(value = "主办人姓名")
    private String sponsorName;

    /**
     * 协办人姓名
     */
    @TableField(value = "assistant_name")
    @ApiModelProperty(value = "协办人姓名")
    private String assistantName;

    /**
     * 受理人
     */
    @TableField(value = "acceptor_name")
    @ApiModelProperty(value = "受理人")
    private String acceptorName;

    /**
     * 变更时间
     */
    @TableField(value = "case_bg_time")
    @ApiModelProperty(value = "变更时间")
    private String caseBgTime;

    /**
     * 关联公司
     */
    @TableField(value = "company", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "关联公司")
    private List<String> company;

    /**
     * 关联楼宇
     */
    @TableField(value = "building", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "关联楼宇")
    private List<String> building;

    /**
     * 关联人员
     */
    @TableField(value = "person", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "关联人员")
    private List<String> person;

    /**
     * 关联公司
     */
    @TableField(value = "company_info", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "关联公司")
    private List<String> companyInfo;

    /**
     * 关联楼宇信息
     */
    @TableField(value = "building_info", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "关联楼宇信息")
    private List<String> buildingInfo;

    /**
     * 关联人员信息
     */
    @TableField(value = "person_info",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "关联人员信息")
    private List<String> personInfo;
}