package com.hl.building.domain.vo;

import com.hl.building.domain.WjCompanyInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class WjCompanyRespVO extends WjCompanyInfo {


    @ApiModelProperty("所有户室")
    private List<WjCompanyHouseRespVO> houseList;

    @ApiModelProperty("从业人员数量")
    private Integer peopleCount;

    @ApiModelProperty("涉警数量")
    private Integer jqCount;

    @ApiModelProperty("涉案数量")
    private Integer caseCount;

//    @ApiModelProperty("高管信息")
//    private List<WjCompanyPersonRespVO> executiveInfo;
//
//    @ApiModelProperty("法人信息")
//    private List<WjCompanyPersonRespVO> corporateInfo;

}
