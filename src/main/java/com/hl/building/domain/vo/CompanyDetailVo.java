package com.hl.building.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hl.building.domain.tables.HouseInfo;
import com.hl.building.domain.tables.PersonInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CompanyDetailVo {
    @ApiModelProperty("公司名称")
    private String companyName;
    @ApiModelProperty("其他人员详情")
    private List<PersonInfo> otherPersonInfo;
    @ApiModelProperty("企业法人详情")
    private List<PersonInfo> corporateInfo;
    @ApiModelProperty("企业高管详情")
    private List<PersonInfo> executivesInfo;
    @ApiModelProperty("财务人员详情")
    private List<PersonInfo> financeInfo;
    @ApiModelProperty("重点人员详情")
    private List<PersonInfo> focusInfo;
    @ApiModelProperty("户室信息")
    private List<HouseInfo> houseInfo;
    @ApiModelProperty("涉警信息")
    private String sjInfo;
    @ApiModelProperty("涉案信息")
    private String saInfo;
}
