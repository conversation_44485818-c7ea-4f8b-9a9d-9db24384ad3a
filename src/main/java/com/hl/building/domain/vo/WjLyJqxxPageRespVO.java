package com.hl.building.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hl.building.config.TimeStringSerializer;
import com.hl.building.trans.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class WjLyJqxxPageRespVO {
    @ApiModelProperty(value = "接警编号")
    private String jjbh;


    @ApiModelProperty(value = "报警类型")
    private String bjlx;

    @Translation(type = TransConstants.JQ_TRANS, mapper = "bjlx")
    private String bjlxMc;

    @TableField(value = "jjdwmc")
    @ApiModelProperty(value = "接警单位")
    private String jjdwmc;

    @ApiModelProperty(value = "报警时间")
    @JsonSerialize(using = TimeStringSerializer.class)
    private String bjdhsjTime;

    @ApiModelProperty(value = "")
    private String jjdw;

    @ApiModelProperty(value = "")
    private String lxdh;

    @ApiModelProperty(value = "")
    private String bjnr;

    @ApiModelProperty(value = "")
    private String djdwmc;

    @ApiModelProperty(value = "")
    private String sfdd;

    @ApiModelProperty(value = "")
    private String bjxs;

    @ApiModelProperty(value = "")
    private String jjdbh;

    @ApiModelProperty(value = "")
    private Date bjdhsj;

    @ApiModelProperty(value = "")
    private String jjrqsj;

    @ApiModelProperty(value = "")
    private String jqdj;

    @ApiModelProperty(value = "")
    private String gisX;

    @ApiModelProperty(value = "")
    private String gisY;

    @ApiModelProperty(value = "")
    private String sfxq;

    @ApiModelProperty(value = "")
    private String cljgnr;

    @ApiModelProperty(value = "")
    private String djr;

    @ApiModelProperty(value = "")
    private String jqsx;

    @ApiModelProperty(value = "")
    private String tqqk;

    @ApiModelProperty(value = "")
    private String zrq;

    @ApiModelProperty(value = "")
    private String cjbh;

    @ApiModelProperty(value = "处警时间")
    @JsonSerialize(using = TimeStringSerializer.class)
    private String cjsjTime;

    @ApiModelProperty(value = "处警类别")
    private String cjlb;

    @Translation(type = TransConstants.JQ_TRANS, mapper = "cjlb")
    private String cjlbMc;

    @ApiModelProperty(value = "")
    private String cjdw;

    @ApiModelProperty(value = "")
    private String sfcs;

    @ApiModelProperty(value = "")
    private String ssxxqk;

    @ApiModelProperty(value = "")
    private String bccljg;

    @ApiModelProperty(value = "")
    private String cjr;

    @ApiModelProperty(value = "")
    private String cjxz;

    @ApiModelProperty(value = "处警单位")
    private String cjdwmc;

    @ApiModelProperty(value = "")
    private String cjjg;

    @ApiModelProperty(value = "")
    private Date cjsj;

    @TableField(value = "company", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "")
    private List<String> company;

    @TableField(value = "building", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "")
    private List<String> building;

    @TableField(value = "person", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "")
    private List<String> person;

    @TableField(value = "company_info", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "")
    private List<String> companyInfo;

    @TableField(value = "building_info", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "")
    private List<String> buildingInfo;

    @TableField(value = "person_info", typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "")
    private List<String> personInfo;
}
