package com.hl.building.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.hl.building.domain.tables.BuildingInfo;
import com.hl.building.domain.tables.CompanyInfo;
import com.hl.building.domain.tables.HouseInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class HouseDistributionVo {
    @ApiModelProperty("楼宇基本信息")
    private BuildingInfo base_info;

    @ApiModelProperty("所有户室")
    private List<HouseInfo> rooms;
}
