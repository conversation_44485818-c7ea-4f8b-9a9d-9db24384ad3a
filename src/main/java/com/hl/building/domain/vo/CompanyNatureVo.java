package com.hl.building.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hl.building.domain.tables.PersonInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CompanyNatureVo {
    @ApiModelProperty("公司编号")
    private long companyId;
    @ApiModelProperty("户室性质")
    private String houseNature;
    @ApiModelProperty("公司名称")
    private String companyName;
    @ApiModelProperty("户室id组成的数组")
    @TableField(value="house_id",typeHandler = JacksonTypeHandler.class)
    private List<Integer> houseId;
}
