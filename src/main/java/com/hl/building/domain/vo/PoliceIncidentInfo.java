package com.hl.building.domain.vo;

import lombok.Data;

/**
 * 警情信息实体类
 */
@Data
public class PoliceIncidentInfo {
    /**
     * 警情编号
     */
    private String jjbh;
    
    /**
     * 处警标识
     */
    private String cjbs;
    
    /**
     * 报警人
     */
    private String bjr;
    
    /**
     * 报警类型
     */
    private String bjlx;
    
    /**
     * 接警单位名称
     */
    private String jjdwmc;
    
    /**
     * 报警电话时间
     */
    private String bjdhsjTime;
    
    /**
     * 接警单位
     */
    private String jjdw;
    
    /**
     * 联系电话
     */
    private String lxdh;
    
    /**
     * 报警内容
     */
    private String bjnr;
    
    /**
     * 登记单位名称
     */
    private String djdwmc;
    
    /**
     * 事发地点
     */
    private String sfdd;
    
    /**
     * 报警形式
     */
    private String bjxs;
    
    /**
     * 接警单编号
     */
    private String jjdbh;
    
    /**
     * 报警电话时间
     */
    private String bjdhsj;
    
    /**
     * 接警日期时间
     */
    private String jjrqsj;
    
    /**
     * 警情等级
     */
    private String jqdj;
    

    /**
     * 处警时间
     */
    private String cjsjTime;
    
    /**
     * 处警类别
     */
    private String cjlb;
    
    /**
     * 处警单位
     */
    private String cjdw;
    
    /**
     * 是否超时
     */
    private String sfcs;
    
    /**
     * 实施详细情况
     */
    private String ssxxqk;
    
    /**
     * 补充处理结果
     */
    private String bccljg;
    
    /**
     * 事发时间
     */
    private String sfsjxxTime;
    
    /**
     * 事发详情
     */
    private String sfxq;
    
    /**
     * 处理过程内容
     */
    private String cljgnr;
    
    /**
     * 事发结束时间
     */
    private String sfsjsxTime;
    
    /**
     * 登记人
     */
    private String djr;
    
    /**
     * 警情属性
     */
    private String jqsx;
    
    /**
     * 天气情况
     */
    private String tqqk;
    
    /**
     * 处警编号
     */
    private String cjbh;
    
    /**
     * 处警性质
     */
    private String cjxz;
    
    /**
     * 处警单位名称
     */
    private String cjdwmc;
    
    /**
     * 处警结果
     */
    private String cjjg;
    
    /**
     * 处警时间
     */
    private String cjsj;
    
    /**
     * 事发开始时间
     */
    private String sfsjxx;
    
    /**
     * 事发结束时间
     */
    private String sfsjsx;
    
    /**
     * 所属辖区
     */
    private String ssxq;
    
    /**
     * 处警人
     */
    private String cjr;
    
    /**
     * 处警警情标签
     */
    private String cjjqbq;
    
    /**
     * 标注地址名称
     */
    private String bzdzmc;
    
    /**
     * 区域类别
     */
    private String qylb;
    
    /**
     * 处警详细地点
     */
    private String cjxxdd;
    
    /**
     * X坐标
     */
    private String xzb;
    
    /**
     * Y坐标
     */
    private String yzb;
    
    /**
     * 警情备注
     */
    private String jqbz;
    
    /**
     * 附加标签
     */
    private String fjbq;
    
    /**
     * 标注状态
     */
    private String bzzt;
    
    /**
     * 地址标注
     */
    private String addressM;
    
    /**
     * 人员标注
     */
    private String personM;
    
    /**
     * 结果标注
     */
    private String resultM;
    
    /**
     * 时间标注
     */
    private String timeM;
    
    /**
     * 工具标注
     */
    private String toolM;
    
    /**
     * 原因标注
     */
    private String reasonM;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 审批结果
     */
    private String spjg;
    
    /**
     * 审批人
     */
    private String spr;
    
    /**
     * 审批时间
     */
    private String spTime;
    
    /**
     * 审批内容
     */
    private String spnr;
    
    /**
     * 标注人
     */
    private String bzr;
    
    /**
     * 标注时间
     */
    private String bzTime;
    
    /**
     * 标记
     */
    private String mark;
    
    /**
     * 标注人姓名
     */
    private String bzrxm;
    
    /**
     * 审批人姓名
     */
    private String sprxm;
    
    /**
     * 发生原因
     */
    private String fsyy;
    
    /**
     * 百度坐标
     */
    private String baidu;
    
    /**
     * 地址类型
     */
    private String dzType;
    
    /**
     * 地址
     */
    private String dsdz;
    
    /**
     * 机构编号
     */
    private String jgbh;
    
    /**
     * 地址ID
     */
    private String dzid;
    
    /**
     * 单位名称
     */
    private String dwmc;
    
    /**
     * 单位地址
     */
    private String dwdz;
    
    /**
     * 辖区
     */
    private String xq;
    
    /**
     * 建筑编号
     */
    private String buildNo;
    
    /**
     * 责任区
     */
    private String zrq;
} 