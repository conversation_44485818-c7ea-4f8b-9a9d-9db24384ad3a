package com.hl.building.domain.vo;

import com.hl.building.domain.tables.BuildingInfo;
import com.hl.building.domain.tables.CompanyInfo;
import com.hl.building.domain.tables.HouseInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class CompanyDistributionVo extends CompanyInfo {
    @ApiModelProperty("户室信息")
    private List<HouseInfo> houseInfoList;
}
