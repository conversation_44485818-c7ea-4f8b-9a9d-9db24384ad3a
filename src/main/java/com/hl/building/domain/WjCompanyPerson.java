package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description="wj_company_person")
@Data
@TableName(value = "wjhl.wj_company_person")
public class WjCompanyPerson {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value="id")
    private String id;

    /**
     * 公司id
     */
    @TableField(value = "company_id")
    @ApiModelProperty(value="公司id")
    private String companyId;

    /**
     * 人员id
     */
    @TableField(value = "person_id")
    @ApiModelProperty(value="人员id")
    private String personId;

    /**
     * 职位
1 是法人
2 是高管
     */
    @TableField(value = "\"position\"")
    @ApiModelProperty(value="职位,1 是法人,2 是高管")
    private Integer position;

    /**
     * 是否删除
     */
    @TableField(value = "is_delete")
    @ApiModelProperty(value="是否删除")
    private Integer isDelete;
}