package com.hl.building.domain;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description="wj_excel_data")
@Data
@TableName(value = "wjhl.wj_excel_data",autoResultMap = true)
public class WjExcelData {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @TableField(value = "import_config_id")
    @ApiModelProperty(value="")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long importConfigId;

    @TableField(value = "\"data\"",typeHandler = JacksonTypeHandler.class)
    @ApiModelProperty(value="")
    private JSONObject data;

    @TableField(value = "create_time")
    @ApiModelProperty(value="")
    private Date createTime;
}