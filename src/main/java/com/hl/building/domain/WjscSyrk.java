package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "harzon.wjsc_syrk")
public class WjscSyrk {
    @TableId(value = "syrkid", type = IdType.INPUT)
    private String syrkid;

    @TableField(value = "gmsfhm")
    private String gmsfhm;

    @TableField(value = "xm")
    private String xm;

    @TableField(value = "xb")
    private String xb;

    @TableField(value = "csrq")
    private Date csrq;

    @TableField(value = "rylb")
    private String rylb;

    /**
     * 人员状态（0-无效 1-有效）
     */
    @TableField(value = "ryzt")
    private String ryzt;

    @TableField(value = "hjddzmc")
    private String hjddzmc;

    @TableField(value = "hjdzrqdm")
    private String hjdzrqdm;

    @TableField(value = "xzddzmc")
    private String xzddzmc;

    @TableField(value = "xzddzdm")
    private String xzddzdm;

    @TableField(value = "xzdzrqdm")
    private String xzdzrqdm;

    @TableField(value = "rksj")
    private Date rksj;

    @TableField(value = "lxfs")
    private String lxfs;

    @TableField(value = "scrksj")
    private Date scrksj;

    @TableField(value = "zxgxsj")
    private Date zxgxsj;

    /**
     * 姓名拼音（全拼）
     */
    @TableField(value = "xmpy")
    private String xmpy;

    /**
     * 曾用名
     */
    @TableField(value = "cym")
    private String cym;

    /**
     * 民族
     */
    @TableField(value = "mz")
    private String mz;

    /**
     * 身高
     */
    @TableField(value = "sg")
    private Integer sg;

    /**
     * 国籍
     */
    @TableField(value = "gj")
    private String gj;

    /**
     * 户籍区县
     */
    @TableField(value = "hjqx")
    private String hjqx;

    /**
     * 婚姻状况
     */
    @TableField(value = "hyzk")
    private String hyzk;

    /**
     * 政治面貌
     */
    @TableField(value = "zzmm")
    private String zzmm;

    /**
     * 宗教信仰
     */
    @TableField(value = "zjxy")
    private String zjxy;

    /**
     * 户籍地地址代码
     */
    @TableField(value = "hjddzdm")
    private String hjddzdm;

    /**
     * 户籍地社区代码
     */
    @TableField(value = "hjddsqdm")
    private String hjddsqdm;

    /**
     * 现住地派出所代码
     */
    @TableField(value = "xzdpcsdm")
    private String xzdpcsdm;

    /**
     * 现住地分局代码
     */
    @TableField(value = "xzdfjdm")
    private String xzdfjdm;

    /**
     * 现住地市局代码
     */
    @TableField(value = "xzdsjdm")
    private String xzdsjdm;

    /**
     * 大平台人员编号
     */
    @TableField(value = "dpt_rybh")
    private String dptRybh;

    /**
     * 大平台人员属性
     */
    @TableField(value = "dpt_rysx")
    private String dptRysx;

    /**
     * 业务ID
     */
    @TableField(value = "ywid")
    private String ywid;

    /**
     * 坐标X
     */
    @TableField(value = "x")
    private BigDecimal x;

    /**
     * 坐标y
     */
    @TableField(value = "y")
    private BigDecimal y;

    /**
     * 时间戳
     */
    @TableField(value = "sjc")
    private Date sjc;

    /**
     * 户号
     */
    @TableField(value = "hh")
    private String hh;

    /**
     * 户口类型
     */
    @TableField(value = "hklx")
    private String hklx;

    /**
     * 与户主关系
     */
    @TableField(value = "yhzgx")
    private String yhzgx;

    /**
     * 文化程度
     */
    @TableField(value = "whcd")
    private String whcd;

    /**
     * 兵役状况
     */
    @TableField(value = "byzk")
    private String byzk;

    /**
     * 服务处所
     */
    @TableField(value = "fwcs")
    private String fwcs;

    /**
     * 职业类别
     */
    @TableField(value = "zylb")
    private String zylb;

    /**
     * 职务
     */
    @TableField(value = "zw")
    private String zw;

    /**
     * 分区ID
     */
    @TableField(value = "fqid")
    private String fqid;

    /**
     * 寄住人口标识
     */
    @TableField(value = "jzrkbs")
    private String jzrkbs;

    /**
     * 寄住人口更新时间戳
     */
    @TableField(value = "jzrksjc")
    private String jzrksjc;

    /**
     * 就业情况ID
     */
    @TableField(value = "jyqkid")
    private String jyqkid;

    /**
     * 照片ID
     */
    @TableField(value = "xpid")
    private String xpid;

    /**
     * 居住情况ID
     */
    @TableField(value = "jzqkid")
    private String jzqkid;

    /**
     * 人员属性
     */
    @TableField(value = "rysx")
    private String rysx;

    /**
     * 身份
     */
    @TableField(value = "sf")
    private String sf;

    @TableField(value = "jg")
    private String jg;

    @TableField(value = "\"operation\"")
    private String operation;

    @TableField(value = "last_modify_time")
    private Date lastModifyTime;

    /**
     * 照片连接
     */
    @TableField(value = "zplj")
    private String zplj;

    /**
     * 核实结果 1 新登记 2 已离开 3 死亡 4 无变化
     */
    @TableField(value = "syrkhsjg")
    private String syrkhsjg;

    /**
     * 操作时间
     */
    @TableField(value = "czsj")
    private Date czsj;

    /**
     * 流动人口标识:1-是；2-否
     */
    @TableField(value = "ldrkbs")
    private String ldrkbs;

    /**
     * 现住地责任区名称
     */
    @TableField(value = "xzdzrqmc")
    private String xzdzrqmc;
}