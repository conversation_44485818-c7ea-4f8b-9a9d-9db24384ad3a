package com.hl.building.domain;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hl.building.trans.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 风险预警任务配置
 */
@ApiModel(description = "风险预警任务配置")
@Data
@TableName(value = "wjhl.wj_ly_warn_task_config",autoResultMap = true)
public class WjLyWarnTaskConfig {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**
     * 任务名称
     */
    @TableField(value = "task_name")
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    /**
     * 任务行为
     */
    @TableField(value = "task_action")
    @ApiModelProperty(value = "任务行为")
    private Integer taskAction;

    /**
     * 任务说明
     */
    @TableField(value = "task_remark")
    @ApiModelProperty(value = "任务说明")
    private String taskRemark;

    /**
     * 任务基础分
     */
    @TableField(value = "task_base_score")
    @ApiModelProperty(value = "任务基础分")
    private BigDecimal taskBaseScore;

    /**
     * 周期 单位天
     */
    @TableField(value = "count_period")
    @ApiModelProperty(value = "周期 单位天")
    private Integer countPeriod;

    /**
     * 人员选择
     */
    @TableField(value = "task_attribute",typeHandler = Fastjson2TypeHandler.class)
    @ApiModelProperty(value = "人员选择")
    private JSONArray taskAttribute;

    /**
     * 任务开始时间
     */
    @TableField(value = "task_start")
    @ApiModelProperty(value = "任务开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskStart;

    /**
     * 任务结束时间
     */
    @TableField(value = "task_end")
    @ApiModelProperty(value = "任务结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskEnd;

    /**
     * 任务状态
     */
    @TableField(value = "task_status")
    @ApiModelProperty(value = "任务状态")
    private Short taskStatus;

    /**
     * 删除标识
     */
    @TableField(value = "is_delete")
    @ApiModelProperty(value = "删除标识")
    private Short isDelete;

    /**
     * 创建人员
     */
    @TableField(value = "create_user")
    @ApiModelProperty(value = "创建人员")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人员
     */
    @TableField(value = "update_user")
    @ApiModelProperty(value = "更新人员")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField(value = "actions_config")
    @ApiModelProperty(value = "关联行为id")
    private String actionsConfig;

    @TableField(exist = false)
    @ApiModelProperty(value = "关联行为名称")
    private String actionName;

    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ,mapper = "createUser")
    private JSONObject createUserInfo;
}