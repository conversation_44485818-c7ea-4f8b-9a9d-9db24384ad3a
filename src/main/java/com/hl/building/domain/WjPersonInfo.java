package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 个人信息
 */
@ApiModel(description = "个人信息")
@Data
@TableName(value = "wjhl.wj_person_info")
public class WjPersonInfo {
    /**
     * 主键
     */
    @TableId(value = "person_id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value="主键")
    private String personId;

    /**
     * 姓名
     */
    @TableField(value = "\"name\"")
    @ApiModelProperty(value="姓名")
    private String name;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value="身份证号")
    private String idCard;

    /**
     * 手机号
     */
    @TableField(value = "phone")
    @ApiModelProperty(value="手机号")
    private String phone;

    /**
     * 户籍地
     */
    @TableField(value = "hjxz")
    @ApiModelProperty(value="户籍地")
    private String hjxz;

    /**
     * 现住地
     */
    @TableField(value = "xzz")
    @ApiModelProperty(value="现住地")
    private String xzz;

    /**
     * 0非重点人员，1重点人员
     */
    @TableField(value = "is_focus")
    @ApiModelProperty(value="0非重点人员，1重点人员")
    private Integer isFocus;

    /**
     * 创建用户
     */
    @TableField(value = "create_user")
    @ApiModelProperty(value="创建用户")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
     * 更新用户
     */
    @TableField(value = "update_user")
    @ApiModelProperty(value="更新用户")
    private String updateUser;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField(value = "is_delete")
    @ApiModelProperty(value="是否删除")
    private Integer isDelete;

    /**
     * 职位名称
     */
    @TableField(value = "zw_mc")
    @ApiModelProperty(value="职位名称")
    private String zwMc;

    /**
     * 关联机构编号
     */
    @TableField(value = "jgbh")
    @ApiModelProperty(value="关联机构编号")
    private String jgbh;

    /**
     * 公司编号
     */
    @TableField(value = "company_id")
    @ApiModelProperty(value="公司编号")
    private String companyId;
}