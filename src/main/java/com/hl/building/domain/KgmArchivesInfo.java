package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description="kgm_archives_info")
@Data
@TableName(value = "harzon.kgm_archives_info")
public class KgmArchivesInfo {
    @TableId(value = "uuid", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value="")
    private String uuid;

    @TableField(value = "create_dept_name")
    @ApiModelProperty(value="")
    private String createDeptName;

    @TableField(value = "create_dept_uid")
    @ApiModelProperty(value="")
    private String createDeptUid;

    @TableField(value = "group_number")
    @ApiModelProperty(value="")
    private String groupNumber;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value="")
    private String createUserName;

    @TableField(value = "create_user_uid")
    @ApiModelProperty(value="")
    private String createUserUid;

    @TableField(value = "create_time")
    @ApiModelProperty(value="")
    private Date createTime;

    @TableField(value = "group_name")
    @ApiModelProperty(value="")
    private String groupName;

    @TableField(value = "group_public_kind")
    @ApiModelProperty(value="")
    private String groupPublicKind;

    @TableField(value = "group_level")
    @ApiModelProperty(value="")
    private String groupLevel;

    @TableField(value = "group_state")
    @ApiModelProperty(value="")
    private String groupState;

    @TableField(value = "group_source")
    @ApiModelProperty(value="")
    private String groupSource;

    @TableField(value = "ew_group_uid")
    @ApiModelProperty(value="")
    private String ewGroupUid;
}