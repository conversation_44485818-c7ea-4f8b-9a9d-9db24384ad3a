package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description="wj_company_house")
@Data
@TableName(value = "wjhl.wj_company_house")
public class WjCompanyHouse {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 公司id
     */
    @TableField(value = "company_id")
    @ApiModelProperty(value="公司id")
    private String companyId;

    /**
     * 户室id
     */
    @TableField(value = "house_id")
    @ApiModelProperty(value="户室id")
    private String houseId;

    /**
     * 是否活跃
     */
    @TableField(value = "is_active")
    @ApiModelProperty(value="是否活跃")
    private Integer isActive;
}