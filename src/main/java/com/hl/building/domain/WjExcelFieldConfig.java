package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description="wj_excel_field_config")
@Data
@TableName(value = "wjhl.wj_excel_field_config")
public class WjExcelFieldConfig {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @TableField(value = "import_config_id")
    @ApiModelProperty(value="")
    private Long importConfigId;

    @TableField(value = "field_name")
    @ApiModelProperty(value="")
    private String fieldName;

    @TableField(value = "\"column_name\"")
    @ApiModelProperty(value="")
    private String columnName;

    @TableField(value = "data_type")
    @ApiModelProperty(value="")
    private String dataType;

    @TableField(value = "field_order")
    @ApiModelProperty(value="")
    private Integer fieldOrder;

    @TableField(value = "is_required")
    @ApiModelProperty(value="")
    private Boolean isRequired;
}