package com.hl.building.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@ApiModel(description="wj_excel_import_config")
@Data
@TableName(value = "wjhl.wj_excel_import_config")
public class WjExcelImportConfig {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value="")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @TableField(value = "\"name\"")
    @ApiModelProperty(value="")
    private String name;

    @TableField(value = "\"table_name\"")
    @ApiModelProperty(value="")
    private String tableName;

    @TableField(value = "create_time")
    @ApiModelProperty(value="")
    private Date createTime;

    @TableField(value = "remark")
    @ApiModelProperty(value="")
    private String remark;
}