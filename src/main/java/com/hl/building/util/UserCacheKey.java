package com.hl.building.util;

public interface UserCacheKey {


    /**
     * 以身份证为主键用户信息
     */
    String USER = "user";

    /**
     * 以警号为主键用户信息
     */
    String POLICE = "police";

    /**
     * 角色信息
     */
    String ROLE = "role";

    /**
     * 圈层信息
     */
    String CIRCLE = "circle";

    /**
     * 资源信息
     */
    String RESOURCE = "resource";

    /**
     * 用户下的角色信息
     */
    String USER_ROLE = "user_role";

    /**
     * 用户下的圈层信息
     */
    String USER_CIRCLE = "user_circle";

    /**
     * 用户下的资源信息
     */
    String USER_RESOURCE = "user_resource";

    /**
     * 圈层下的用户信息
     */
    String CIRCLE_USER = "circle_user";

    /**
     * 单位+职务+身份证对应的用户信息
     */
    String ORG_JOB_USER = "org_job_user";

    /**
     * 用户+单位+职务对应的角色信息
     */
    String USER_ORG_ROLE = "user_org_role";

    /**
     * 单位信息
     */
    String ORGANIZATION = "organization";

    String PROJECT = "project";


    String ROLE_USER = "role_user";

}
