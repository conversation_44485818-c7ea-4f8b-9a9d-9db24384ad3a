package com.hl.building.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

public class DateRangeChecker {

    public static boolean isBetween(DateTime date,DateTime start, DateTime end) {
        return (date.isAfterOrEquals(start) && date.isBeforeOrEquals(end));
    }

    /**
     * 是否本周
     * @param date
     * @return
     */
    public static boolean isThisWeek(DateTime date){
        DateTime start = DateUtil.beginOfWeek(DateUtil.date());
        DateTime end = DateUtil.endOfWeek(DateUtil.date());
        return isBetween(date,start,end);
    }

    public static boolean isThisMonth(DateTime date){
        DateTime start = DateUtil.beginOfMonth(DateUtil.date());
        DateTime end = DateUtil.endOfMonth(DateUtil.date());
        return isBetween(date,start,end);
    }
    public static boolean isThisYear(DateTime date){
        DateTime start = DateUtil.beginOfYear(DateUtil.date());
        DateTime end = DateUtil.endOfYear(DateUtil.date());
        return isBetween(date,start,end);
    }

    public static boolean isLastWeek(DateTime date){
        DateTime start = DateUtil.beginOfWeek(DateUtil.offsetWeek(DateUtil.date(), -1));
        DateTime end = DateUtil.endOfWeek(DateUtil.offsetWeek(DateUtil.date(), -1));
        return isBetween(date,start,end);
    }
    public static boolean isLastMonth(DateTime date){
        DateTime start = DateUtil.beginOfMonth(DateUtil.offsetMonth(DateUtil.date(), -1));
        DateTime end = DateUtil.endOfMonth(DateUtil.offsetMonth(DateUtil.date(), -1));
        return isBetween(date,start,end);
    }
    public static boolean isLastYear(DateTime date){
        DateTime start = DateUtil.beginOfYear(DateUtil.offsetMonth(DateUtil.date(), -12));
        DateTime end = DateUtil.endOfYear(DateUtil.offsetMonth(DateUtil.date(), -12));
        return isBetween(date,start,end);
    }
}
