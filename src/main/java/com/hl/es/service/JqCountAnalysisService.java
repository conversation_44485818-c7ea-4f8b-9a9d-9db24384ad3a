package com.hl.es.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.building.domain.WjBuildingInfo;
import com.hl.building.domain.WjCompanyInfo;
import com.hl.building.domain.dto.BuildCountDetailDTO;
import com.hl.building.domain.dto.JqCountDetailInfoDTO;
import com.hl.building.service.JqTransService;
import com.hl.building.service.WjBuildingInfoService;
import com.hl.building.service.WjCompanyInfoService;
import com.hl.building.util.DateRangeChecker;
import com.hl.es.domain.ViewEsJqBz;
import com.hl.es.domain.dto.JqCountDTO;
import com.hl.es.mapper.ViewEsJqBzMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class JqCountAnalysisService {

    private final WjBuildingInfoService wjBuildingInfoService;

    private final WjCompanyInfoService wjCompanyInfoService;

    private final ViewEsJqBzMapper viewEsJqBzMapper;

    private final JqTransService jqTransService;

    //    @EventListener(ApplicationReadyEvent.class)
    public void analysis() {
        // 获取楼宇
        List<WjBuildingInfo> list = wjBuildingInfoService.list();

        for (WjBuildingInfo buildingInfo : list) {
            String buildId = buildingInfo.getBuildId();
            List<WjCompanyInfo> companyInfoList = wjCompanyInfoService.list(Wrappers.<WjCompanyInfo>lambdaQuery()
                    .eq(WjCompanyInfo::getBuildId, buildId));

            List<String> dzIdList = companyInfoList.stream().map(WjCompanyInfo::getDzId).collect(Collectors.toList());

            List<String> jgBhList = companyInfoList.stream().map(WjCompanyInfo::getJgbh).collect(Collectors.toList());
            if (dzIdList.isEmpty() && jgBhList.isEmpty()) {
                continue;
            }

            LambdaEsQueryWrapper<ViewEsJqBz> queryWrapper = new LambdaEsQueryWrapper<>();
            queryWrapper.in(!dzIdList.isEmpty(), ViewEsJqBz::getDzid, dzIdList)
                    .or()
                    .in(!jgBhList.isEmpty(), ViewEsJqBz::getJgbh, jgBhList);
            List<ViewEsJqBz> viewEsJqBzs = viewEsJqBzMapper.selectList(queryWrapper);
            // 获取这个楼宇所有的数据
            DateTime date = DateUtil.date();
            JqCountDTO jqCountDTO = new JqCountDTO();
            Integer thisWeekCount = jqCountDTO.getThisWeekCount();
            Integer lastWeekCount = jqCountDTO.getLastWeekCount();
            Integer thisMonthCount = jqCountDTO.getThisMonthCount();
            Integer lastMonthCount = jqCountDTO.getLastMonthCount();
            Integer thisYearCount = jqCountDTO.getThisYearCount();
            Integer lastYearCount = jqCountDTO.getLastYearCount();
            Integer totalCount = jqCountDTO.getTotalCount();

            Map<String, Integer> mainTypeMap = new HashMap<>();

            for (ViewEsJqBz viewEsJqBz : viewEsJqBzs) {
                String bjdhsjTime = viewEsJqBz.getBjdhsjTime();
                totalCount++;
                if (StrUtil.isNotBlank(bjdhsjTime)) {
                    DateTime dateTime = DateUtil.parse(bjdhsjTime);
                    if (DateRangeChecker.isThisWeek(dateTime)) {
                        thisWeekCount++;
                    }
                    if (DateRangeChecker.isLastWeek(dateTime)) {
                        lastWeekCount++;
                    }
                    if (DateRangeChecker.isThisMonth(dateTime)) {
                        thisMonthCount++;
                    }
                    if (DateRangeChecker.isLastMonth(dateTime)) {
                        lastMonthCount++;
                    }
                    if (DateRangeChecker.isThisYear(dateTime)) {
                        thisYearCount++;
                    }
                    if (DateRangeChecker.isLastYear(dateTime)) {
                        lastYearCount++;
                    }
                }
                String cjlb = viewEsJqBz.getCjlb();
                if (StrUtil.isNotBlank(cjlb)) {
                    mainTypeMap.merge(cjlb, 1, Integer::sum);
                }
            }
            List<JqCountDTO.MainType> mainTypeList = new ArrayList<>();

            for (Map.Entry<String, Integer> entry : mainTypeMap.entrySet()) {
                String key = entry.getKey();
                Integer value = entry.getValue();
                JqCountDTO.MainType mainType = new JqCountDTO.MainType();
                mainType.setTypeCode(key);
                mainType.setCount(value);
                String dictName = jqTransService.getDictName(JqTransService.DictType.JJCJN, key);
                mainType.setTypeName(dictName);
                mainTypeList.add(mainType);
            }
            jqCountDTO.setTotalCount(totalCount);
            jqCountDTO.setThisWeekCount(thisWeekCount);
            jqCountDTO.setLastWeekCount(lastWeekCount);
            jqCountDTO.setThisMonthCount(thisMonthCount);
            jqCountDTO.setLastMonthCount(lastMonthCount);
            jqCountDTO.setThisYearCount(thisYearCount);
            jqCountDTO.setLastYearCount(lastYearCount);
            jqCountDTO.setMainType(mainTypeList);
            buildingInfo.setJqCount(jqCountDTO);
            wjBuildingInfoService.updateById(buildingInfo);
            log.info(jqCountDTO.toString());
        }
    }

    public JqCountDetailInfoDTO countJqDetail(BuildCountDetailDTO buildCountDetailDTO) {
        JqCountDetailInfoDTO dto = new JqCountDetailInfoDTO();
        String buildId = buildCountDetailDTO.getBuildId();
        // 查询楼宇
        WjBuildingInfo buildingInfo = wjBuildingInfoService.getById(buildId);
        dto.setBuildingInfo(buildingInfo);

        // 获取楼宇关联的警情数据
        List<WjCompanyInfo> companyInfoList = wjCompanyInfoService.list(Wrappers.<WjCompanyInfo>lambdaQuery()
                .eq(WjCompanyInfo::getBuildId, buildId));

        List<String> dzIdList = companyInfoList.stream().map(WjCompanyInfo::getDzId).collect(Collectors.toList());
        List<String> jgBhList = companyInfoList.stream().map(WjCompanyInfo::getJgbh).collect(Collectors.toList());

//        LambdaEsQueryWrapper<ViewEsJqBz> queryWrapper = new LambdaEsQueryWrapper<>();
//        queryWrapper.in(!dzIdList.isEmpty(), ViewEsJqBz::getDzid, dzIdList)
//                .or()
//                .in(!jgBhList.isEmpty(), ViewEsJqBz::getJgbh, jgBhList);
//        List<ViewEsJqBz> viewEsJqBzs = viewEsJqBzMapper.selectList(queryWrapper);

        Map<String, List<ViewEsJqBz>> dzMap = new HashMap<>();
        if (!dzIdList.isEmpty()){
            List<ViewEsJqBz> viewEsJqBzs = viewEsJqBzMapper.selectList(new LambdaEsQueryWrapper<ViewEsJqBz>()
                    .in(ViewEsJqBz::getDzid,dzIdList));
            for (ViewEsJqBz jqBz : viewEsJqBzs) {
                String dzid = jqBz.getDzid();
                if (StrUtil.isNotBlank(dzid)) {
                    String dwdz = jqBz.getDwdz();
                    String str = dzid + "_" + dwdz;
                    if (dzMap.containsKey(str)) {
                        dzMap.get(str).add(jqBz);
                    } else {
                        List<ViewEsJqBz> list = new ArrayList<>();
                        list.add(jqBz);
                        dzMap.put(str, list);
                    }
                }
            }
        }

        Map<String, List<ViewEsJqBz>> jgBhMap = new HashMap<>();
        if (!jgBhList.isEmpty()){
            List<ViewEsJqBz> viewEsJqBzs = viewEsJqBzMapper.selectList(new LambdaEsQueryWrapper<ViewEsJqBz>()
                    .in(ViewEsJqBz::getJgbh, jgBhList));
            for (ViewEsJqBz jqBz : viewEsJqBzs) {
                String jgbh = jqBz.getJgbh();
                if (StrUtil.isNotBlank(jgbh)) {
                    String dwmc = jqBz.getDwmc();
                    String str = jgbh + "_" + dwmc;
                    if (jgBhMap.containsKey(str)) {
                        jgBhMap.get(str).add(jqBz);
                    } else {
                        List<ViewEsJqBz> list = new ArrayList<>();
                        list.add(jqBz);
                        jgBhMap.put(str, list);
                    }
                }
            }
        }

        List<JqCountDetailInfoDTO.CompanyDetail> companyDetailList = new ArrayList<>();
        for (Map.Entry<String, List<ViewEsJqBz>> entry : jgBhMap.entrySet()) {
            String key = entry.getKey();
            String[] split = key.split("_");
            JqCountDetailInfoDTO.CompanyDetail companyDetail = new JqCountDetailInfoDTO.CompanyDetail();
            companyDetail.setCompanyName(split[1]);
            companyDetail.setJgbh(split[0]);
            List<ViewEsJqBz> list = entry.getValue();
            List<JqCountDetailInfoDTO.MainType> mainTypes = analysisMainType(list);
            companyDetail.setMainTypeList(mainTypes);
            companyDetail.setJqCount(list.size());
            companyDetailList.add(companyDetail);
        }
        dto.setCompanyDetailList(companyDetailList);

        List<JqCountDetailInfoDTO.HouseDetail> houseDetailList = new ArrayList<>();
        for (Map.Entry<String, List<ViewEsJqBz>> entry : dzMap.entrySet()) {
            String key = entry.getKey();
            String[] split = key.split("_");
            List<ViewEsJqBz> list = entry.getValue();
            JqCountDetailInfoDTO.HouseDetail houseDetail = new JqCountDetailInfoDTO.HouseDetail();
            houseDetail.setHouseName(split[1]);
            houseDetail.setDzId(split[0]);
            List<JqCountDetailInfoDTO.MainType> mainTypes = analysisMainType(list);
            houseDetail.setMainTypeList(mainTypes);
            houseDetail.setJqCount(list.size());
            houseDetailList.add(houseDetail);
        }
        dto.setHouseDetailList(houseDetailList);

        return dto;

    }


    private List<JqCountDetailInfoDTO.MainType> analysisMainType(List<ViewEsJqBz> jqBzList) {
        Map<String, Integer> mainTypeMap = new HashMap<>();
        jqBzList.forEach(jq->{
            String cjlb = jq.getCjlb();
            if (StrUtil.isNotBlank(cjlb)) {
                mainTypeMap.merge(cjlb, 1, Integer::sum);
            }
        });
        List<JqCountDetailInfoDTO.MainType> mainTypeList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : mainTypeMap.entrySet()) {
            String key = entry.getKey();
            Integer count = entry.getValue();
            String dictName = jqTransService.getDictName(JqTransService.DictType.JJCJN, key);
            JqCountDetailInfoDTO.MainType mainType = new JqCountDetailInfoDTO.MainType();
            mainType.setCount(count);
            mainType.setTypeName(dictName);
            mainType.setTypeCode(entry.getKey());
            mainTypeList.add(mainType);
        }

        mainTypeList.sort((o1, o2) -> o2.getCount() - o1.getCount());
        mainTypeList = mainTypeList.subList(0, Math.min(3, mainTypeList.size()));
        return mainTypeList;
    }


    public EsPageInfo<ViewEsJqBz> pageJqList(BuildCountDetailDTO buildCountDetailDTO) {
        String buildId = buildCountDetailDTO.getBuildId();
        // 获取楼宇关联的警情数据
        List<WjCompanyInfo> companyInfoList = wjCompanyInfoService.list(Wrappers.<WjCompanyInfo>lambdaQuery()
                .eq(WjCompanyInfo::getBuildId, buildId));

        List<String> dzIdList = companyInfoList.stream().map(WjCompanyInfo::getDzId).collect(Collectors.toList());
        List<String> jgBhList = companyInfoList.stream().map(WjCompanyInfo::getJgbh).collect(Collectors.toList());

        LambdaEsQueryWrapper<ViewEsJqBz> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.and(q->{
            q.in(!dzIdList.isEmpty(), ViewEsJqBz::getDzid, dzIdList)
                    .or()
                    .in(!jgBhList.isEmpty(), ViewEsJqBz::getJgbh, jgBhList);
        });
        queryWrapper.eq(StrUtil.isNotBlank(buildCountDetailDTO.getJgbh()), ViewEsJqBz::getJgbh, buildCountDetailDTO.getJgbh())
                .eq(StrUtil.isNotBlank(buildCountDetailDTO.getDzid()), ViewEsJqBz::getDzid, buildCountDetailDTO.getDzid())
                .eq(StrUtil.isNotBlank(buildCountDetailDTO.getCjlb()), ViewEsJqBz::getCjlb, buildCountDetailDTO.getCjlb());

        EsPageInfo<ViewEsJqBz> viewEsJqBzEsPageInfo = viewEsJqBzMapper.pageQuery(queryWrapper, buildCountDetailDTO.getPage(), buildCountDetailDTO.getLimit());
        return viewEsJqBzEsPageInfo;


    }
}
