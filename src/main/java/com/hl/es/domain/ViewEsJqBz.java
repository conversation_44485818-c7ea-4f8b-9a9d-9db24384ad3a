package com.hl.es.domain;

import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;

import java.util.Date;

@Data
@IndexName(value = "view_es_jq_bz")
public class ViewEsJqBz {
    /**
     * 接警编号
     */
    @IndexId(value = "jjbh")
    private String jjbh;

    /**
     * 处警标识
     */
    @IndexField(value = "cjbs")
    private String cjbs;

    /**
     * 接警报警人
     */
    @IndexField(value = "bjr")
    private String bjr;

    /**
     * 报警类型
     */
    @IndexField(value = "bjlx")
    private String bjlx;

    /**
     * 接警单位名称
     */
    @IndexField(value = "jjdwmc")
    private String jjdwmc;

    /**
     * 接警报警时间
     */
    @IndexField(value = "bjdhsj_time")
    private String bjdhsjTime;

    /**
     * 接警单位
     */
    @IndexField(value = "jjdw")
    private String jjdw;

    /**
     * 接警报警人联系电话
     */
    @IndexField(value = "lxdh")
    private String lxdh;

    /**
     * 报警内容
     */
    @IndexField(value = "bjnr")
    private String bjnr;

    /**
     * 接警登记单位名称
     */
    @IndexField(value = "djdwmc")
    private String djdwmc;

    /**
     * 发生地点
     */
    @IndexField(value = "sfdd")
    private String sfdd;

    /**
     * 报警方式
     */
    @IndexField(value = "bjxs")
    private String bjxs;

    /**
     * 接警登记编号
     */
    @IndexField(value = "jjdbh")
    private String jjdbh;

    /**
     * 警情等级
     */
    @IndexField(value = "bjdhsj")
    private Date bjdhsj;

    /**
     * 接警日期时间
     */
    @IndexField(value = "jjrqsj")
    private String jjrqsj;

    /**
     * 警情等级
     */
    @IndexField(value = "jqdj")
    private String jqdj;

    /**
     * 警情坐标-X
     */
    @IndexField(value = "gis_x")
    private String gisX;

    /**
     * 警情坐标-y
     */
    @IndexField(value = "gis_y")
    private String gisY;

    /**
     * 处警处警时间
     */
    @IndexField(value = "cjsj_time")
    private String cjsjTime;

    /**
     * 处警类别
     */
    @IndexField(value = "cjlb")
    private String cjlb;

    /**
     * 处警单位
     */
    @IndexField(value = "cjdw")
    private String cjdw;

    /**
     * 事发场所
     */
    @IndexField(value = "sfcs")
    private String sfcs;

    /**
     * 损失详细情况
     */
    @IndexField(value = "ssxxqk")
    private String ssxxqk;

    /**
     * 补充处理结果
     */
    @IndexField(value = "bccljg")
    private String bccljg;

    /**
     * 事发时间下限
     */
    @IndexField(value = "sfsjxx_time")
    private String sfsjxxTime;

    /**
     * 事发星期
     */
    @IndexField(value = "sfxq")
    private String sfxq;

    /**
     * 处理结果内容
     */
    @IndexField(value = "cljgnr")
    private String cljgnr;

    /**
     * 事发时间上限
     */
    @IndexField(value = "sfsjsx_time")
    private String sfsjsxTime;

    /**
     * 登记人
     */
    @IndexField(value = "djr")
    private String djr;

    /**
     * 警情属性
     */
    @IndexField(value = "jqsx")
    private String jqsx;

    /**
     * 天气情况
     */
    @IndexField(value = "tqqk")
    private String tqqk;

    /**
     * 处警编号
     */
    @IndexField(value = "cjbh")
    private String cjbh;

    /**
     * 处警详址
     */
    @IndexField(value = "cjxz")
    private String cjxz;

    /**
     * 处警处警单位名称
     */
    @IndexField(value = "cjdwmc")
    private String cjdwmc;

    /**
     * 处警结果
     */
    @IndexField(value = "cjjg")
    private String cjjg;

    /**
     * 处警处警时间
     */
    @IndexField(value = "cjsj")
    private Date cjsj;

    /**
     * 事发时间下限
     */
    @IndexField(value = "sfsjxx")
    private Date sfsjxx;

    /**
     * 事发时间上限
     */
    @IndexField(value = "sfsjsx")
    private Date sfsjsx;

    /**
     * 所属辖区
     */
    @IndexField(value = "ssxq")
    private String ssxq;

    /**
     * 处警人
     */
    @IndexField(value = "cjr")
    private String cjr;

    /**
     * 警情标签
     */
    @IndexField(value = "cjjqbq")
    private String cjjqbq;

    /**
     * 地址补充
     */
    @IndexField(value = "bzdzmc")
    private String bzdzmc;

    /**
     * 区域类别
     */
    @IndexField(value = "qylb")
    private String qylb;

    /**
     * 处警信息地点
     */
    @IndexField(value = "cjxxdd")
    private String cjxxdd;

    /**
     * 警情坐标-X
     */
    @IndexField(value = "xzb")
    private String xzb;

    /**
     * 警情坐标-Y'
     */
    @IndexField(value = "yzb")
    private String yzb;

    /**
     * 警情标注标签
     */
    @IndexField(value = "jqbz")
    private String jqbz;

    /**
     * 分局标签
     */
    @IndexField(value = "fjbq")
    private String fjbq;

    /**
     * 标注状态  - 未标注 0
  - 已标注审核通过 1
  - 已标注待审核 2
  - 已标注审核未通过 3
     */
    @IndexField(value = "bzzt")
    private Integer bzzt;

    /**
     * 地址标签
     */
    @IndexField(value = "addressM")
    private String addressm;

    /**
     * 人员标签
     */
    @IndexField(value = "personM")
    private String personm;

    /**
     * 结果标签
     */
    @IndexField(value = "resultM")
    private String resultm;

    /**
     * 时间标签
     */
    @IndexField(value = "timeM")
    private String timem;

    /**
     * 手段标签
     */
    @IndexField(value = "toolM")
    private String toolm;

    /**
     * 原因标签
     */
    @IndexField(value = "reasonM")
    private String reasonm;

    /**
     * 标注单位
     */
    @IndexField(value = "unit")
    private String unit;

    /**
     * 审批结果:-1未标注 0已标注待审批 1同意 2退回
     */
    @IndexField(value = "spjg")
    private Integer spjg;

    /**
     * 审批人
     */
    @IndexField(value = "spr")
    private String spr;

    /**
     * 审批时间
     */
    @IndexField(value = "sp_time")
    private String spTime;

    /**
     * 审批内容
     */
    @IndexField(value = "spnr")
    private String spnr;

    /**
     * 标注人
     */
    @IndexField(value = "bzr")
    private String bzr;

    /**
     * 标注时间
     */
    @IndexField(value = "bz_time")
    private String bzTime;

    /**
     * 0未标注1已标注
     */
    @IndexField(value = "mark")
    private Integer mark;

    @IndexField(value = "bzrxm")
    private String bzrxm;

    @IndexField(value = "sprxm")
    private String sprxm;

    /**
     * 事发原因 dict 77
     */
    @IndexField(value = "fsyy")
    private String fsyy;

    /**
     * 百度提示
     */
    @IndexField(value = "baidu")
    private String baidu;

    @IndexField(value = "dz_type")
    private String dzType;

    @IndexField(value = "dsdz")
    private String dsdz;

    @IndexField(value = "jgbh")
    private String jgbh;

    @IndexField(value = "dzid")
    private String dzid;

    @IndexField(value = "dwmc")
    private String dwmc;

    @IndexField(value = "dwdz")
    private String dwdz;

    @IndexField(value = "xq")
    private String xq;

    @IndexField(value = "build_no")
    private String buildNo;

    @IndexField(value = "zrq")
    private String zrq;

    /**
     * 事件标签Id
     */
    @IndexField(value = "eventId")
    private String eventid;

    /**
     * 所属责任区
     */
    @IndexField(value = "sszrq")
    private String sszrq;
}