package com.hl.es.domain.dto;

import lombok.Data;

import java.util.List;

@Data
public class JqCountDTO {

    private Integer thisWeekCount = 0;
    private Integer thisMonthCount = 0;
    private Integer thisYearCount = 0;

    private Integer lastWeekCount = 0;
    private Integer lastMonthCount = 0;
    private Integer lastYearCount = 0;

    private Integer totalCount = 0;

    private List<MainType> mainType;

    @Data
    public static class MainType {
        private String typeName;

        private String typeCode;

        private Integer count;
    }
}
