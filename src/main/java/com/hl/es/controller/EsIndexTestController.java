package com.hl.es.controller;

import com.hl.common.domain.R;
import com.hl.es.mapper.ViewEsJqBzMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/esIndexTest")
@RequiredArgsConstructor
public class EsIndexTestController {

    private final ViewEsJqBzMapper viewEsJqBzMapper;

    @GetMapping
    public R<?> createIndex() {
        viewEsJqBzMapper.deleteIndex();
        viewEsJqBzMapper.createIndex();
        return R.ok();
    }
}
