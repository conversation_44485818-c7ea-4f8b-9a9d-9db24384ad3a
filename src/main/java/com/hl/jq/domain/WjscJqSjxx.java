package com.hl.jq.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 警情分析常州市渉警信息
 */
@Data
@TableName(value = "wjsc_jq_sjxx")
public class WjscJqSjxx {
    @TableId(value = "uuid", type = IdType.INPUT)
    private String uuid;

    @TableField(value = "jjbh")
    private String jjbh;

    @TableField(value = "gmsfhm")
    private String gmsfhm;

    @TableField(value = "xm")
    private String xm;

    /**
     * 涉警类别
     */
    @TableField(value = "sjlb")
    private String sjlb;

    /**
     * 登记时间格式
     */
    @TableField(value = "djsj_time")
    private String djsjTime;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "djsj")
    private Date djsj;

    @TableField(value = "personM")
    private String personm;

    @TableField(value = "personMs")
    private String personms;

    @TableField(value = "jgbh")
    private String jgbh;

    @TableField(value = "dwmc")
    private String dwmc;

    @TableField(value = "zxbs")
    private Integer zxbs;
}