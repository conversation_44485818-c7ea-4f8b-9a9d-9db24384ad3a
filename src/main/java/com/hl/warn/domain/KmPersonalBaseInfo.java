package com.hl.warn.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 人员基本信息表
 */
@ApiModel(description = "人员基本信息表")
@Data
@TableName(value = "km_personal_base_info")
public class KmPersonalBaseInfo {
    @TableField(value = "rkbm")
    @ApiModelProperty(value = "")
    private String rkbm;

    @TableField(value = "gmsfhm")
    @ApiModelProperty(value = "")
    private String gmsfhm;

    @TableField(value = "xm")
    @ApiModelProperty(value = "")
    private String xm;

    @TableField(value = "xmhypy")
    @ApiModelProperty(value = "")
    private String xmhypy;

    @TableField(value = "cym")
    @ApiModelProperty(value = "")
    private String cym;

    @TableField(value = "cymhypy")
    @ApiModelProperty(value = "")
    private String cymhypy;

    @TableField(value = "wwx")
    @ApiModelProperty(value = "")
    private String wwx;

    @TableField(value = "wwm")
    @ApiModelProperty(value = "")
    private String wwm;

    @TableField(value = "cyzjdm")
    @ApiModelProperty(value = "")
    private String cyzjdm;

    @TableField(value = "zjhm")
    @ApiModelProperty(value = "")
    private String zjhm;

    @TableField(value = "bmch")
    @ApiModelProperty(value = "")
    private String bmch;

    @TableField(value = "bmchhypy")
    @ApiModelProperty(value = "")
    private String bmchhypy;

    @TableField(value = "xbdm")
    @ApiModelProperty(value = "")
    private String xbdm;

    @TableField(value = "mzdm")
    @ApiModelProperty(value = "")
    private String mzdm;

    @TableField(value = "csrq")
    @ApiModelProperty(value = "")
    private Date csrq;

    @TableField(value = "cssj")
    @ApiModelProperty(value = "")
    private Date cssj;

    @TableField(value = "cszmbh")
    @ApiModelProperty(value = "")
    private String cszmbh;

    @TableField(value = "csdgjhdqdm")
    @ApiModelProperty(value = "")
    private String csdgjhdqdm;

    @TableField(value = "csd_qhnxxdz")
    @ApiModelProperty(value = "")
    private String csdQhnxxdz;

    @TableField(value = "csd_xzqhdm")
    @ApiModelProperty(value = "")
    private String csdXzqhdm;

    @TableField(value = "jggjdqdm")
    @ApiModelProperty(value = "")
    private String jggjdqdm;

    @TableField(value = "jg_xzqhdm")
    @ApiModelProperty(value = "")
    private String jgXzqhdm;

    @TableField(value = "jg_qhnxxdz")
    @ApiModelProperty(value = "")
    private String jgQhnxxdz;

    @TableField(value = "zjxydm")
    @ApiModelProperty(value = "")
    private String zjxydm;

    @TableField(value = "zzmmdm")
    @ApiModelProperty(value = "")
    private String zzmmdm;

    @TableField(value = "xldm")
    @ApiModelProperty(value = "")
    private String xldm;

    @TableField(value = "hyzkdm")
    @ApiModelProperty(value = "")
    private String hyzkdm;

    @TableField(value = "byzkdm")
    @ApiModelProperty(value = "")
    private String byzkdm;

    @TableField(value = "sg")
    @ApiModelProperty(value = "")
    private String sg;

    @TableField(value = "xxdm")
    @ApiModelProperty(value = "")
    private String xxdm;

    @TableField(value = "grsfxldm")
    @ApiModelProperty(value = "")
    private String grsfxldm;

    @TableField(value = "saryzcdm")
    @ApiModelProperty(value = "")
    private String saryzcdm;

    @TableField(value = "sfhzw")
    @ApiModelProperty(value = "")
    private String sfhzw;

    @TableField(value = "zy")
    @ApiModelProperty(value = "")
    private String zy;

    @TableField(value = "zylbdm")
    @ApiModelProperty(value = "")
    private String zylbdm;

    @TableField(value = "fwcs")
    @ApiModelProperty(value = "")
    private String fwcs;

    @TableField(value = "lxdh")
    @ApiModelProperty(value = "")
    private String lxdh;

    @TableField(value = "zwsp")
    @ApiModelProperty(value = "")
    private String zwsp;

    @TableField(value = "swrq")
    @ApiModelProperty(value = "")
    private Date swrq;

    @TableField(value = "swzmbh")
    @ApiModelProperty(value = "")
    private String swzmbh;

    @TableField(value = "gjdm")
    @ApiModelProperty(value = "")
    private String gjdm;

    @TableField(value = "hjdz_xzqhdm")
    @ApiModelProperty(value = "")
    private String hjdzXzqhdm;

    @TableField(value = "hjdz_qhnxxdz")
    @ApiModelProperty(value = "")
    private String hjdzQhnxxdz;

    @TableField(value = "rkxxjbdm")
    @ApiModelProperty(value = "")
    private String rkxxjbdm;

    @TableField(value = "rylbdm")
    @ApiModelProperty(value = "")
    private String rylbdm;

    @TableField(value = "jdrysx")
    @ApiModelProperty(value = "")
    private String jdrysx;

    @TableField(value = "xzz_dzbm")
    @ApiModelProperty(value = "")
    private String xzzDzbm;

    @TableField(value = "szzwbh")
    @ApiModelProperty(value = "")
    private String szzwbh;

    @TableField(value = "rydnabh")
    @ApiModelProperty(value = "")
    private String rydnabh;

    @TableField(value = "djr_yhm")
    @ApiModelProperty(value = "")
    private String djrYhm;

    @TableField(value = "djsj")
    @ApiModelProperty(value = "")
    private Date djsj;

    @TableField(value = "djdw_gajgjgdm")
    @ApiModelProperty(value = "")
    private String djdwGajgjgdm;

    @TableField(value = "xzz_zaglxxssjwzrqdm")
    @ApiModelProperty(value = "")
    private String xzzZaglxxssjwzrqdm;

    @TableField(value = "xzz_xzqhdm")
    @ApiModelProperty(value = "")
    private String xzzXzqhdm;

    @TableField(value = "xzz_qhnxxdz")
    @ApiModelProperty(value = "")
    private String xzzQhnxxdz;

    @TableField(value = "djdw_gajgmc")
    @ApiModelProperty(value = "")
    private String djdwGajgmc;

    @TableField(value = "djr_xm")
    @ApiModelProperty(value = "")
    private String djrXm;

    @TableField(value = "djbgsj")
    @ApiModelProperty(value = "")
    private String djbgsj;

    @TableField(value = "sjgsry")
    @ApiModelProperty(value = "")
    private String sjgsry;

    @TableField(value = "sjgsdwdm")
    @ApiModelProperty(value = "")
    private String sjgsdwdm;

    @TableField(value = "xgr_xm")
    @ApiModelProperty(value = "")
    private String xgrXm;

    @TableField(value = "sjgsdwmc")
    @ApiModelProperty(value = "")
    private String sjgsdwmc;

    @TableField(value = "hjdz_xzqhmc")
    @ApiModelProperty(value = "")
    private String hjdzXzqhmc;

    @TableField(value = "xzz_xzqhmc")
    @ApiModelProperty(value = "")
    private String xzzXzqhmc;

    @TableField(value = "sjlydm")
    @ApiModelProperty(value = "")
    private String sjlydm;

    @TableField(value = "rqsj")
    @ApiModelProperty(value = "")
    private String rqsj;

    @TableField(value = "rytbbs")
    @ApiModelProperty(value = "")
    private String rytbbs;

    @TableField(value = "cjfsdm")
    @ApiModelProperty(value = "")
    private String cjfsdm;

    @TableField(value = "tz")
    @ApiModelProperty(value = "")
    private String tz;

    @TableField(value = "scrksj")
    @ApiModelProperty(value = "")
    private Date scrksj;

    @TableField(value = "zxgxsj")
    @ApiModelProperty(value = "")
    private Date zxgxsj;

    @TableField(value = "jlmjdm")
    @ApiModelProperty(value = "")
    private String jlmjdm;

    @TableField(value = "sjjzd_dzbm")
    @ApiModelProperty(value = "")
    private String sjjzdDzbm;

    @TableField(value = "sjjzd_dzmc")
    @ApiModelProperty(value = "")
    private String sjjzdDzmc;

    @TableField(value = "ssdzfw_xxzjbh")
    @ApiModelProperty(value = "")
    private String ssdzfwXxzjbh;

    @TableField(value = "hh")
    @ApiModelProperty(value = "")
    private String hh;

    @TableField(value = "hklxdm")
    @ApiModelProperty(value = "")
    private String hklxdm;

    @TableField(value = "yhzgxdm")
    @ApiModelProperty(value = "")
    private String yhzgxdm;

    @TableField(value = "ryztdm")
    @ApiModelProperty(value = "")
    private String ryztdm;

    @TableField(value = "jzrkbs")
    @ApiModelProperty(value = "")
    private String jzrkbs;

    @TableField(value = "qqzh")
    @ApiModelProperty(value = "")
    private String qqzh;

    @TableField(value = "wxzh")
    @ApiModelProperty(value = "")
    private String wxzh;

    @TableField(value = "hjdz_zaglxxssjwzrqdm")
    @ApiModelProperty(value = "")
    private String hjdzZaglxxssjwzrqdm;

    @TableField(value = "jbrysx")
    @ApiModelProperty(value = "")
    private String jbrysx;

    @TableField(value = "qtsjzh")
    @ApiModelProperty(value = "")
    private String qtsjzh;

    @TableField(value = "hjdz_dzbm")
    @ApiModelProperty(value = "")
    private String hjdzDzbm;

    @TableField(value = "sjly")
    @ApiModelProperty(value = "")
    private Long sjly;

    @TableField(value = "zdryhjsxdm")
    @ApiModelProperty(value = "")
    private String zdryhjsxdm;

    @TableField(value = "image_url")
    @ApiModelProperty(value = "")
    private String imageUrl;

    @TableField(value = "dp_uid")
    @ApiModelProperty(value = "")
    private String dpUid;

    @TableField(value = "dp_type")
    @ApiModelProperty(value = "")
    private String dpType;
}