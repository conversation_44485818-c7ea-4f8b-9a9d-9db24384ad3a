package com.hl.warn.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@ApiModel(description="kgm_archives_member")
@Data
@TableName(value = "kgm_archives_member")
public class KgmArchivesMember {
    @TableField(value = "uuid")
    @ApiModelProperty(value="")
    private String uuid;

    @TableField(value = "group_uid")
    @ApiModelProperty(value="")
    private String groupUid;

    @TableField(value = "create_dept_name")
    @ApiModelProperty(value="")
    private String createDeptName;

    @TableField(value = "create_dept_uid")
    @ApiModelProperty(value="")
    private String createDeptUid;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value="")
    private String createUserName;

    @TableField(value = "create_user_uid")
    @ApiModelProperty(value="")
    private String createUserUid;

    @TableField(value = "create_time")
    @ApiModelProperty(value="")
    private Date createTime;

    @TableField(value = "personel_uid")
    @ApiModelProperty(value="")
    private String personelUid;

    @TableField(value = "member_status")
    @ApiModelProperty(value="")
    private String memberStatus;

    @TableField(value = "ew_deteail_uid")
    @ApiModelProperty(value="")
    private String ewDeteailUid;
}