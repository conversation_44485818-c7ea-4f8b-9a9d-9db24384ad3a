package com.hl.warn.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 人员预警任务结果
 */
@ApiModel(description="人员预警任务结果")
@Data
@TableName(value = "wjhl.wj_ly_task_result_person")
public class WjLyTaskResultPerson {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value="主键id")
    private String id;

    /**
     * 身份证号
     */
    @TableField(value = "gmsfhm")
    @ApiModelProperty(value="身份证号")
    private String gmsfhm;

    /**
     * 姓名
     */
    @TableField(value = "xm")
    @ApiModelProperty(value="姓名")
    private String xm;

    /**
     * 任务名称
     */
    @TableField(value = "task_name")
    @ApiModelProperty(value="任务名称")
    private String taskName;

    /**
     * 任务id
     */
    @TableField(value = "task_id")
    @ApiModelProperty(value="任务id")
    private String taskId;

    /**
     * 分线分数
     */
    @TableField(value = "risk_score")
    @ApiModelProperty(value="分线分数")
    private String riskScore;

    /**
     * 详情
     */
    @TableField(value = "details")
    @ApiModelProperty(value="详情")
    private String details;

    @TableField(value = "create_time")
    private Date createTime;
}