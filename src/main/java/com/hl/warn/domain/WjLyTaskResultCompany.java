package com.hl.warn.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 武进楼宇公司预警结果
 */
@ApiModel(description="武进楼宇公司预警结果")
@Data
@TableName(value = "wjhl.wj_ly_task_result_company")
public class WjLyTaskResultCompany {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 公司名称
     */
    @TableField(value = "company_name")
    @ApiModelProperty(value="公司名称")
    private String companyName;

    /**
     * 统一信用代码
     */
    @TableField(value = "company_code")
    @ApiModelProperty(value="统一信用代码")
    private String companyCode;

    /**
     * 预警任务名称
     */
    @TableField(value = "task_name")
    @ApiModelProperty(value="预警任务名称")
    private String taskName;

    /**
     * 预警任务id
     */
    @TableField(value = "task_id")
    @ApiModelProperty(value="预警任务id")
    private String taskId;

    /**
     * 结果详情
     */
    @TableField(value = "details")
    @ApiModelProperty(value="结果详情")
    private String details;

    /**
     * 风险得分
     */
    @TableField(value = "risk_score")
    @ApiModelProperty(value="风险得分")
    private String riskScore;

    /**
     * 风险预警等级
     */
    @TableField(value = "risk_level")
    @ApiModelProperty(value="风险预警等级")
    private String riskLevel;
}