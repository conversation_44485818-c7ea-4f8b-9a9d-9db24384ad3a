package com.hl.face.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;

import java.util.List;

public class YTApiUtils {

    public static final String HEADER_URL = "http://50.58.189.168:11180/business/api";


    /**
     * 获取登录session id
     *
     * @return
     */
    public static String getSessionId() {
        JSONObject param = new JSONObject()
                .fluentPut("name", "hkdj")
                .fluentPut("password", "3af904feb8ffdf6cf19b8fb7258ec23d");

        HttpResponse execute = HttpUtil.createPost(HEADER_URL + "/login")
                .body(param.toJSONString())
                .timeout(3000)
                .execute();
        JSONObject response = JSONObject.parse(execute.body());
        String sessionId = response.getString("session_id");
        if (StrUtil.isNotBlank(sessionId)) {
            return sessionId;
        } else {
            return "";
        }
    }


    public static List<JSONObject> getDeviceList(String clusterId) {
        HttpResponse response = HttpUtil.createGet(HEADER_URL + "/camera-device")
                .header("session_id", getSessionId())
                .header("target_cluster_id", clusterId)
                .timeout(3000)
                .execute();
        String body = response.body();
        List<JSONObject> cameras = JSONObject.parse(body).getList("cameras", JSONObject.class);
        return cameras;
    }

    public static JSONObject postDevicePictureInfo(String clusterId,
                                                   List<String> cameraIds,
                                                   long startTime) {

        String seeionId = getSessionId();

        JSONObject param = new JSONObject();
        param.put("start", 0);
        param.put("limit", 100);
        param.put("order", new JSONObject()
                .fluentPut("timestamp", 1));
        JSONObject condition = new JSONObject()
                .fluentPut("camera_ids", cameraIds);
        if (startTime != 0) {
            condition.put("start_timestamp", startTime);
        }
        param.put("condition", condition);

        HttpResponse response = HttpUtil.createPost(HEADER_URL + "/condition/query_camera")
                .header("session_id", seeionId)
                .header("target_cluster_id", clusterId)
                .body(param.toJSONString())
                .timeout(1000 * 60)
                .execute();

        String body = response.body();
        JSONObject object = JSONObject.parseObject(body);
        return object;
    }


    public static String downloadPicture(String sessionId,
                                         String clusterId,
                                         String url) {
        HttpResponse execute = HttpUtil.createGet(HEADER_URL + "/storage/image")
                .form("uri_base64", url)
                .header("session_id", sessionId)
                .header("target_cluster_id", clusterId)
                .timeout(1000*30)
                .execute();
        byte[] bytes = execute.bodyBytes();
        String encode = Base64.encode(bytes);
        return encode;
    }


}
