package com.hl.face.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hl.face.domain.vo.YTCompareResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 依图接口比对
 */
@Slf4j
public class CompareYTApi {

    // 依图 登录接口
    private static final String YTLOGIN_URL = "http://50.58.189.169:11180/business/api/login";

    // 依图图片对比接口
    private static final String YTCOMPARE_URL = "http://50.58.189.169:11180/business/api/retrieval_repository";

    // 依图图片获取接口
    private static final String YTPIC_URL = "http://50.58.189.169:11180/storage/v1/image/global?cluster_id=WJGAW_FP_1637857773&uri_base64=";

    //身份信息获取（市局）
    private static final String SFLOGIN_URL = "http://50.56.88.192:7100/jttxkitf/common/auth?ak=1652455815&sk=KHIU78jjopHOp96JLkuM";

    // 身份信息获取接口
    private static final String SFXXGET_URL = "http://50.56.88.192:7100/jttxkitf/ryid/translate";


    private static final String PIC_MARK = "data:image/jpg;base64,";


    public static YTCompareResultVO comparePicture(JSONObject loginResult,
                                                   String pic) {
        YTCompareResultVO ytCompareResultVO = new YTCompareResultVO();
        pic = pic.replaceAll(PIC_MARK, "");
        try {
            getYtCompareResultVO(pic, loginResult, ytCompareResultVO);
            return ytCompareResultVO;
        } catch (Exception e) {
            log.error("比对图片失败");
            log.error(e.toString());
        }
        return ytCompareResultVO;
    }


    public static YTCompareResultVO comparePicture(String pic) {
        YTCompareResultVO ytCompareResultVO = new YTCompareResultVO();
        pic = pic.replaceAll(PIC_MARK, "");
        try {
            JSONObject loginResult = getSessionId();
            getYtCompareResultVO(pic, loginResult, ytCompareResultVO);
            return ytCompareResultVO;
        } catch (Exception e) {
            log.error("比对图片失败");
            log.error(e.toString());
        }
        return ytCompareResultVO;
    }

    public static void getYtCompareResultVO(String pic, JSONObject loginResult, YTCompareResultVO ytCompareResultVO) {
        if ("0".equals(loginResult.getStr("rtn"))) {
            // 登录成功
            String sessionId = loginResult.getStr("session_id");
            String paramStr = "{\"extra_fields\":[\"ZDRID\"],\"limit\":1,\"order\":{\"similarity\":-1},\"retrieval\":{\"repository_ids\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,39,40,41,43,44,45,46,47,48,49,50,51,52,53,54,56,57,58,60,61,62,63,64,65,66,67],\"picture_image_content_base64\":\"\",\"threshold\":0.0,\"topk\":100},\"start\":0}";
            JSONObject param = JSONUtil.parseObj(paramStr);
            JSONObject retrieval = param.getJSONObject("retrieval");
            retrieval.set("picture_image_content_base64", pic);
            param.set("retrieval", retrieval);
            
            String result;
            try {
                result = HttpRequest.post(YTCOMPARE_URL)
                        .header(Header.CONTENT_TYPE, "application/json")
                        .header("session_id", sessionId)
                        .body(param.toJSONString(0))
                        .timeout(10000)
                        .execute().body();
            } catch (Exception e) {
                log.error("调用依图比对接口失败", e);
                return;
            }
            
            ytCompareResultVO.setCompareResult(result);
            JSONObject resultObj = JSONUtil.parseObj(result);
            if ("0".equals(resultObj.getStr("rtn"))) {
                JSONArray results = resultObj.getJSONArray("results");
                if (results == null || results.isEmpty()) {
                    return;
                }
                
                JSONObject resultsJSONObject = results.getJSONObject(0);
                JSONObject extraMeta = resultsJSONObject.getJSONObject("extra_meta");
                String similarity = resultsJSONObject.getStr("similarity");
                ytCompareResultVO.setSimilarity(similarity);
                
                if (extraMeta.isEmpty()) {
                    // 第一种情况 extraMeta 返回内容已经存在身份信息
                    try {
                        String name = resultsJSONObject.getStr("name");
                        String personId = resultsJSONObject.getStr("person_id");
                        String pictureUri = resultsJSONObject.getStr("picture_uri");
                        String encode = Base64.encode(pictureUri);
                        byte[] bytes = HttpUtil.downloadBytes(YTPIC_URL + encode);
                        String realPic = PIC_MARK + Base64.encode(bytes);
                        ytCompareResultVO.setName(name);
                        ytCompareResultVO.setIdCard(personId);
                        ytCompareResultVO.setRealPic(realPic);
                    } catch (Exception e) {
                        log.error("获取人员基本信息失败", e);
                    }
                    return;
                } else {
                    // 第二种情况 返回人员id 需要主动调用市局获取身份信息接口
                    String zdrid = extraMeta.getStr("ZDRID");
                    if (StringUtils.isBlank(zdrid)) {
                        return;
                    }
                    
                    String accessToken = null;
                    try {
                        // 身份信息获取接口登录
                        String sfLogin = HttpRequest.get(SFLOGIN_URL)
                                .timeout(5000)
                                .execute().body();
                        JSONObject sfLoginObj = JSONUtil.parseObj(sfLogin);
                        if ("200".equals(sfLoginObj.getStr("code"))) {
                            JSONObject sfLoginData = sfLoginObj.getJSONObject("data");
                            accessToken = sfLoginData.getStr("access_token");
                        }
                    } catch (Exception e) {
                        log.error("市局身份信息获取接口登录失败");
                        return;
                    }
                    
                    if (StringUtils.isBlank(accessToken)) {
                        return;
                    }
                    
                    try {
                        String sfResult = HttpRequest.post(SFXXGET_URL)
                                .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                                .header("access_token", accessToken)
                                .form("ryid", zdrid)
                                .timeout(5000)
                                .execute().body();
                        JSONObject sfResultObj = JSONUtil.parseObj(sfResult);
                        JSONObject data = sfResultObj.getJSONObject("data");
                        if (data != null) {
                            String idnumber = data.getStr("idnumber");
                            String name = data.getStr("name"); 
                            String faceUrl = data.getStr("face_url");
                            ytCompareResultVO.setIdCard(idnumber);
                            ytCompareResultVO.setName(name);
                            if (StringUtils.isNotBlank(faceUrl)) {
                                try {
                                    byte[] bytes = HttpUtil.downloadBytes(faceUrl);
                                    String realPic = PIC_MARK + Base64.encode(bytes);
                                    ytCompareResultVO.setRealPic(realPic);
                                } catch (Exception e) {
                                    log.error("下载人脸图片失败", e);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("获取身份信息失败", e);
                    }
                }
            }
        }

    }

    public static JSONObject getSessionId() {
        String loginUser = "{\"name\":\"hkdj\",\"password\":\"3af904feb8ffdf6cf19b8fb7258ec23d\"}";
        String body = HttpRequest.post(YTLOGIN_URL)
                .body(loginUser)
                .timeout(2000)
                .execute().body();
        JSONObject loginResult = JSONUtil.parseObj(body);
        return loginResult;
    }


}
