package com.hl.face.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.building.domain.WjBuildingInfo;
import com.hl.building.domain.dto.WjFaceCameraInfoPageReq;
import com.hl.building.domain.dto.WjBuildingCameraReq;
import com.hl.building.domain.dto.WjFaceCameraPageReq;
import com.hl.building.domain.vo.WjBuildingCameraRespVO;
import com.hl.building.mapper.WjBuildingInfoMapper;
import com.hl.face.domain.WjFaceCameraInfo;
import com.hl.face.mapper.WjFaceCameraInfoMapper;
import com.hl.face.service.WjFaceCameraInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WjFaceCameraInfoServiceImpl extends ServiceImpl<WjFaceCameraInfoMapper, WjFaceCameraInfo> implements WjFaceCameraInfoService {

    private final WjBuildingInfoMapper  wjBuildingInfoMapper;

    @Override
    public List<String> getCameraIds() {

        List<WjFaceCameraInfo> list = list(Wrappers.<WjFaceCameraInfo>lambdaQuery()
                .eq(WjFaceCameraInfo::getEnabled, 1));

        return list.stream().map(WjFaceCameraInfo::getCameraId).collect(Collectors.toList());
    }


    @Override
    public Page<WjBuildingCameraRespVO> pageBuildingCameraInfo(WjFaceCameraInfoPageReq req) {
        Page<WjBuildingInfo> buildingInfoPage = wjBuildingInfoMapper.selectPage(Page.of(req.getPage(), req.getLimit()),
                Wrappers.<WjBuildingInfo>lambdaQuery()
                        .like(StrUtil.isNotBlank(req.getBuildingName()), WjBuildingInfo::getBuildName, req.getBuildingName())
                        .orderByDesc(WjBuildingInfo::getBuildId));
        if (!buildingInfoPage.getRecords().isEmpty()){
            List<WjBuildingInfo> records = buildingInfoPage.getRecords();
            
            // 收集所有设备ID
            List<String> allDeviceIds = records.stream()
                    .map(WjBuildingInfo::getDeviceInfo)
                    .filter(deviceInfo -> deviceInfo != null && !deviceInfo.isEmpty())
                    .flatMap(List::stream)
                    .distinct()
                    .collect(Collectors.toList());
            
            // 一次性查询所有设备信息
            List<WjFaceCameraInfo> allCameraInfoList = new ArrayList<>();
            if (!allDeviceIds.isEmpty()) {
                allCameraInfoList = list(Wrappers.<WjFaceCameraInfo>lambdaQuery()
                        .in(WjFaceCameraInfo::getId, allDeviceIds));
            }
            
            // 构建设备ID到设备信息的映射
            Map<String, WjFaceCameraInfo> cameraInfoMap = allCameraInfoList.stream()
                    .collect(Collectors.toMap(WjFaceCameraInfo::getId, camera -> camera));
            
            // 构建返回结果
            List<WjBuildingCameraRespVO> resultList = new ArrayList<>();
            for (WjBuildingInfo record : records) {
                List<String> deviceInfo = record.getDeviceInfo();
                WjBuildingCameraRespVO respVO = new WjBuildingCameraRespVO();
                respVO.setBuildId(record.getBuildId());
                respVO.setBuildName(record.getBuildName());
                respVO.setDeviceInfo(deviceInfo);
                
                if (deviceInfo != null && !deviceInfo.isEmpty()) {
                    List<WjFaceCameraInfo> cameraInfoList = deviceInfo.stream()
                            .map(cameraInfoMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    respVO.setDeviceInfoList(cameraInfoList);
                }
                resultList.add(respVO);
            }

            Page<WjBuildingCameraRespVO> resultPage = new Page<>(buildingInfoPage.getCurrent(), buildingInfoPage.getSize(), buildingInfoPage.getTotal());
            resultPage.setRecords(resultList);
            return resultPage;
        }
        return new Page<>();
    }

    @Override
    public Boolean updateDeviceRelation(WjBuildingCameraReq req) {
        WjBuildingInfo buildingInfo = wjBuildingInfoMapper.selectById(req.getBuildId());
        if (buildingInfo != null) {
            buildingInfo.setDeviceInfo(req.getDeviceInfo());
            return wjBuildingInfoMapper.updateById(buildingInfo) > 0;
        }
        return false;
    }

    @Override
    public Page<WjFaceCameraInfo> pageCameraList(WjFaceCameraPageReq req) {
        return page(Page.of(req.getPage(), req.getLimit()),
                Wrappers.<WjFaceCameraInfo>lambdaQuery()
                        .like(StrUtil.isNotBlank(req.getName()), WjFaceCameraInfo::getName, req.getName()));
    }
}
