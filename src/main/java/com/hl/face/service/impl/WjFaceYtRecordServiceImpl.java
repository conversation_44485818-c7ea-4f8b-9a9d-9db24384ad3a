package com.hl.face.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.face.domain.WjFacePicInfo;
import com.hl.face.domain.WjFaceYtRecord;
import com.hl.face.domain.vo.YTCompareResultVO;
import com.hl.face.mapper.WjFaceYtRecordMapper;
import com.hl.face.service.WjFacePicInfoService;
import com.hl.face.service.WjFaceYtRecordService;
import com.hl.face.utils.CompareYTApi;
import com.hl.face.utils.YTApiUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class WjFaceYtRecordServiceImpl extends ServiceImpl<WjFaceYtRecordMapper, WjFaceYtRecord> implements WjFaceYtRecordService {

    private final WjFacePicInfoService wjFacePicInfoService;

    @Value("${face-compare.compare-picture.enable}")
    private Boolean isCompare;

    @Value("${face-compare.compare-picture.cluster-id}")
    private String clusterId;

    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        // 创建线程池，核心线程数20，最大线程数20，使用有界队列
        executorService = new ThreadPoolExecutor(
            20,
            40,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1000),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @PreDestroy
    public void destroy() {
        if (executorService != null) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    @Override
//    @Scheduled(cron = "0 0/2 * * * ?")
    @JobExecutor(name = "comparePicture")
    public void comparePicture() {
        if (!isCompare) {
            log.debug("未开启人脸比对");
            return;
        }

        // 查找没有比对的照片
        List<WjFacePicInfo> list = wjFacePicInfoService.list(Wrappers.<WjFacePicInfo>lambdaQuery()
                .eq(WjFacePicInfo::getStatus, 0)
                .orderByAsc(WjFacePicInfo::getTimestamp)      
                .last("limit 1000"));

        if (list.isEmpty()) {
            return;
        }

        // 获取会话信息
        String sessionId = YTApiUtils.getSessionId();
        JSONObject loginResult = CompareYTApi.getSessionId();

        // 使用CountDownLatch等待所有任务完成
        CountDownLatch latch = new CountDownLatch(list.size());
        
        // 并行处理每张照片
        for (WjFacePicInfo picInfo : list) {
            executorService.submit(() -> {
                try {
                    processSinglePicture(picInfo, sessionId, loginResult);
                } catch (Exception e) {
                    log.error("处理图片失败，图片ID: " + picInfo.getId(), e);
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            // 等待所有任务完成或超时
            latch.await(5, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("等待任务完成时被中断", e);
            Thread.currentThread().interrupt();
        }
    }

    private void processSinglePicture(WjFacePicInfo picInfo, String sessionId, JSONObject loginResult) {
        try {
            log.info("开始处理图片ID:{}", picInfo.getId());
            
            picInfo.setStatus(-1);
            wjFacePicInfoService.updateById(picInfo);
            log.info("图片ID:{} 状态已更新为处理中", picInfo.getId());

            String globalFaceImageUri = picInfo.getGlobalFaceImageUri();
            log.info("图片ID:{} 开始下载人脸图片", picInfo.getId());
            String downloadedPicture = YTApiUtils. downloadPicture(sessionId, clusterId, globalFaceImageUri);
            
            if (StringUtils.isBlank(downloadedPicture)) {
                log.warn("图片ID:{} 下载人脸图片失败", picInfo.getId());
                return;
            }
            log.info("图片ID:{} 人脸图片下载成功", picInfo.getId());

            log.info("图片ID:{} 开始进行人脸比对", picInfo.getId());
            YTCompareResultVO resultVO = CompareYTApi.comparePicture(loginResult, downloadedPicture);
            log.info("图片ID:{} 人脸比对完成,比对结果:{}", picInfo.getId(), resultVO.getSimilarity());

            WjFaceYtRecord wjFaceYtRecord = new WjFaceYtRecord();
            wjFaceYtRecord.setPicId(picInfo.getId());
            wjFaceYtRecord.setFaceImageId(picInfo.getFaceImageId());
            wjFaceYtRecord.setName(resultVO.getName());
            wjFaceYtRecord.setIdCard(resultVO.getIdCard());
            wjFaceYtRecord.setCreateTime(new Date());
            wjFaceYtRecord.setAppearTime(DateUtil.date(picInfo.getTimestamp() * 1000));
            wjFaceYtRecord.setSimilarity(resultVO.getSimilarity());
            wjFaceYtRecord.setCompareResult(resultVO.getCompareResult());
            
            this.save(wjFaceYtRecord);
            log.info("图片ID:{} 比对记录保存成功", picInfo.getId());
        } catch (Exception e) {
            log.error("处理单张图片失败", e);
        }
    }
}
