package com.hl.face.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.face.domain.WjFacePicInfo;
import com.hl.face.domain.vo.YtCameraVO;
import com.hl.face.mapper.WjFacePicInfoMapper;
import com.hl.face.service.WjFaceCameraInfoService;
import com.hl.face.service.WjFacePicInfoService;
import com.hl.face.utils.YTApiUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class WjFacePicInfoServiceImpl extends ServiceImpl<WjFacePicInfoMapper, WjFacePicInfo> implements WjFacePicInfoService {

    @Value("${face-compare.get-picture.enable}")
    private Boolean getPictureEnable;

    @Value("${face-compare.get-picture.cluster-id}")
    private String clusterId;

    private final WjFaceCameraInfoService wjFaceCameraInfoService;

    @Override
//    @Scheduled(cron = "0 0/2 * * * ?")
    @JobExecutor(name = "getPicInfo")
    public void getPicInfo() {
        if (!getPictureEnable) {
            log.debug("未开启获取照片信息");
            return;
        }
        // 获取要获取信息的设备id
        List<String> cameraIds = wjFaceCameraInfoService.getCameraIds();
        if (cameraIds.isEmpty()) {
            log.debug("未获取到设备id");
            return;
        }

        // 当前时间的时间戳
        long defaultStartTime = System.currentTimeMillis() / 1000L;

        // 按设备分别获取数据
        for (String cameraId : cameraIds) {
            try {
                // 获取该设备最新一条照片的时间戳
                long startTime = getLatestTimestampByCameraId(cameraId);
                if (startTime == 0L) {
                    startTime = defaultStartTime; // 如果是新设备，从3月1日开始获取
                }

                // 获取单个设备的数据
                List<YtCameraVO> allCameraVOList = new ArrayList<>();
                int pageNum = 1;
                int maxLoops = 50;
                int loopCount = 0;
                while (loopCount < maxLoops) {
                    log.info("正在获取设备{}的第{}页数据,当前时间戳:{}", cameraId, pageNum, startTime);
                    JSONObject pictureInfo = YTApiUtils.postDevicePictureInfo(clusterId, Collections.singletonList(cameraId), startTime);
                    List<YtCameraVO> cameraVOList = pictureInfo.getList("results", YtCameraVO.class);
                    if (ObjectUtil.isNull(cameraVOList)) {
                        log.info("设备{}第{}页数据为空,结束获取", cameraId, pageNum);
                        break;
                    }

                    allCameraVOList.addAll(cameraVOList);
                    log.info("设备{}第{}页获取到{}条数据,累计{}条", cameraId, pageNum, cameraVOList.size(), allCameraVOList.size());

                    // 如果返回数据不足100条,说明已经获取完所有数据
                    if (cameraVOList.size() < 100) {
                        log.info("设备{}数据获取完毕,共{}页{}条数据", cameraId, pageNum, allCameraVOList.size());
                        break;
                    }

                    // 更新startTime为最后一条记录的时间戳
                    startTime = cameraVOList.get(cameraVOList.size() - 1).getTimestamp();
                    pageNum++;
                    loopCount++;
                }

                if (!allCameraVOList.isEmpty()) {
                    log.info("获取到设备{}的{}张照片", cameraId, allCameraVOList.size());
                }

                // 保存照片信息
                for (YtCameraVO cameraVO : allCameraVOList) {
                    String faceImageId = cameraVO.getFaceImageId();
                    // 检验有没有保存过
                    WjFacePicInfo one = getOne(Wrappers.<WjFacePicInfo>lambdaQuery()
                            .eq(WjFacePicInfo::getFaceImageId, faceImageId)
                    );
                    if (one != null) {
                        continue;
                    }
                    String globalPictureUri = cameraVO.getGlobalPictureUri();
                    String globalFaceImageUri = cameraVO.getGlobalFaceImageUri();
                    long timestamp = cameraVO.getTimestamp();
                    WjFacePicInfo wjFacePicInfo = new WjFacePicInfo();
                    wjFacePicInfo.setCameraId(cameraId);
                    wjFacePicInfo.setFaceImageId(faceImageId);
                    wjFacePicInfo.setGlobalPictureUri(globalPictureUri);
                    wjFacePicInfo.setGlobalFaceImageUri(globalFaceImageUri);
                    wjFacePicInfo.setStatus(0);
                    wjFacePicInfo.setTimestamp(timestamp);
                    save(wjFacePicInfo);
                }
            } catch (Exception e) {
                log.error("获取设备{}的照片信息失败", cameraId, e);
            }
        }
    }

    /**
     * 获取指定设备最新一条照片的时间戳
     *
     * @param cameraId 设备ID
     * @return 时间戳
     */
    private long getLatestTimestampByCameraId(String cameraId) {
        WjFacePicInfo one = getOne(Wrappers.<WjFacePicInfo>lambdaQuery()
                .eq(WjFacePicInfo::getCameraId, cameraId)
                .last(" limit 1")
                .orderByDesc(WjFacePicInfo::getTimestamp));
        if (one != null) {
            return one.getTimestamp();
        }
        return 0L;
    }
}
