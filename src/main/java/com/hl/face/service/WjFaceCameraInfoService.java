package com.hl.face.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.building.domain.dto.WjFaceCameraInfoPageReq;
import com.hl.building.domain.dto.WjBuildingCameraReq;
import com.hl.building.domain.dto.WjFaceCameraPageReq;
import com.hl.building.domain.vo.WjBuildingCameraRespVO;
import com.hl.face.domain.WjFaceCameraInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface WjFaceCameraInfoService extends IService<WjFaceCameraInfo>{

    List<String> getCameraIds();

    Page<WjBuildingCameraRespVO> pageBuildingCameraInfo(WjFaceCameraInfoPageReq req);
    
    Boolean updateDeviceRelation(WjBuildingCameraReq req);
    
    Page<WjFaceCameraInfo> pageCameraList(WjFaceCameraPageReq req);
}
