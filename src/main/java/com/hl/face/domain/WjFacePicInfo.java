package com.hl.face.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description="wj_face_pic_info")
@Data
@TableName(value = "wjhl.wj_face_pic_info")
public class WjFacePicInfo {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value="id")
    private String id;

    /**
     * 依图相机id
     */
    @TableField(value = "camera_id")
    @ApiModelProperty(value="依图相机id")
    private String cameraId;

    /**
     * 时间戳
     */
    @TableField(value = "\"timestamp\"")
    @ApiModelProperty(value="时间戳")
    private Long timestamp;

    /**
     * 人脸照片id
     */
    @TableField(value = "face_image_id")
    @ApiModelProperty(value="人脸照片id")
    private String faceImageId;

    /**
     * 全局图片uri
     */
    @TableField(value = "global_picture_uri")
    @ApiModelProperty(value="全局图片uri")
    private String globalPictureUri;

    /**
     * 人脸照片
     */
    @TableField(value = "global_face_image_uri")
    @ApiModelProperty(value="人脸照片")
    private String globalFaceImageUri;

    /**
     * 状态
     */
    @TableField(value = "\"status\"")
    @ApiModelProperty(value="状态")
    private Integer status;
}