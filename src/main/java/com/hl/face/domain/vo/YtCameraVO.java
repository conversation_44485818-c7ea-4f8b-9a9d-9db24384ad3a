package com.hl.face.domain.vo;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class YtCameraVO {
    @JsonProperty(value = "rec_age_range")
    private int recAgeRange;
    @JsonProperty(value = "timestamp_begin")
    private long timestampBegin;

    @JsonProperty(value = "picture_uri")
    private String pictureUri;

    @JsonProperty(value = "rec_mask")
    private int recMask;
    @JsonProperty(value = "rec_hat_color")
    private int recHatColor;
    @JsonProperty(value = "rec_beard")
    private int recBeard;
    @JsonProperty(value = "rec_fs_color")
    private int recFsColor;
    @JsonProperty(value = "global_picture_uri")
    private String globalPictureUri;
    @JsonProperty(value = "rec_hat")
    private int recHat;
    @JsonProperty(value = "rec_is_calling")
    private int recIsCalling;
    @JsonProperty(value = "timestamp_end")
    private long timestampEnd;
    @JsonProperty(value = "timestamp")
    private long timestamp;
    @JsonProperty(value = "rec_hairstyle")
    private int recHairstyle;
    @JsonProperty(value = "rec_smiley_face")
    private int recSmileyFace;
    @JsonProperty(value = "rec_face_id")
    private String recFaceId;
    @JsonProperty(value = "camera_id")
    private String cameraId;
    @JsonProperty(value = "rec_glasses")
    private int recGlasses;
    @JsonProperty(value = "rec_sunglass")
    private int recSunglass;
    @JsonProperty(value = "rec_ur")
    private int recUr;
    @JsonProperty(value = "rec_beard_style")
    private int recBeardStyle;
    @JsonProperty(value = "rec_gender")
    private int recGender;
    @JsonProperty(value = "face_image_id")
    private String faceImageId;
    @JsonProperty(value = "face_image_uri")
    private String faceImageUri;

    @JsonProperty(value = "global_face_image_uri")
    private String globalFaceImageUri;
}
