package com.hl.face.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人脸比对设备
 */
@ApiModel(description="人脸比对设备")
@Data
@TableName(value = "wjhl.wj_face_camera_info")
public class WjFaceCameraInfo {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private String id;

    /**
     * 依图相机id
     */
    @TableField(value = "camera_id")
    @ApiModelProperty(value="依图相机id")
    private String cameraId;

    /**
     * 点位名称
     */
    @TableField(value = "\"name\"")
    @ApiModelProperty(value="点位名称")
    private String name;

    /**
     * 集群id
     */
    @TableField(value = "cluster_id")
    @ApiModelProperty(value="集群id")
    private String clusterId;

    /**
     * 设备状态
     */
    @TableField(value = "\"status\"")
    @ApiModelProperty(value="设备状态")
    private Integer status;

    /**
     * 国标码
     */
    @TableField(value = "inter_code")
    @ApiModelProperty(value="国标码")
    private String interCode;

    /**
     * 是否比对
     */
    @TableField(value = "enabled")
    @ApiModelProperty(value="是否比对")
    private Integer enabled;
}