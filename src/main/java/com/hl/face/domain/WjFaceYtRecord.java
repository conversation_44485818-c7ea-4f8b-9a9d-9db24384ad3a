package com.hl.face.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 人脸依图比对结果
 */
@ApiModel(description="人脸依图比对结果")
@Data
@TableName(value = "wjhl.wj_face_yt_record")
public class WjFaceYtRecord {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value="主键")
    private String id;

    /**
     * 照片id
     */
    @TableField(value = "pic_id")
    @ApiModelProperty(value="照片id")
    private String picId;

    /**
     * 依图图片id
     */
    @TableField(value = "face_image_id")
    @ApiModelProperty(value="依图图片id")
    private String faceImageId;

    /**
     * 人员姓名
     */
    @TableField(value = "\"name\"")
    @ApiModelProperty(value="人员姓名")
    private String name;

    /**
     * 证件号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value="证件号")
    private String idCard;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    @TableField(value = "appear_time")
    @ApiModelProperty(value="出现时间")
    private Date appearTime;


    private String compareResult;

    private String similarity;
}