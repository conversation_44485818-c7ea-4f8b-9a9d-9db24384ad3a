package com.hl.ai.controller;

import com.hl.ai.dto.SjxxQueryDTO;
import com.hl.ai.service.AiToolService;
import com.hl.common.domain.R;
import com.hl.jq.domain.WjscJqSjxx;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/tool")
@RequiredArgsConstructor
@Api(tags = "楼宇任务组件")
public class AiController {

    private final AiToolService aiToolService;

    @PostMapping("/querySjxx")
    @ApiOperation(value = "查询涉警信息")
    public R<List<WjscJqSjxx>> querySjxx(@RequestBody SjxxQueryDTO queryDTO) {
        List<WjscJqSjxx> sjxx = aiToolService.sjxx(queryDTO);
        return R.ok(sjxx);
    }

}
