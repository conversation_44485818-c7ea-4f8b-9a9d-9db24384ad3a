package com.hl.ai.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hl.ai.dto.SjxxQueryDTO;
import com.hl.jq.domain.WjscJqSjxx;
import com.hl.jq.mapper.WjscJqSjxxMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class AiToolService {

    private final WjscJqSjxxMapper wjscJqSjxxMapper;

    public List<WjscJqSjxx> sjxx(SjxxQueryDTO queryDTO) {
        String idCard = queryDTO.getIdCard();
        List<WjscJqSjxx> wjscJqSjxxes = wjscJqSjxxMapper.selectList(Wrappers.<WjscJqSjxx>lambdaQuery()
                .eq(WjscJqSjxx::getGmsfhm, idCard));
        return wjscJqSjxxes;

    }
}
