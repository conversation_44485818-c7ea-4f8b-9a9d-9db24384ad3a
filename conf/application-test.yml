# 数据源配置
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
          master:
            driverClassName: org.postgresql.Driver
            url: **********************************************************************************************************************************************
            username: hl
            password: hl123
          datasource1:
            driverClassName: com.mysql.cj.jdbc.Driver
            url: **************************************************************************************************************************************************
            username: hl
            password: hl1234!@#$
      druid:
        initialSize: 5
        # 最小连接池数量
        minIdle: 10
        # 最大连接池数量
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置连接超时时间
        connectTimeout: 30000
        # 配置网络超时时间
        socketTimeout: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        # 配置一个连接在池中最大生存的时间，单位是毫秒
        maxEvictableIdleTimeMillis: 900000
        # 配置检测连接是否有效
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
